{"port": "PORT", "authentication": {"secret": "DMXVvkbodjESSJNsDFLLCicK0POMpbQklZKQgXsa3RNWAX5xvIm4e6no4KvyOYZ783n6M6ojfkHFJczMqbF5izBH5xo9GYSOhAcu11s8tp7WU5K1HipWwsRlmiVgiSuH", "jwtOptions": {"audience": "https://yourdomain.com", "expiresIn": "7d"}}, "smtp": {"host": "smtp.sendgrid.net", "port": 465, "secure": true, "auth": {"user": "apikey", "pass": "*********************************************************************"}, "sender": "CDAS <<EMAIL>>"}, "sms": {"account": {"username": "SMS_USR", "password": "SMS_PWD"}, "from": "CDAS", "host": "https://mx.fortdigital.net/http/send-message?"}, "mongodb": "mongodb+srv://dbUserProd:<EMAIL>/cdasPSA?retryWrites=true&w=majority", "redis": "rediss://:<EMAIL>/0", "serviceAdaptorUrl": "http://internal-psa-prod-internal-alb-**********.ap-southeast-1.elb.amazonaws.com:9000/messages/psa", "storageBucket": "cdas-ctr", "logger": {"level": "INFO"}, "maxDayMsgHistory": "30", "agenda": {"msgHistories": "0 5 * * *", "timezone": "Asia/Singapore", "jobCancellation": "0 4 * * *"}, "sb": {"baseUrl": "http://***********:4040", "allowedIPs": ["***********", "***********"]}, "optetruckUrl": "https://api.portnet.com/pc/optetruck/ctrUpdateMovement"}