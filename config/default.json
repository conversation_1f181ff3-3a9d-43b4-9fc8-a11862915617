{"host": "localhost", "port": 3030, "public": "../public", "import": "../import", "attachment": "../attachment", "paginate": {"default": 200, "max": 999999}, "authentication": {"entity": "user", "service": "users", "secret": "ZEkCYNMGbqpWSdwUA4ZfoxIjc04=", "authStrategies": ["jwt", "local"], "maximumSessionLength": "30d", "jwtOptions": {"header": {"typ": "access"}, "audience": "https://yourdomain.com", "issuer": "feathers", "algorithm": "HS256", "expiresIn": "2d"}, "local": {"usernameField": "\\username", "passwordField": "password"}}, "otpValidity": 180, "mongodb": "mongodb+srv://user-21:<EMAIL>/cdasPSA?retryWrites=true&w=majority", "redis": "redis://:UUYijo9CiZgaxhSA2M8Xs8@localhost:6379/0", "smtp": {"host": "smtp.ethereal.email", "port": 587, "auth": {"user": "<EMAIL>", "pass": "qy7xX6V1B6M5PbyDf4"}, "sender": "CDAS Development <<EMAIL>>"}, "template": {"email": "../src/templates/email"}, "serviceAdaptorUrl": "http://internal-psa-uat-internal-alb-1334015383.ap-southeast-1.elb.amazonaws.com:9000/messages/psa", "cdi": {"baseUrl": "https://ctr.integrate.afa-cdi.com/api/v1", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjA2YTcwMzQxLWJiZDItNGNhYi04YjQ3LTQ3YmQ0NjhmMzU4MyIsImlhdCI6MTYxMzkwMjk1NH0.6MJEJc7a1C9TY4Gwp6In5yapbkMzmdvaHXCJc8b87c8"}, "storageBucket": "cdas-ctr-uat", "logger": {"fileName": "/.pm2/logs/PSA-out.log", "level": "LOGLEVEL"}, "maxDayMsgHistory": "30", "agenda": {"msgHistories": "0 11 * * *", "timezone": "Asia/Singapore", "compactCollection": "0 3 * * 0", "dataArchive": "0 1 * * 0", "jobCancellation": "0 8 * * *"}, "vehicleKeyName": "vehicle-api-key", "resetPassword": {"notification": "sms"}, "sb": {"apiKeyName": "sb-api-key", "baseUrl": "http://localhost:3031", "allowedIPs": ["127.0.0.1"]}, "optetruckUrl": "https://api.pntestbox.com/pc/optetruck/ctrUpdateMovement"}