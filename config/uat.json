{"port": "PORT", "authentication": {"secret": "i9BYWr1Ym9oOzwJQhEaZmnttC2yo2VALFXAOD5RlluURXKWMFPJ8um7oPx0PHiykpkfa4W2aPCbrCeJ34o7CTXFvbGW6nwKn5YybjcxPPHcYN9MxsC2cHzkb3fNNO783"}, "smtp": {"host": "smtp.sendgrid.net", "port": 465, "secure": true, "auth": {"user": "apikey", "pass": "*********************************************************************"}, "sender": "CDAS Development <<EMAIL>>"}, "mongodb": "mongodb+srv://dbUser:<EMAIL>/cdasPSA", "redis": "rediss://:<EMAIL>/0", "maxDayMsgHistory": "30", "agenda": {"msgHistories": "0 11 * * *", "timezone": "Asia/Singapore", "jobCancellation": "0 8 * * *"}, "sb": {"baseUrl": "http://************:4040", "allowedIPs": ["************"]}}