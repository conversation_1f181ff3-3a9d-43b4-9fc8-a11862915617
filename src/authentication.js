const { LocalStrategy } = require('@feathersjs/authentication-local')
const { expressOauth } = require('@feathersjs/authentication-oauth')
const CustomAuthenticationService = require('./customAuthenticationSvc')
const CustomJWTStrategy = require('./customJWTStrategy')
const hooks = require('./authentication.hooks')

module.exports = app => {
  const authentication = new CustomAuthenticationService(app)

  authentication.register('jwt', new CustomJWTStrategy())
  authentication.register('local', new LocalStrategy())

  app.use('/authentication', authentication)

  app.service('authentication').hooks(hooks)

  app.configure(expressOauth())
}
