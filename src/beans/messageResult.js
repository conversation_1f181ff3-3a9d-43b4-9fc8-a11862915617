const BaseBean = require('./baseBean')
class MessageResult extends BaseBean {
  constructor (_id, content, senderId, sender, isSent,
    conversationId, type, createdAt, recipientStatus, flagFrom, vehicleDrivers) {
    super(_id)
    this.content = content
    this.senderId = senderId
    this.sender = sender
    this.isSent = isSent
    this.conversationId = conversationId
    this.type = type
    this.createdAt = createdAt
    this.recipientStatus = recipientStatus
    this.flagFrom = flagFrom
    this.vehicleDrivers = vehicleDrivers
  }
}

module.exports = MessageResult
