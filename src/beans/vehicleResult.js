const BaseBean = require('./baseBean')
class VehicleResult extends BaseBean {
  constructor (_id, status, portNet, vehicleNo, group,
    company, driver, createdBy, createdAt, updatedAt, __v, license, conversationsIds, latestMessageDate, hasNewMessage) {
    super(_id)
    this.status = status
    this.portNet = portNet
    this.vehicleNo = vehicleNo
    this.group = group
    this.company = company
    this.driver = driver
    this.createdBy = createdBy
    this.createdAt = createdAt
    this.updatedAt = updatedAt
    this.__v = __v
    this.license = license
    this.conversationsIds = conversationsIds
    this.latestMessageDate = latestMessageDate
    this.hasNewMessage = hasNewMessage
  }
}

module.exports = VehicleResult
