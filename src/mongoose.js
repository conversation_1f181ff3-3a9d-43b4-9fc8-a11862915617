const mongoose = require('mongoose')
const logger = require('@hooks/logger')

mongoose.set('strictQuery', true)
mongoose.set('strictPopulate', false)
mongoose.set('debug', process.env.NODE_ENV !== 'production')

module.exports = function (app) {
  mongoose.connect(
    app.get('mongodb'),
    {
      useNewUrlParser: true,
      useUnifiedTopology: true
    }
  ).catch(err => {
    logger.error(err)
    process.exit(1)
  })

  mongoose.Promise = global.Promise

  app.set('mongooseClient', mongoose)
}
