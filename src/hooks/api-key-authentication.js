const { NotAuthenticated } = require('@feathersjs/errors')

module.exports = () => async (ctx) => {
  const { params } = ctx
  const { data } = await ctx.app.service('api-keys').find({
    query: {
      status: 'active'
    }
  })

  let found = false

  for (const [key, value] of Object.entries(params.headers)) {
    const header = data.find(d => d.headerKey === key && d.accessToken === value)

    if (header) {
      found = true
      break
    }
  }

  if (!found) throw new NotAuthenticated('API key is invalid')

  return ctx
}
