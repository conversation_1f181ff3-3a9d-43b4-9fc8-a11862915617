// Use this hook to manipulate incoming or outgoing data.
// For more information on hooks see: http://docs.feathersjs.com/api/hooks.html
const feathers = require('@feathersjs/feathers')
const configuration = require('@feathersjs/configuration')
const log4js = require('log4js')

const app = feathers().configure(configuration())
const filename = `${process.env.HOME}${app.get('logger').fileName || '/cdas-psa-business-layer.log'}`
const level = app.get('logger').level || 'DEBUG'

log4js.configure({
  appenders: {
    daterollinglogger: {
      type: 'dateFile',
      filename,
      pattern: '.yyyy-MM-dd',
      alwaysIncludePattern: false,
      compress: true,
      numBackups: 14,
      layout: {
        type: 'pattern',
        pattern: '%d %p [%z] [%f{1}:%l] %m'
      }
    }
  },
  categories: {
    default: {
      appenders: ['daterollinglogger'],
      level,
      enableCallStack: true
    }
  },
  pm2: true,
  disableClustering: true
})

const clogger = log4js.getLogger()

console.log = (msg) => {
  if (msg instanceof Error) {
    clogger.error('Error occur ', msg)
  } else {
    clogger.info('this console need to remove ', msg)
  }
}

module.exports = clogger
