const logger = require('./logger')
module.exports = () => ctx => {
  if (
    ctx.path === 'users' &&
    ctx.type === 'after' &&
    ['patch', 'update'].includes(ctx.method) &&
    typeof ctx.data.lastOnline !== 'undefined'
  ) {
    const userName = ctx.result.fullname || 'Unknown'
    const text = ctx.data.lastOnline
      ? `${userName} is offline at ${new Date(ctx.result.lastOnline).toISOString()}`
      : `${userName} is online`

    logger.debug(text)
  }

  return ctx
}
