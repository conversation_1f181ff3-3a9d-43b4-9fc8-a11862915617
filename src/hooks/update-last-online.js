module.exports = () => async ctx => {
  const isLastOnlineExists = ctx.result.user && typeof ctx.result.user.lastOnline !== 'undefined'

  if (
    ctx.path === 'authentication' &&
    isLastOnlineExists &&
    ctx.type === 'after'
  ) {
    let lastOnline

    if (ctx.method === 'create') {
      lastOnline = null
    } else if (ctx.method === 'remove') {
      lastOnline = Date.now()
    }

    await ctx.app.service('users').patch(ctx.result.user._id, {
      lastOnline
    })
  }

  return ctx
}
