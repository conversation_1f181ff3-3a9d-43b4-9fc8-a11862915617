// Use this hook to manipulate incoming or outgoing data.
// For more information on hooks see: http://docs.feathersjs.com/api/hooks.html

const logger = require('@hooks/logger')

module.exports = (options = {}) => async context => {
  if (context.method !== 'find') {
    logger.debug('This hook is only for "find" method, other than this will do nothing.')

    return context
  }

  if (context.params.query.$limit && parseInt(context.params.query.$limit) === -1) {
    logger.debug('Pagination is disabled')

    context.params.paginate = false
    delete context.params.query.$limit
  }

  return context
}
