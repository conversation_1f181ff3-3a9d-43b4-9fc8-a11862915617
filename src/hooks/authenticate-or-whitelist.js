// Use this hook to manipulate incoming or outgoing data.
// For more information on hooks see: http://docs.feathersjs.com/api/hooks.html

const { authenticate } = require('@feathersjs/authentication').hooks
const { Forbidden } = require('@feathersjs/errors')
const logger = require('@hooks/logger')

module.exports = () => async context => {
  logger.debug('authenticate or whitelist')

  // bypass internal call
  if (!context.params.provider) return context

  if (context.params.headers && context.params.headers.authorization) {
    return authenticate('jwt')(context)
  } else {
    const headers = context.params.headers || {}
    const ip = headers['x-forwarded-for']

    if (!ip || !context.app.get('sb').allowedIPs.includes(ip)) {
      logger.warn(ip ? `IP: ${ip} not allowed` : 'Could not determine IP')

      throw new Forbidden('You are not allowed to access this resource.')
    }

    logger.info('Incoming request from:', ip)

    return context
  }
}
