// Use this hook to manipulate incoming or outgoing data.
// For more information on hooks see: http://docs.feathersjs.com/api/hooks.html

// eslint-disable-next-line no-unused-vars
const crypto = require('crypto')

module.exports = () => {
  return async context => {
    const app = context.app
    const tokens = app.service('tokens')

    const data = context.result

    const refreshToken = crypto.randomBytes(
      40).toString('hex')

    const multidevice = app.get('authentication').multidevice

    const generateNewToken = () => tokens.create({ user: data.user._id, refreshToken })
      .then(() => {
        context.result.refreshToken = refreshToken
        return context
      })
      .catch(err => {
        return Promise.reject(err)
      })

    return context.data.remember
      ? (multidevice
          ? generateNewToken()
          : tokens
            .remove(null, { query: { user: data.user._id } })
            .then(() => {
              return generateNewToken()
            }))
      : Promise.resolve(context)
  }
}
