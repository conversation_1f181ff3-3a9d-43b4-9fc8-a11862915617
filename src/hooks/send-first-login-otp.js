const logger = require('./logger')

module.exports = () => async ctx => {
  const validation = {
    method: 'create',
    path: 'authentication',
    type: 'after'
  }
  const isValid = await Object.keys(validation).every(v => validation[v] === ctx[v]) && ctx.data.strategy === 'local'

  if (isValid && (ctx.result.user && !ctx.result.user.firstLogin)) {
    const data = {
      type: 'login',
      channel: ctx.result.user.preferredComm,
      secret: `${ctx.result.user._id}-first-login`,
      to: ctx.result.user._id
    }

    if (ctx.result.user.preferredComm === 'email') data.templateUrl = `${ctx.app.get('template').email}/otp-notifications.ejs`

    ctx.app.service('notifications').create(data)
      .then(({ type, channel, token }) => {
        logger.debug(`OTP ${channel} ${type} with token ${token} is sent`)
      })
      .catch(err => logger.error('Oops! something when wrong ', err))
  }

  return ctx
}
