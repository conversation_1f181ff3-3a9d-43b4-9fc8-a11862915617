const ObjectId = require('mongoose').Types.ObjectId

exports.updateConfig = () => async ctx => {
  if (Array.isArray(ctx.result)) {
    for (let i = 0; i < ctx.result.length; i++) {
      await ctx.app.redis.HSET(ctx.result[i].type === 'emailNotificationType' ? 'notificationEmailType' : ctx.result[i].type, `${ctx.result[i]._id}`, ctx.result[i].name)
    }
  }
  if (ctx.result.type === 'trailerStatus') await ctx.app.redis.HSET(ctx.result.type, `${ctx.result._id}`, ctx.result.name)
  if (ctx.result.type === 'trailerSize') await ctx.app.redis.HSET(ctx.result.type, `${ctx.result._id}`, ctx.result.name)
  if (ctx.result.type === 'trailerType') await ctx.app.redis.HSET(ctx.result.type, `${ctx.result._id}`, ctx.result.name)
  if (ctx.result.type === 'emailNotificationType') await ctx.app.redis.HSET('notificationEmailType', `${ctx.result._id}`, ctx.result.name)
  if (ctx.result.type === 'notificationScheduleType') await ctx.app.redis.HSET(ctx.result.type, `${ctx.result._id}`, ctx.result.name)

  return ctx
}

exports.getConfig = (type = true) => async ctx => {
  if (ctx.params.query.trailerStatus) {
    const trailerStatus = await ctx.app.redis.HGETALL('trailerStatus')
    const res = Object.keys(trailerStatus).filter(v => trailerStatus[v] === ctx.params.query.trailerStatus)

    if (type) ctx.params.query.trailerStatus = { $in: res }
    else {
      ctx.params.query['trailerNumber.trailerStatus'] = { $in: res.map(v => ObjectId(v)) }
      delete ctx.params.query.trailerStatus
    }
  }
  if (ctx.params.query.trailerSize) {
    const trailerSize = await ctx.app.redis.HGETALL('trailerSize')
    const res = Object.keys(trailerSize).filter(v => trailerSize[v] === ctx.params.query.trailerSize)

    if (type) ctx.params.query.trailerSize = { $in: res }
    else {
      ctx.params.query['trailerNumber.trailerSize'] = { $in: res.map(v => ObjectId(v)) }
      delete ctx.params.query.trailerSize
    }
  }
  if (ctx.params.query.trailerType) {
    const trailerType = await ctx.app.redis.HGETALL('trailerType')
    const res = Object.keys(trailerType).filter(v => trailerType[v] === ctx.params.query.trailerType)

    if (type) ctx.params.query.trailerType = { $in: res }
    else {
      ctx.params.query['trailerNumber.trailerType'] = { $in: res.map(v => ObjectId(v)) }
      delete ctx.params.query.trailerType
    }
  }

  return ctx
}
