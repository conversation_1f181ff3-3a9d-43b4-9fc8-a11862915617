module.exports = () => ctx => {
  const { method, type, path } = ctx

  if (path === 'authentication' && (method === 'remove' || (method === 'create' && ctx.data && !!ctx.data.email)) && type === 'after') {
    ctx.app.service('activity-logs').create({
      user: ctx.result.user._id,
      module: path,
      action: method === 'remove' ? 'logout' : 'login',
      data: {
        _id: ctx.result.user._id,
        username: ctx.result.user.username,
        fullname: ctx.result.user.fullname || '',
        email: ctx.result.user.email || ''
      }
    })
  }

  return ctx
}
