const path = require('path')
const favicon = require('serve-favicon')
const compress = require('compression')
const helmet = require('helmet')
const cors = require('cors')

const feathers = require('@feathersjs/feathers')
const configuration = require('@feathersjs/configuration')
const express = require('@feathersjs/express')
const socketio = require('@feathersjs/socketio')
const qs = require('qs')

const logger = require('@hooks/logger')
const middleware = require('./middleware')
const services = require('./services')
const appHooks = require('./app.hooks')
const channels = require('./channels')
const events = require('./events')
const sync = require('feathers-sync')

const authentication = require('./authentication')
const mongoose = require('./mongoose')
const firebase = require('./firebase')
const redis = require('redis')
const agenda = require('./agenda')

const app = express(feathers())

app.set('trust proxy', true)
app.configure(express.rest())

app.set('etag', false)

// set the array query limit
app.set('query parser', function (str) {
  return qs.parse(str, {
    arrayLimit: 500
  })
})

// Load app configuration
app.configure(configuration())

app.use(helmet())
app.use(cors())
app.use(compress())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ limit: '10mb', extended: true }))
app.use(favicon(path.join(app.get('public'), 'favicon.ico')))

// Host the public folder
app.use('/', express.static(app.get('public')))

// Set up Plugins and providers
app.configure(socketio(io => {
  io.setMaxListeners(100)
}, {
  transports: ['websocket']
}))

app.configure(mongoose)
app.configure(firebase)

// Configure redis
app.configure(sync({ uri: app.get('redis') }))
app.redis = redis.createClient({ url: app.get('redis') })
app.redis.connect()

// Configure logger
app.logger = logger

app.sync.ready.then(() => {
  app.logger.info('Redis communication layer ready')
})

// Configure other middleware (see `middleware/index.js`)
app.configure(middleware)
app.configure(authentication)

app.initRoleAcl = null

// Set up our services (see `services/index.js`)
app.configure(services)

// Set up event channels (see channels.js)
app.configure(channels)

app.configure(events)

app.configure(agenda)

// Configure a middleware for 404s and the error handler
app.use(express.notFound())
app.use(express.errorHandler({ logger }))

app.hooks(appHooks)

module.exports = app
