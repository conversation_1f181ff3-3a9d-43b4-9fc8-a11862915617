const moment = require('moment')

const deepen = (obj) => {
  const result = {}

  for (const objectPath in obj) {
    const parts = objectPath.split('.')

    let target = result
    while (parts.length > 1) {
      const part = parts.shift()
      target = target[part] = target[part] || {}
    }

    target[parts[0]] = obj[objectPath]
  }

  return result
}

const formatDateSG = (date) => {
  const format = moment(date, 'DD-MM-YYYY HH:mm:ss').format('YYYY-MM-DD HH:mm:ss')
  date = moment.tz(format, 'Asia/Singapore').format()

  return date
}

module.exports = {
  deepen,
  formatDateSG
}
