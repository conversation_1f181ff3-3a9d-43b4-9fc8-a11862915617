const logger = require('@hooks/logger')
const { exec } = require('child_process')

function compactCollection (app) {
  try {
    const shellcmd = `sh script/compactcollection.sh "${app.get('mongodb')}"`
    logger.debug('Excecute cmd', shellcmd)
    exec(shellcmd, (error, stdout, stderr) => {
      logger.info('call back on run script', stdout, stderr)
      if (error) { throw error }
    })
  } catch (e) {
    logger.error('Error occur on compactCollection', e)
  }
}

function getPropertyValue (obj, attributName) {
  const EMPTY_STR = ''
  try {
    if (!obj) {
      throw new Error('Empty Object')
    }
    return obj[attributName] ? obj[attributName] : EMPTY_STR
  } catch (e) {
    logger.error('Error occur on getPropertyValue', e)
  }
  return EMPTY_STR
}

async function dataArchive (app) {
  try {
    const [setting] = await app.service('system-settings').find({
      paginate: false
    })
    logger.debug('set redis data archive status = true.')
    await app.redis.HSET('dataArchive', 'status', 'true')
    await Promise.all(setting.dataArchive.map(v => new Promise((resolve, reject) => {
      const shellcmd = `sh script/dataarchive.sh "${app.get('mongodb')}" "${v.tableName}" "${v.dataRetention}" "${v.fileRetention}" "${app.get('storageBucket')}"`

      logger.debug('Execute command', shellcmd)

      exec(shellcmd, (error, stdout, stderr) => {
        logger.debug('Run command callback', stdout)

        if (error) {
          logger.error('Oops! Something went wrong', stderr)
          return reject(error)
        }

        logger.info(`Successfully archiving ${v.tableName}`)
        resolve()
      })
    }))).then(async () => {
      await app.redis.HSET('dataArchive', 'status', 'false')
      logger.debug('Loop finished, set redis data archive status = false.')
    })
  } catch (e) {
    logger.error('Error occur when archiving data', e)
  }
}

module.exports = {
  compactCollection,
  dataArchive,
  getPropertyValue
}
