const COMPANY_INACTIVE = 'this is inactive company. PLease choose active company'
const COMPANY_EMPTY = 'Please fill the company field'
const COMPANY_IS_OBJECT_ID = 'company must be filled with object id'
const LEASEE_INACTIVE = 'company of lessee is inactive. Please choose active company of lessee'
const LEASEE_EMPTY = 'Please fill the company field'
const LEASEE_IS_OBJECT_ID = 'company of lessee must be filled with object id'
const TRUCK_INACTIVE = 'truck number is inactive. Please choose active truck number'
const TRUCKNUMBER_EMPTY = 'Please fill the truck number field'
const TRUCK_IS_OBJECT_ID = 'truck number must be filled with object id'
const TRAILERNUMBER_EMPTY = 'trailer number is empty'
const TRAILER_NUMBER_EXIST = 'trailer number already exist in this company'
const TRAILERSTATUS_EMPTY = 'Please fill the trailer status field'
const TRAILERSIZE_EMPTY = 'Please fill the trailer size field'
const TRAILERTYPE_EMPTY = 'Please fill the trailer type field'
const TRAILERDESCRIPTION_EMPTY = 'Please fill the trailer description field'
const TRAILERMAKE_EMPTY = 'Please fill the trailer make field'
const TRAILERMODEL_EMPTY = 'Please fill the trailer model field'
const CHASSISNUMBER_EMPTY = 'Please fill the chassis number field'
const MLWKG_EMPTY = 'Please fill the mlwKg field'
const ULWKG_EMPTY = 'Please fill the ulwKg field'
const MANUFACTUREDATE_EMPTY = 'Please fill the manufacture date field'
const REGISTRATIONDATE_EMPTY = 'Please fill the registration date field'
const ROADTAXEXPIRYDATE_EMPTY = 'Please fill the road tax expiry date field'
const INSURANCEEXPIRYDATE_EMPTY = 'Please fill the insurance expiry date field'
const NEXTINSPECTIONDATE_EMPTY = 'Please fill the next inpection date field'
const REGISTRATIONEXPIRYDATE_EMPTY = 'Please fill the registration expiry date field'
const COLOR_EMPTY = 'Please fill the color field'
const PAIRINGDATE_EMPTY = 'Please fill the pairing date field'
const COMPANY_NOT_SAME = 'you\'re not allowed to delete this data'
const CHASSIS_NUMBER_LENGTH = 'Chassis number cannot be more than 30 characters'
const LEASEE_SIMILIAR_WITH_COMPANY = 'Leasee cannot be the same as the company'
const LICENCESTATUS_EMPTY = 'Please fill the licence status'
const ONLEASE_EMPTY = 'Please fill the on lease'
const ISPAIRINGREQUIRED_EMPTY = 'Please fill the is pairing required'
const BILLABLE_EMPTY = 'Please fill the billable'
const GROUP_RETRIEVE = 'Error occur on retrieve the groups'
const TRAILER_DATA = 'Trailer Number not found'
const DUPLICATE_EMAIL_TYPE = 'Duplicate Email Notification Was Found! Please Make Sure You Update Right Record.'
const EMAIL_NOT_VALID = 'email address is not valid'
const REQUIRED_DATE_TIME = 'Notification Date time is required, if Notification schedule type is Specific Date'
const EMAILNOTIFICATIONTYPE_EMPTY = 'Please fill the email notification type'
const RECIPIENTS_EMPTY = 'Please fill the recipients'
const DAYSBEFORENOTIFICATION_EMPTY = 'Please fill the days before notification'
const NOTIFICATIONSCHEDULETYPE_EMPTY = 'Please fill the notification schedule type'
const ENABLED_EMPTY = 'Please fill enabled'
const ID_EMPTY = 'Please add id for updating data'
const TRAILER_NUMBER_GLOBAL_EXIST = 'Trailer Number already exist in another company! please fill licence Status false, if trailer number still in use'
const FROM_EMPTY = 'Please fill the FROM field'
const RECEIVER_EMPTY = 'Please fill the FROM field'
const SUBJECT_EMPTY = 'Please fill the Subject field'
const LASTLOCATION_EMPTY = 'please fill last location'
const LASTLOCATION_STRING = 'last location must be string'
const LASTLOCATION_NOT_VALID = 'Please check last location must fill latitude and longitude'
const INDICATOR_STRING = 'indicator must be string'
const TRAILER_GET_DELETED = 'No trailer Management Found'
const TRAILER_CREATE_DELETED = 'No Trailer Master Found '
const DESCRIPTION_EMPTY = 'please fill description if using push notif'
const FROM_OBJECTID = 'From must be filled with object id'
const RECEIVER_OBJECTID = 'Receiver must be filled with object id'
const INCLUDE_DELETE = 'create trailer not acceptable, "deleted" status is true'
const HAS_APIKEYNAME = 'api key name already exist'
const GET_DATA_BY_COMPANY = 'you\'re not allowed get data using this company id'
const ROLE_IS_OBJECT_ID = 'role must be filled with object id'
const TRAILER_HAS_DELETED = 'Trailer has been deleted'
const ROLEACL_IS_OBJECT_ID = 'role must be filled with object id'

module.exports = {
  COMPANY_INACTIVE,
  COMPANY_EMPTY,
  COMPANY_IS_OBJECT_ID,
  LEASEE_INACTIVE,
  LEASEE_EMPTY,
  LEASEE_IS_OBJECT_ID,
  TRUCK_INACTIVE,
  TRUCKNUMBER_EMPTY,
  TRUCK_IS_OBJECT_ID,
  TRAILERNUMBER_EMPTY,
  TRAILER_NUMBER_EXIST,
  TRAILERSTATUS_EMPTY,
  TRAILERSIZE_EMPTY,
  TRAILERTYPE_EMPTY,
  TRAILERDESCRIPTION_EMPTY,
  TRAILERMAKE_EMPTY,
  TRAILERMODEL_EMPTY,
  CHASSISNUMBER_EMPTY,
  MLWKG_EMPTY,
  ULWKG_EMPTY,
  MANUFACTUREDATE_EMPTY,
  REGISTRATIONDATE_EMPTY,
  ROADTAXEXPIRYDATE_EMPTY,
  INSURANCEEXPIRYDATE_EMPTY,
  NEXTINSPECTIONDATE_EMPTY,
  REGISTRATIONEXPIRYDATE_EMPTY,
  COLOR_EMPTY,
  PAIRINGDATE_EMPTY,
  COMPANY_NOT_SAME,
  CHASSIS_NUMBER_LENGTH,
  LEASEE_SIMILIAR_WITH_COMPANY,
  LICENCESTATUS_EMPTY,
  ONLEASE_EMPTY,
  ISPAIRINGREQUIRED_EMPTY,
  BILLABLE_EMPTY,
  GROUP_RETRIEVE,
  TRAILER_DATA,
  DUPLICATE_EMAIL_TYPE,
  EMAIL_NOT_VALID,
  REQUIRED_DATE_TIME,
  EMAILNOTIFICATIONTYPE_EMPTY,
  RECIPIENTS_EMPTY,
  DAYSBEFORENOTIFICATION_EMPTY,
  NOTIFICATIONSCHEDULETYPE_EMPTY,
  ENABLED_EMPTY,
  ID_EMPTY,
  TRAILER_NUMBER_GLOBAL_EXIST,
  FROM_EMPTY,
  RECEIVER_EMPTY,
  SUBJECT_EMPTY,
  LASTLOCATION_EMPTY,
  LASTLOCATION_STRING,
  LASTLOCATION_NOT_VALID,
  INDICATOR_STRING,
  TRAILER_GET_DELETED,
  TRAILER_CREATE_DELETED,
  DESCRIPTION_EMPTY,
  FROM_OBJECTID,
  RECEIVER_OBJECTID,
  HAS_APIKEYNAME,
  GET_DATA_BY_COMPANY,
  INCLUDE_DELETE,
  ROLE_IS_OBJECT_ID,
  TRAILER_HAS_DELETED,
  ROLEACL_IS_OBJECT_ID
}
