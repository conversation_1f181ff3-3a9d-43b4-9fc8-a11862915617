const { deepen } = require('@utils/format')

module.exports = function (app) {
  // Define the service
  const service = app.service('vehicle-groups')
  const activityLogs = app.service('activity-logs')

  const formatData = async (item, data) => {
    data = await deepen(data)
    data._id = item?._id

    if (!data.vehicleGroupName) data.vehicleGroupName = item.vehicleGroupName

    // Convert object id to readable data
    if (data.group) {
      data.group = await app.service('groups').get(data.group)
        .then(res => res.name)
        .catch(() => null)
    }

    if (data.company) {
      data.company = await app.service('groups').get(data.company)
        .then(res => res.name)
        .catch(() => null)
    }

    if (data.vehicles && data.vehicles.length) {
      data.vehicles = await app.service('vehicles').find({
        paginate: false,
        query: { _id: { $in: data.vehicles } }
      })
        .then(res => {
          if (!res.length) return ''
          return res.map(v => v.vehicleNo).join(', ')
        })
        .catch(() => null)
    }

    if (data.createdBy) {
      data.createdBy = await app.service('users').get(data.createdBy)
        .then(res => (res.fullname || res.username))
        .catch(() => null)
    }

    if (data.updatedBy) {
      data.updatedBy = await app.service('users').get(data.updatedBy)
        .then(res => (res.fullname || res.username))
        .catch(() => null)
    }

    return data
  }

  const onCreated = async (item, ctx) => {
    if (ctx.data) {
      const data = await formatData(item, ctx.data)

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: ctx.path,
        data
      })
    }
  }

  const onPatched = async (item, ctx) => {
    item = ctx.event
    if (ctx.data) {
      const data = await formatData(item, ctx.data)

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: ctx.path,
        data
      })
    }
  }

  const onRemoved = async (item, ctx) => {
    if (item) {
      const data = await formatData(item, item)
      delete data.__v

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: ctx.path,
        data
      })
    }
  }

  service.on('created', onCreated)
  service.on('patched', onPatched)
  service.on('removed', onRemoved)
}
