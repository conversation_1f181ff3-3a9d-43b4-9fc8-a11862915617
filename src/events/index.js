const users = require('./users.event')
const vehicles = require('./vehicles.event')
const vehicleGroups = require('./vehicle-groups.event')
const groups = require('./groups.event')
const rolesAcl = require('./roles-acl.event')
const roles = require('./roles.event')
const articles = require('./articles.event')
const licenses = require('./licenses.event')
const systemSettings = require('./system-settings.event')
const subscriptions = require('./subscriptions.event')
const trailerMaster = require('./trailer-master.event')
const emailNotificationSettings = require('./email-notification-settings.event')

module.exports = function (app) {
  app.configure(users)
  app.configure(vehicles)
  app.configure(vehicleGroups)
  app.configure(groups)
  app.configure(rolesAcl)
  app.configure(roles)
  app.configure(articles)
  app.configure(licenses)
  app.configure(systemSettings)
  app.configure(subscriptions)
  app.configure(trailerMaster)
  app.configure(emailNotificationSettings)
}
