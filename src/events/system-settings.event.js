const { deepen } = require('@utils/format')
const logger = require('@hooks/logger')

module.exports = function (app) {
  // Define the service
  const service = app.service('system-settings')
  const activityLogs = app.service('activity-logs')

  const formatData = async (item, data) => {
    data = await deepen(data)

    const timesFields = ['jobReminder', 'jobCancel', 'multiReminderInterval']
    Object.keys(data).forEach(v => {
      if (timesFields.includes(v)) {
        data[v] = `${parseInt(data[v].days) ? data[v].days + ' Days' : ''} ${parseInt(data[v].hours) ? data[v].hours + ' Hours' : ''} ${parseInt(data[v].minutes) ? data[v].minutes + ' Minutes' : ''}`.trim()
      }
    })

    if (data.trackingDistance) data.trackingDistance += ' Meter'
    if (data.mapAutoRefreshInterval) data.mapAutoRefreshInterval += ' Seconds'

    // Convert object id to readable data
    if (data.updatedBy) {
      data.updatedBy = await app.service('users').get(data.updatedBy)
        .then(res => (res.fullname || res.username))
        .catch(() => null)
    }

    return data
  }

  const onPatched = async (item, ctx) => {
    if (ctx.data) {
      item = ctx.event
      const data = await formatData(item, ctx.data)
      delete data.__v

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: 'system-settings',
        data
      }).catch(err => logger.error('Oops! something wrong', err))
    }
  }

  service.on('patched', onPatched)
}
