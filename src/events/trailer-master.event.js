const { deepen } = require('@utils/format')

module.exports = function (app) {
  // Define the service
  const service = app.service('trailer-master')
  const activityLogs = app.service('activity-logs')

  const arrayData = async (res, type, x) => {
    const data = type === 'multi' ? res[x] : res
    // Convert object id to readable data
    if (data.company) {
      data.company = await app.service('groups').get(data.company)
        .then(res => res.name)
        .catch(() => null)
    }

    if (data.leasee) {
      data.leasee = await app.service('groups').get(data.leasee)
        .then(res => res.name)
        .catch(() => null)
    }

    if (data.trailerStatus) {
      data.trailerStatus = await app.service('trailer-config').get(data.trailerStatus)
        .then(res => res.name)
        .catch(() => null)
    }

    if (data.trailerSize) {
      data.trailerSize = await app.service('trailer-config').get(data.trailerSize)
        .then(res => res.name)
        .catch(() => null)
    }

    if (data.trailerType) {
      data.trailerType = await app.service('trailer-config').get(data.trailerType)
        .then(res => res.name)
        .catch(() => null)
    }

    if (data.truckNumber) {
      data.truckNumber = await app.service('vehicles').get(data.truckNumber)
        .then(res => res.vehicleNo)
        .catch(() => null)
    }

    if (data.createdBy) {
      data.createdBy = await app.service('users').get(data.createdBy)
        .then(res => (res.fullname || res.username))
        .catch(() => null)
    }

    if (data.updatedBy) {
      data.updatedBy = await app.service('users').get(data.updatedBy)
        .then(res => (res.fullname || res.username))
        .catch(() => null)
    }

    // Unused field
    if (data.deleted) delete data.deleted

    return data
  }
  const formatData = async (item, data) => {
    data = await deepen(data)

    if (Object.keys(data).filter(v => v === '0').length) {
      for (let i = 0; i < Object.keys(data).length; i++) {
        if (data[i] && item && item.trailerNumber && data[i].trailerNumber && data[i].trailerNumber === item.trailerNumber) data = await arrayData(data, 'multi', i)
      }
    } else {
      data = await arrayData(data, 'single', 0)
    }

    data._id = item?._id

    if (!data.trailerNumber) data.trailerNumber = item.trailerNumber

    return data
  }

  const onCreated = async (item, ctx) => {
    if ((Array.isArray(ctx.data) && ctx.data.length > 0) || (!Array.isArray(ctx.data))) {
      const data = await formatData(item, ctx.data)

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: ctx.path,
        data
      })
    }
  }

  const onPatched = async (item, ctx) => {
    item = ctx.event
    const isDataValid = ctx.data && typeof ctx.data.lastOnline === 'undefined'
    if (isDataValid && !ctx.data.deleted) {
      const data = await formatData(item, ctx.data)

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: ctx.path,
        data
      })
    }
  }

  const onRemoved = async (item, ctx) => {
    if (item) {
      const data = await formatData(item, item)
      delete data.__v

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: ctx.path,
        data
      })
    }
  }

  service.on('created', onCreated)
  service.on('patched', onPatched)
  service.on('removed', onRemoved)
}
