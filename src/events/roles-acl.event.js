const { deepen } = require('@utils/format')
const logger = require('@hooks/logger')

module.exports = function (app) {
  // Define the service
  const service = app.service('roles-acl')
  const activityLogs = app.service('activity-logs')

  const formatData = async (item, data) => {
    data = await deepen(data)
    data._id = item?._id

    if (!data.name) data.name = item.name

    // Convert object id to readable data
    if (data.createdBy) {
      data.createdBy = await app.service('users').get(data.createdBy)
        .then(res => (res.fullname || res.username))
        .catch(() => null)
    }

    if (data.updatedBy) {
      data.updatedBy = await app.service('users').get(data.updatedBy)
        .then(res => (res.fullname || res.username))
        .catch(() => null)
    }

    return data
  }

  const onCreated = async (item, ctx) => {
    if (ctx.data) {
      const data = await formatData(item, ctx.data)

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: 'role-modules',
        data
      }).catch(err => logger.error('Oops! something wrong', err))
    }
  }

  const onPatched = async (item, ctx) => {
    if (ctx.data) {
      item = ctx.event
      const data = await formatData(item, ctx.data)

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: 'role-modules',
        data
      }).catch(err => logger.error('Oops! something wrong', err))
    }
  }

  const onRemoved = async (item, ctx) => {
    if (item) {
      const data = await formatData(item, item)
      delete data.__v

      activityLogs.create({
        user: (ctx.params.user && ctx.params.user._id) || null,
        action: ctx.method,
        module: 'role-modules',
        data
      }).catch(err => logger.error('Oops! something wrong', err))
    }
  }

  service.on('created', onCreated)
  service.on('patched', onPatched)
  service.on('removed', onRemoved)
}
