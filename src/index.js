require('module-alias/register')

const logger = require('@hooks/logger')
const app = require('./app')
const port = app.get('port')
const server = app.listen(port)

process.on('unhandledRejection', (reason, p) =>
  logger.error('Unhandled Rejection at: Promise ', p, reason)
)

process.on('uncaughtException', (err, origin) => {
  logger.error('Unhandled Exception', err)
})

server.on('listening', () =>
  logger.info('Feathers application started on http://%s:%d', app.get('host'), port)
)
