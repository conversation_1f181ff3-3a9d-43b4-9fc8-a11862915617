// groups-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'groups'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    name: {
      type: String,
      required: true
    },
    code: { type: String, uppercase: true },
    contactPerson: {
      name: { type: String },
      phone: { type: String }
    },
    groupInfo: {
      uen: { type: String },
      registrationDate: { type: Date, default: Date.now() },
      address: { type: String },
      postalCode: { type: String },
      phone: { type: String },
      email: { type: String }
    },
    status: {
      type: String,
      enum: ['inactive', 'active', 'suspended', 'terminated']
    },
    parent: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    organizationType: {
      type: Schema.Types.ObjectId,
      ref: 'roles'
    },
    logo: { type: String },
    timezone: { type: String, default: '+08:00' },
    level: { type: Number, default: 0 },
    copiedGroupInfo: { type: Boolean, default: false },
    copiedContactPerson: { type: Boolean, default: false },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    trailerShareStatus: { type: Boolean, default: false }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
