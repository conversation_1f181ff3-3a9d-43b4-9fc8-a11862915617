// trips-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'trips'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    outHdr: {
      type: Object
    },
    tripId: {
      type: String
    },
    pmNo: {
      type: String
    },
    status: {
      type: String
    },
    c1: {
      type: String
    },
    s1: {
      type: String
    },
    c2: {
      type: String
    },
    s2: {
      type: String
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
