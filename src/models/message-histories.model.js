// message-histories-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'messageHistories'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    messagDateTime: {
      type: Date
    },
    userSend: {
      type: String
    },
    userRec: {
      type: String
    },
    dataMsg: {
      type: String
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    vehicle: {
      type: Schema.Types.ObjectId,
      ref: 'vehicles'
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
