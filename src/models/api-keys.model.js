// api-keys-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'apiKeys'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    apiKeyName: { type: String, unique: true },
    description: { type: String },
    headerKey: { type: String },
    accessToken: { type: String, required: true },
    tmsAccessToken: { type: String },
    responseWebhook: { type: String },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    status: {
      type: String,
      default: 'active',
      enum: ['inactive', 'active', 'suspended']
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
