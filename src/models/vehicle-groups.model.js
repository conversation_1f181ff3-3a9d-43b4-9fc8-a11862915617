// vehicle-groups-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'vehicleGroups'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    vehicleGroupName: {
      type: String,
      required: true
    },
    group: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    vehicles: [
      {
        type: Schema.Types.ObjectId,
        ref: 'vehicles'
      }
    ],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
