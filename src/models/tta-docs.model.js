// tta-docs-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'ttaDocs'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    jobId: { type: Schema.Types.ObjectId, ref: 'jobs', unique: true },
    vehnum: { type: String, required: true },
    vehlatestlocation: { type: String },
    vehlatestlocationtime: { type: Date },
    completiondt: { type: Date },
    gates: [{
      gate: { type: String, required: true },
      inoutzone: { type: String, enum: ['IN', 'OUT'], required: true, default: 'OUT' },
      outsidetta: { type: String },
      insidetta: { type: String },
      lastupdate: { type: Date },
      triggerstatus: { type: String, enum: ['Y', 'N'], required: true, default: 'N' }
    }],
    msglastsentdt: { type: Date },
    status: { type: String, enum: ['active', 'completed', 'expired', 'triggered', 'inactive', 'active-far'], required: true },
    msgcount: { type: Number, default: 0 },
    upd_flg: { type: String, enum: ['Y', 'N'], default: 'N', required: true }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
