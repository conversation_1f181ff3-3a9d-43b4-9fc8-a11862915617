// job-counter-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'jobCounter'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    tripId: {
      type: String
    },
    containerNumber: {
      type: String
    },
    truckNumber: {
      type: String
    }
  }, {
    timestamps: true,
    capped: { size: 10000000 }
  })

  schema.index({ tripId: 1, containerNumber: 1, truckNumber: 1 }, { unique: true })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
