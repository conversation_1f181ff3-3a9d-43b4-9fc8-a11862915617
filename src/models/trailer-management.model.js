// trailer-management-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'trailerManagement'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    trailerNumber: {
      type: Schema.Types.ObjectId,
      ref: 'trailerMaster',
      required: true
    },
    lastLocation: {
      type: String
    },
    job: {
      type: String
    },
    remarks1: {
      type: String
    },
    remarks2: {
      type: String
    },
    referenceDateTime1: {
      type: Date
    },
    referenceDateTime2: {
      type: Date
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
