// vehicles-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'vehicles'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    vehicleNo: {
      type: String,
      required: true,
      uppercase: true
    },
    group: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    driver: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    status: {
      type: Boolean,
      default: true
    },
    license: {
      type: Schema.Types.ObjectId,
      ref: 'licences'
    },
    licenseList: [
      {
        _id: false,
        idLicense: {
          type: Schema.Types.ObjectId,
          ref: 'licenses'
        },
        portNet: {
          type: Boolean
        }
      }
    ],
    portNet: {
      type: Boolean,
      default: false
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    remarks: {
      type: 'String'
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
