// roles-acl-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'rolesAcl'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    name: { type: String, required: true, unique: true },
    forCdas: {
      type: Boolean,
      default: false
    },
    modules: [
      {
        _id: false,
        name: {
          type: String,
          required: true
        },
        allowCreate: {
          type: Boolean,
          default: false
        },
        allowRead: {
          type: Boolean,
          default: false
        },
        allowUpdate: {
          type: Boolean,
          default: false
        },
        allowDelete: {
          type: Boolean,
          default: false
        }
      }
    ],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
