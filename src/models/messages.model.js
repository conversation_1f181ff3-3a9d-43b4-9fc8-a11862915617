// messages-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html

// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'messages'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    // type text or images
    type: {
      type: String,
      required: true
    },
    conversationId: {
      type: Schema.Types.ObjectId,
      ref: 'conversations'
    },
    content: {
      type: String
    },
    media: {
      type: String
    },
    sender: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    recipientStatus: [
      {
        _id: false,
        user: {
          type: Schema.Types.ObjectId,
          ref: 'users'
        },
        vehicle: {
          type: Schema.Types.ObjectId,
          ref: 'vehicles'
        },
        readBy: { type: String, enum: ['driver', 'controller'] },
        status: { type: String, enum: ['delivered', 'read'] },
        updatedAt: { type: Date, default: Date.now() }
      }
    ],
    flagFrom: { /** for flag if message sent from button broadcast **/
      type: String,
      enum: ['normal', 'broadcast', 'ctr-message', 'inward'],
      default: 'normal'
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    sentAt: { type: Date },
    draft: {
      type: Boolean
    },
    vehicle: {
      type: Schema.Types.ObjectId,
      ref: 'vehicles'
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
