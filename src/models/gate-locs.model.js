// gate-locs-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'gateLocs'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    gate: { type: String, required: true },
    linkgates: [{ type: String }],
    lat: { type: Number, required: true },
    long: { type: Number, required: true },
    zoneradius: { type: Number, default: 3 },
    insidezonetime: { type: Number, default: 10 },
    outsidezonetime: { type: Number, default: 40 },
    remark: { type: String, maxlength: 200 }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
