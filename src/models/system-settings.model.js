// vehicle-groups-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'systemSettings'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    jobReminder: {
      type: Object
    },
    jobCancel: {
      type: Object
    },
    trackingDistance: {
      type: Number,
      default: 0
    },
    trackingInterval: {
      type: Number,
      default: 10000
    },
    allowMultipleReminder: {
      type: Boolean,
      default: false
    },
    multiReminderInterval: {
      type: Object
    },
    mapAutoRefreshInterval: {
      type: Number,
      default: 15
    },
    graceSb: {
      type: Object
    },
    radiusDistance: {
      type: Number,
      default: 500
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    limitRequestFailed: {
      type: Number,
      default: 3
    },
    dataArchive: [
      {
        _id: false,
        tableName: {
          type: 'String'
        },
        dataRetention: {
          type: Number,
          default: 380
        },
        fileRetention: {
          type: Number,
          default: 3
        }
      }
    ],
    optetruckApiKey: {
      type: String
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
