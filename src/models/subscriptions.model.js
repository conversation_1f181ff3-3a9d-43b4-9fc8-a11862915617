// subscriptions-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'subscriptions'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    company: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    license: {
      type: Schema.Types.ObjectId,
      ref: 'licences'
    },
    licenseNo: {
      type: Number,
      default: 0
    },
    endDate: {
      type: Date
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    companySubUpdatedAt: {
      type: Date
    },
    companySubUpdatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    vehSubUpdatedAt: {
      type: Date
    },
    vehSubUpdatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
