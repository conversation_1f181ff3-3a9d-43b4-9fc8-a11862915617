// job-deleted-model.js - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
module.exports = function (app) {
  const modelName = 'job_deleted'
  const mongooseClient = app.get('mongooseClient')
  const { Schema } = mongooseClient
  const schema = new Schema({
    bookingN: {
      type: String
    },
    depotC: {
      type: String
    },
    depotName: {
      type: String
    },
    haulierC: {
      type: String
    },
    trailerNumber: {
      type: String
    },
    ucrN: {
      type: String
    },
    jobType: {
      type: String
    },
    jobTypeManual: { // for save job type set manual by driver
      type: String
    },
    tripId: {
      type: String
    },
    text: {
      type: String
    },
    jobStatus: {
      type: String,
      enum: ['new', 'in progress', 'completed', 'withdrawn', 'cancel', 'not dispatched', 'rejected', 'start pickup', 'completed pickup', 'start delivery', 'completed delivery'],
      required: true
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: 'groups'
    },
    DriverLogin: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    truckNumber: {
      type: String
    },
    logVehicleN: [
      {
        _id: false,
        vehicleN: { type: String },
        assignAt: { type: Date }
      }
    ],
    sealNumber: {
      type: String
    },
    containerNumber: {
      type: String
    },
    cntrStatusC: {
      type: String
    },
    cntrOpC: {
      type: String
    },
    batchNumber: {
      type: String
    },
    typeSize: {
      type: String
    },
    psaInstruction: {
      type: String
    },
    pickupLocation: {
      type: String
    },
    deliveryLocation: {
      type: String
    },
    psaLocation: {
      type: String
    },
    survey: {
      type: String,
      default: null
    },
    weight: {
      type: Number,
      default: 0
    },
    tt: {
      type: String
    },
    tag: {
      type: String
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'users'
    },
    firstReminder: {
      type: Boolean,
      default: false
    },
    lastReminder: {
      type: Date
    },
    snapshotDt: {
      type: Date
    },
    payloadType: {
      type: String,
      enum: ['mix', 'normal', 'batch'],
      required: true,
      default: 'normal'
    },
    tripIdFromAdaptor: {
      type: String
    },
    bookingSlotStartDt: {
      type: Date,
      default: null
    },
    bookingSlotEndDt: {
      type: Date,
      default: null
    },
    jobStartDate: {
      type: Date,
      default: null
    },
    jobEndDate: {
      type: Date,
      default: null
    },
    haulierMovementC: {
      type: String,
      enum: ['in', 'out', null],
      default: null
    },
    area_id: {
      type: String
    },
    source: {
      type: String
    },
    driverName: {
      type: String
    }
  }, {
    timestamps: true
  })

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    mongooseClient.deleteModel(modelName)
  }
  return mongooseClient.model(modelName, schema)
}
