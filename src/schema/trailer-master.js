const Joi = require('joi')
const validate = require('feathers-validate-joi')
const _ = require('lodash')
const mongooseClient = require('mongoose')
const { Schema } = mongooseClient

// define user schema
const schema = {
  trailerNumber: {
    type: String,
    uppercase: true,
    validation: Joi.string().trim().min(2).max(254)
  },
  company: {
    type: Schema.Types.ObjectId,
    ref: 'groups',
    validation: Joi.optional()
  },
  leasee: {
    type: Schema.Types.ObjectId,
    ref: 'groups',
    validation: Joi.optional()
  },
  licenceStatus: {
    type: Boolean,
    default: false,
    validation: Joi.optional()
  },
  onLease: {
    type: Boolean,
    default: false,
    validation: Joi.optional()
  },
  trailerDescription: {
    type: String,
    validation: Joi.optional()
  },
  trailerMake: {
    type: String,
    validation: Joi.optional()
  },
  trailerModel: {
    type: String,
    validation: Joi.optional()
  },
  trailerStatus: {
    type: Schema.Types.ObjectId,
    ref: 'trailerConfig',
    validation: Joi.optional()
  },
  trailerSize: {
    type: Schema.Types.ObjectId,
    ref: 'trailerConfig',
    validation: Joi.optional()
  },
  trailerType: {
    type: Schema.Types.ObjectId,
    ref: 'trailerConfig',
    validation: Joi.optional()
  },
  chassisNumber: {
    type: String,
    validation: Joi.optional()
  },
  mlwKg: {
    type: Number,
    validation: Joi.optional()
  },
  ulwKg: {
    type: Number,
    validation: Joi.optional()
  },
  manufactureDate: {
    type: Date,
    validation: Joi.optional()
  },
  registrationDate: {
    type: Date,
    validation: Joi.optional()
  },
  roadTaxExpiryDate: {
    type: Date,
    validation: Joi.optional()
  },
  insuranceExpiryDate: {
    type: Date,
    validation: Joi.optional()
  },
  nextInspectionDate: {
    type: Date,
    validation: Joi.optional()
  },
  registrationExpiryDate: {
    type: Date,
    validation: Joi.optional()
  },
  color: {
    type: String,
    validation: Joi.optional()
  },
  remarks: {
    type: String,
    validation: Joi.optional()
  },
  isPairingRequired: {
    type: Boolean,
    default: false,
    validation: Joi.optional()
  },
  truckNumber: {
    type: Schema.Types.ObjectId,
    ref: 'vehicles',
    validation: Joi.optional()
  },
  pairingDate: {
    type: Date,
    validation: Joi.optional()
  },
  pairedBy: {
    type: Schema.Types.ObjectId,
    ref: 'users',
    validation: Joi.optional()
  },
  billable: {
    type: Boolean,
    default: false,
    validation: Joi.optional()
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'users',
    validation: Joi.optional()
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'users',
    validation: Joi.optional()
  },
  deleted: {
    type: Boolean,
    default: false,
    validation: Joi.optional()
  }
}

// take validation rules from user schema
const dbSchema = {}
const validationSchema = {}

_.forEach(schema, (v, k) => {
  if (v.validation) { validationSchema[k] = v.validation }
  delete v.validation
  dbSchema[k] = v
})

// validate input
const joiOptions = { convert: true, abortEarly: false }
const trailerMasterValidation = validate.form(Joi.object().keys(validationSchema), joiOptions)

module.exports = { dbSchema, trailerMasterValidation }
