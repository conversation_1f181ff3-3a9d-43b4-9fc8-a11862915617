const Joi = require('joi')
const validate = require('feathers-validate-joi')
const _ = require('lodash')
const mongooseClient = require('mongoose')
const { Schema } = mongooseClient

// define user schema
const schema = {
  fullname: {
    type: String,
    required: true,
    validation: Joi.string().trim().min(2).max(254)
  },
  username: {
    type: String,
    unique: true,
    required: true,
    lowercase: true,
    index: true,
    validation: Joi.string().trim().min(2).max(30)
  },
  mobile: {
    type: String,
    validation: Joi.string().trim().min(2).max(15)
  },
  email: {
    type: String,
    index: true,
    text: true,
    validation: Joi.string().trim().max(254)
      // eslint-disable-next-line no-useless-escape
      .regex(/^[\w-\.+]+@([\w-]+\.)+[\w-]{2,4}$/, 'email address is not valid')
      .allow('').optional()
  },
  status: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
    validation: Joi.optional()
  },
  password: {
    type: String,
    validation: Joi.string().trim().min(2).max(30).required()
  },
  group: {
    type: Schema.Types.ObjectId,
    ref: 'groups',
    validation: Joi.optional()
  },
  company: {
    type: Schema.Types.ObjectId,
    ref: 'groups',
    validation: Joi.optional()
  },
  passwordHistories: [
    {
      _id: false,
      oldPassword: {
        type: String
      },
      changeAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  avatar: {
    type: String
  },
  allowNotifications: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'users',
    validation: Joi.optional()
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'users',
    validation: Joi.optional()
  },
  device: {
    os: { type: String },
    FCMId: { type: String },
    uuid: { type: String },
    validation: Joi.optional(),
    version: { type: String }
  },
  deviceHistory: {
    os: { type: String },
    FCMId: { type: String },
    uuid: { type: String },
    validation: Joi.optional(),
    version: { type: String }
  },
  fontSize: {
    type: String,
    validation: Joi.optional()
  },
  preferredComm: {
    type: String,
    default: 'email',
    validation: Joi.optional()
  },
  role: {
    type: Schema.Types.ObjectId,
    ref: 'roles',
    required: true,
    validation: Joi.optional()
  },
  roleAcl: {
    type: Schema.Types.ObjectId,
    ref: 'rolesAcl',
    validation: Joi.optional()
  },
  lastOnline: {
    type: Date,
    default: Date.now()
  },
  firstLogin: {
    type: Boolean,
    default: false
  },
  requiredOtp: {
    type: Boolean,
    required: true,
    default: false
  }
}

// take validation rules from user schema
const dbSchema = {}
const validationSchema = {}

_.forEach(schema, (v, k) => {
  if (v.validation) { validationSchema[k] = v.validation }
  delete v.validation
  dbSchema[k] = v
})

// validate input
const joiOptions = { convert: true, abortEarly: false }
const userValidation = validate.form(Joi.object().keys(validationSchema), joiOptions)

module.exports = { dbSchema, userValidation }
