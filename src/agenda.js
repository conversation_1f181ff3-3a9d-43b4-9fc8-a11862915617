const { Agenda } = require('agenda')
const logger = require('@hooks/logger')
const commonFunc = require('@utils/commonFunc')

module.exports = function (app) {
  const agenda = new Agenda({ db: { address: app.get('mongodb') } })
  app.agenda = agenda

  agenda.define('call config', async job => {
    const configLog = await app.service('cdi-logs').find({
      paginate: false,
      query: {
        $limit: 1,
        type: 'config'
      }
    }).catch(() => [])

    const data = await app.service('config').get(null)
      .catch(async err => {
        logger.error(`Failed to call config, ${err.message || 'unknown reason'}`, err)

        !configLog.length
          ? await app.service('cdi-logs').create({
            type: 'config',
            data: null,
            lastFailAt: new Date().toISOString()
          })
          : await app.service('cdi-logs').patch(configLog[0]._id, {
            lastFailAt: new Date().toISOString()
          })

        return null
      })

    if (data) {
      try {
        !configLog.length
          ? await app.service('cdi-logs').create({
            type: 'config',
            data,
            lastUpdatedAt: new Date().toISOString()
          })
          : await app.service('cdi-logs').patch(configLog[0]._id, {
            data,
            lastUpdatedAt: new Date().toISOString()
          })
      } catch (err) {
        logger.error(`Failed to update config log, ${err.message || 'unknown reason'}`, err)
      }
    }
  })

  agenda.define('call cdi health', async job => {
    const healthLog = await app.service('cdi-logs').find({
      paginate: false,
      query: {
        $limit: 1,
        type: 'health'
      }
    }).catch(() => [])

    const data = await app.service('cdi-health').get(null)
      .catch(async err => {
        logger.error(`Failed to call health info, ${err.message || 'unknown reason'}`, err)

        !healthLog.length
          ? await app.service('cdi-logs').create({
            type: 'health',
            data: null,
            lastFailAt: new Date().toISOString()
          })
          : await app.service('cdi-logs').patch(healthLog[0]._id, {
            lastFailAt: new Date().toISOString()
          })

        return null
      })

    if (data) {
      try {
        !healthLog.length
          ? await app.service('cdi-logs').create({
            type: 'health',
            data,
            lastUpdatedAt: new Date().toISOString()
          })
          : await app.service('cdi-logs').patch(healthLog[0]._id, {
            data,
            lastUpdatedAt: new Date().toISOString()
          })
      } catch (err) {
        logger.error(`Failed to update config log, ${err.message || 'unknown reason'}`, err)
      }
    }
  })

  agenda.define('data archive', async () => {
    try {
      const { status } = await app.redis.HGETALL('dataArchive')
      if (status === 'false') {
        logger.debug('data archive starting.')
        await commonFunc.dataArchive(app)
      }
    } catch (err) {
      logger.error(`Failed to update config log, ${err.message || 'unknown reason'}`, err)
    }
  })

  // Conditionally define and schedule "store message histories" based on CONNECT_MQTT
  if (!process.env.CONNECT_MQTT) {
    // Only define and schedule this job if CONNECT_MQTT is not set
    agenda.define('store message histories', async () => {
      const now = new Date()

      try {
        const res = await app.service('message-histories/store-message-histories').create({})

        if (res.length) logger.info(`Run store at ${now.getHours()}:${now.getMinutes()}`)
      } catch (err) {
        logger.error(`Failed to store message history, ${err.message || 'unknown reason'}`)
      }
    })
  } else {
    logger.info('Skipping "store message histories" job on this server because CONNECT_MQTT is set.')
  }

  agenda.define('compact collection', async () => {
    await commonFunc.compactCollection(app)
  })

  agenda.define('job cancellation', async () => {
    const self = app
    const moment = require('moment')

    try {
      // get system setting
      const setting = await self.service('system-settings').find({
        query: {
          $limit: 1
        },
        paginate: false
      })

      if (setting[0]) {
        const jobCancel = setting[0].jobCancel
        const jobReminder = setting[0].jobReminder
        const multiReminderInterval = setting[0].multiReminderInterval
        const multiReminder = setting[0].allowMultipleReminder
        const graceSb = setting[0].graceSb

        const cancelSetting =
          jobCancel.days * 86400 +
          jobCancel.hours * 3600 +
          jobCancel.minutes * 60 // 4 days
        const firstReminderSetting =
          jobReminder.days * 86400 +
          jobReminder.hours * 3600 +
          jobReminder.minutes * 60
        const multiReminderIntervalSetting =
          multiReminderInterval.days * 86400 +
          multiReminderInterval.hours * 3600 +
          multiReminderInterval.minutes * 60
        const graceCancelSetting =
          graceSb.days * 86400 +
          graceSb.hours * 3600 +
          graceSb.minutes * 60 // 4 days
        // end get system setting

        const timeNow = Date.now()
        const timeNowFormated = moment(timeNow)
        const vehicles = await self.service('vehicles').find({
          query: {
            driver: { $exists: true }
          },
          paginate: false
        })

        for (let a = 0; a < vehicles.length; a++) {
          let jobNeedAlert = 0
          const arrayContainer = []
          const job = await self.service('jobs').find({
            query: {
              truckNumber: vehicles[a].vehicleNo,
              jobStatus: {
                $in: ['new', 'in progress']
              },
              jobType: { $ne: 'OET' }
            },
            paginate: false
          })

          for (let i = 0; i < job.length; i++) {
            if (job[i].bookingN) {
              if (job[i].jobStatus === 'new') {
                const jobBookingEnd = job[i].bookingSlotEndDt
                const jobBookingEndGrace = moment(jobBookingEnd).add(graceCancelSetting, 'seconds').toDate()

                if (jobBookingEndGrace < moment()) {
                  logger.info('Start to patch job cancel in jobBookingEndGrace < moment()')
                  await self
                    .service('jobs')
                    .patch(job[i]._id, { jobStatus: 'cancel' })
                }
              }
            } else {
              const jobCreatedAt = job[i].createdAt
              const diff = timeNowFormated.diff(jobCreatedAt, 'seconds') // diff now - job created

              // multi reminder is allow
              if (multiReminder) {
                if (job[i].firstReminder) {
                  // if first reminder already sent
                  const lastReminder = job[i].lastReminder
                  const diffReminder = timeNowFormated.diff(lastReminder, 'seconds')

                  if (diffReminder >= multiReminderIntervalSetting) {
                    jobNeedAlert++
                    if (job[i] && job[i].containerNumber) {
                      arrayContainer.push(job[i].containerNumber)
                    } else {
                      arrayContainer.push(job[i].batchNumber)
                    }
                    // update status
                    await self
                      .service('jobs')
                      .patch(job[i]._id, { lastReminder: timeNowFormated })
                  }
                }
              }

              // the newest job never send first reminder
              if (!job[i].firstReminder) {
                if (diff >= firstReminderSetting && diff < cancelSetting) {
                  // send first reminder
                  jobNeedAlert++
                  if (job[i] && job[i].containerNumber) {
                    arrayContainer.push(job[i].containerNumber)
                  } else {
                    arrayContainer.push(job[i].batchNumber)
                  }
                  // update status
                  await self
                    .service('jobs')
                    .patch(job[i]._id, {
                      firstReminder: true,
                      lastReminder: timeNowFormated
                    })
                }
              }

              // job with status new in more than 4 days
              if (diff >= cancelSetting) {
                // auto update the status to cancel
                logger.info('Start to patch job cancel in diff >= cancelSetting')
                await self
                  .service('jobs')
                  .patch(job[i]._id, { jobStatus: 'cancel' })
              }
            }
          }
          // if have job will expired
          if (jobNeedAlert > 0) {
            // send socket for show pop up on mobile apps
            const msgData = {
              type: 'private',
              company: vehicles[a].company,
              members: [vehicles[a].driver],
              messages: {
                type: 'job-expired',
                content: arrayContainer.join()
              }
            }
            await self.service('messages').create(msgData)
          }
        }
        // check jobs with status new and in progress if more than 4 days
      }
    } catch (err) {
      logger.error(`Failed to check job cancellation, ${err.message || 'unknown reason'}`, err)
    }
  })

  agenda.on('ready', async () => {
    const agendConfig = app.get('agenda')
    await agenda.start()
    // Schedule jobs
    if (!process.env.CONNECT_MQTT) {
      // Only schedule "store message histories" on servers without CONNECT_MQTT
      await agenda.every(agendConfig.msgHistories, 'store message histories', '', {
        timezone: agendConfig.timezone
      })
    }
    await agenda.every('1 minutes', 'call config')
    await agenda.every('1 minutes', 'call cdi health')
    await agenda.every(agendConfig.dataArchive, 'data archive', '', {
      timezone: agendConfig.timezone
    })
    await agenda.every(agendConfig.compactCollection, 'compact collection', '', {
      timezone: agendConfig.timezone
    })

    // as per request from client (Yiqian, TC329) to reduce server memory usage regarding to the agenda on every minutes
    await agenda.every(agendConfig.jobCancellation, 'job cancellation', '', {
      timezone: agendConfig.timezone
    })
  })

  agenda.on('complete:store message histories', async job => {
    logger.debug('Store message histories completed')

    try {
      await job.remove()
      logger.debug('Store message histories removed')
    } catch (err) {
      logger.error(err)
    }
  })

  agenda.on('complete:compact collection', async job => {
    logger.debug('compact collection completed')

    try {
      await job.remove()
      logger.debug('compact collection removed')
    } catch (err) {
      logger.error(err)
    }
  })
}
