// Application hooks that run for every service
const logger = require('@hooks/logger')
const { SYNC } = require('feathers-sync')
const collation = require('@hooks/collation')

const serviceCustom = ['messages', 'authentication', 'aggregate', 'users/reset-user-password', 'trailer-managament', 'trailer-master', 'email-notification-setting', 'import-trailer-master', 'custom-notification', 'trailer-config', 'sb-entryexit', 'sb-bookings', 'sbCtr/bookingsPush', 'sbCtr/bookingsPushCancelled', 'delivery-record', 'authentication/otp-confirmation', 'updateDestination', 'dispatchMovement', 'cancelDispatchedMovement', 'job-combine']

const feathersSync = () => async ctx => {
  if (['articles', 'groups', 'licences', 'roles', 'roles-acl', 'subscriptions', 'system-settings', 'users', 'vehicle-groups', 'vehicles', 'trailer-master', 'email-notification-settings'].includes(ctx.path)) {
    ctx[SYNC] = false
  }
}

const createResponse = () => ctx => {
  if (ctx.params.headers && ctx.params.headers.authorization && serviceCustom.indexOf(ctx.path) === -1) {
    ctx.event = ctx.result
    ctx.result = {
      created: 'OK'
    }

    return ctx
  }
}

const patchResponse = () => ctx => {
  if (ctx.params.headers && ctx.params.headers.authorization && serviceCustom.indexOf(ctx.path) === -1) {
    ctx.event = ctx.result
    ctx.result = {
      patched: 'OK'
    }

    return ctx
  }
}

const createLog = () => ctx => {
  const log = {
    params: ctx.params || {},
    body: ctx.data || {},
    result: ctx.result || {}
  }

  if (ctx.type === 'before') delete log.result
  if (logger.isTraceEnabled()) {
    logger.trace(`${ctx.type}: /${ctx.path} ${ctx.method}`, log)
  }

  return ctx
}

module.exports = {
  before: {
    all: [createLog()],
    find: [collation()],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [createLog()],
    find: [],
    get: [],
    create: [createResponse(), feathersSync()],
    update: [feathersSync()],
    patch: [patchResponse(), feathersSync()],
    remove: [feathersSync()]
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
