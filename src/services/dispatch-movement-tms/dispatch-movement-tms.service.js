// Initializes the `dispatch-movement-tms` service on path `/dispatchMovementTms`
const { DispatchMovementTms } = require('./dispatch-movement-tms.class')
const hooks = require('./dispatch-movement-tms.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/dispatchMovementTms', new DispatchMovementTms(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('dispatchMovementTms')

  service.hooks(hooks)
}
