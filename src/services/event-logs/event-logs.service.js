// Initializes the `event-logs` service on path `/event-logs`
const { EventLogs } = require('./event-logs.class')
const createModel = require('../../models/event-logs.model')
const hooks = require('./event-logs.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/event-logs', new EventLogs(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('event-logs')

  app.post('/event-logs/bulk-insert', async (req, res) => {
    try {
      const result = await service.bulkInsert(req.body)
      res.json(result)
    } catch (error) {
      res.status(500).json({ error: error.message })
    }
  })

  service.hooks(hooks)
}
