// Initializes the `vehicle-drivers` service on path `/vehicle-drivers`
const { VehicleDrivers } = require('./vehicle-drivers.class')
const createModel = require('../../models/vehicle-drivers.model')
const hooks = require('./vehicle-drivers.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['patch', 'remove']
  }

  // Initialize our service with any options it requires
  app.use('/vehicle-drivers', new VehicleDrivers(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('vehicle-drivers')

  service.hooks(hooks)
}
