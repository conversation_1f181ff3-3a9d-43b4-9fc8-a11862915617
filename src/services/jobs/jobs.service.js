// Initializes the `jobs` service on path `/jobs`
const { Jobs } = require('./jobs.class')
const createModel = require('../../models/jobs.model')
const hooks = require('./jobs.hooks')
const logger = require('@hooks/logger')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$populate'],
    events: ['patched', 'created', 'removed'],
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/jobs', new Jobs(options, app))

  // Custom multi patch endpoint
  app.post('/jobs/multi-patch', async (req, res, next) => {
    try {
      const jobsService = app.service('jobs')
      const { jobs } = req.body
      const result = await jobsService.multiPatch(jobs)

      res.json(result)
    } catch (error) {
      next(error)
    }
  })

  // Get our initialized service so that we can register hooks
  const service = app.service('jobs')
  const LogPublish = () => async ({ data }, context) => {
    if (logger.isDebugEnabled()) {
      logger.debug('Receive the socket push', data)
      logger.debug('Method socket', context.method)
    }

    if (data.jobType === 'OET' && ['cancel', 'rejected'].indexOf(data.jobStatus) > -1 && (data.pickupLocation || data.deliveryLocation)) return []

    let users = []
    // get driver
    const getDriverId = await app.service('vehicles').find({ query: { vehicleNo: data.truckNumber, company: data.company, status: true, $select: 'driver' }, paginate: false })

    // get transporter
    users = await app.service('users').find({ query: { company: data.company, roleAcl: { $ne: app.initRoleAcl }, status: true, lastOnline: null, $select: '_id' }, paginate: false })
    users = users.map(v => v._id.toString())

    if (getDriverId[0]) users.push(getDriverId[0].driver.toString())

    const socketWillSend = [app.channel('admins')]
    const duplicateCheck = []

    try {
      for (const usr of users) {
        const socket = app.channel('authenticated').filter(connection => connection.user._id.toString() === usr)

        if (socket.connections.length > 0) {
          if (duplicateCheck.indexOf(usr) < 0) {
            socketWillSend.push(socket)
            duplicateCheck.push(socket.connections[0].user._id.toString())
          }
        }
      }
    } catch (e) {
      logger.error('Error occur on filter connection', e)
    }

    if (socketWillSend) {
      if (process.env.NODE_ENV === 'uat') {
        logger.info('user will receive', JSON.stringify(socketWillSend))
      }

      logger.info('job on company', data.company, data.text, socketWillSend.length)
    }

    return socketWillSend
  }

  service.publish('created', LogPublish())
  service.publish('patched', LogPublish())
  service.publish('removed', LogPublish())

  service.hooks(hooks)
}
