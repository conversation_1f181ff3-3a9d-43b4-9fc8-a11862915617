const { Service } = require('feathers-mongoose')
const _ = require('lodash')
const moment = require('moment')
const axios = require('axios')
const plimit = require('p-limit')
const { BadRequest } = require('@feathersjs/errors')
const logger = require('@hooks/logger')

const inTrip = {
  TripID: '',
  Cntr1: '',
  Seal1: '',
  Purp1: '',
  KD1: '',
  Nom1: '',
  Cntr2: '',
  Seal2: '',
  Purp2: '',
  KD2: '',
  Nom2: '',
  Cntr3: '',
  Purp3: '',
  KD3: '',
  Nom3: '',
  Cntr4: '',
  Purp4: '',
  KD4: '',
  Nom4: '',
  Trailer: '',
  TrailerSize: '',
  TrailerType: '',
  ActualSize: '',
  ULW: '',
  MLW: '',
  Status: '',
  PMNo: '',
  TTABT: '',
  TTAPPT: '',
  ContactNo: '',
  Text1: '',
  Text2: ''
}

exports.Jobs = class Jobs extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async multiPatch (jobsArray) {
    const limit = plimit(10)

    if (!Array.isArray(jobsArray) || jobsArray.length === 0) {
      throw new BadRequest('Payload must be a non-empty array of jobs to update.')
    }

    return Promise.all(jobsArray.map(({ id, ...changes }) => {
      if (!id) {
        throw new BadRequest('Each item needs an id')
      }

      return limit(() => this.patch(id, changes))
    }))
  }

  async patch (id, data) {
    logger.debug('paylod job ', data)

    const serviceAdaptorUrl = this.app.get('serviceAdaptorUrl')
    const job = await super.get(id)

    logger.debug('get data job ', job)

    // update job data
    await super.patch(job._id, data)

    if ((['in progress', 'start pickup', 'completed pickup', 'start delivery', 'completed delivery'].indexOf(job.jobStatus) !== -1) && (data.containerNumber || data.sealNumber || data.trailerNumber || (data.weight >= 0 || (data.weight !== '' && data.weight !== null && data.weight !== undefined))) && data.role) {
      if (job.source === 'OET') {
        await this.app.service('optetruck').create({
          jobId: job._id,
          cntrN: data.containerNumber || '',
          sealN: data.sealNumber || '',
          trailerRegN: data.trailerNumber || '',
          tareWeight: (data.weight >= 0 || (data.weight !== '' && data.weight !== null && data.weight !== undefined)) ? data.weight : ''
        })
      }

      const [vehicle] = await this.app.service('vehicles').find({
        query: {
          $limit: 1,
          vehicleNo: job.truckNumber,
          status: true
        },
        paginate: false
      })

      // get roleAcl driver
      const [{ roleAcl }] = await this.app.service('users').find({
        query: {
          $limit: 1,
          _id: vehicle.driver,
          $select: ['roleAcl']
        },
        paginate: false
      })

      // get role name
      const { name: roleName } = await this.app.service('roles-acl').get(data.role)

      const dataMessage = {
        company: vehicle.company,
        vehicle: vehicle._id,
        driver: vehicle.driver,
        sender: data.sender
      }

      if (job.ucrN) {
        dataMessage.content = `RELEASENO ${job.ucrN}`
      }

      if (data.containerNumber) {
        dataMessage.content = dataMessage.content ? dataMessage.content + ` CONTAINERNO ${data.containerNumber}` : `CONTAINERNO ${data.containerNumber}`
      }

      if (data.sealNumber) {
        dataMessage.content = dataMessage.content ? dataMessage.content + ` SEALNO ${data.sealNumber}` : `SEALNO ${data.sealNumber}`
      }

      if (data.trailerNumber) {
        dataMessage.content = dataMessage.content ? dataMessage.content + ` TRAILERNO ${data.trailerNumber}` : `TRAILERNO ${data.trailerNumber}`
      }

      if (data.weight >= 0 || (data.weight !== '' && data.weight !== null && data.weight !== undefined)) {
        dataMessage.content = dataMessage.content ? dataMessage.content + ` TAREWEIGHT ${data.weight}` : `TAREWEIGHT ${data.weight}`
      }

      dataMessage.content = `${roleAcl.toString() === data.role.toString() ? 'Driver' : roleName} update ${job.tripId || job.bookingN} ` + dataMessage.content

      await this.sendMessage(dataMessage, 'group')

      if (data.containerNumber && typeof data.combinedJobs === 'undefined') {
        await this.app.service('job-combine').checkUpdateCombine(job, data)
      }
    }

    if (!_.isUndefined(data.jobStatus)) {
      const jobStatusInProgress = 'in progress'
      const jobStatusCompleted = 'completed'
      const jobStatusReject = 'rejected'
      const jobStatusPickupDelivery = ['start pickup', 'completed pickup', 'start delivery', 'completed delivery']
      const dateNow = Date.now()
      const jobTypeMap = {
        IMP: 'Import',
        Import: 'IMP',
        EXP: 'Export',
        Export: 'EXP',
        Local: 'LOCAL',
        LOCAL: 'Local'
      }

      const jobType = jobTypeMap[job.jobType] || ''
      const inJobType = [job.jobType, 'DC', 'DR']

      if (jobType) inJobType.push(jobType)

      // handle if job status = 'in progress'
      if (data.jobStatus === jobStatusInProgress) {
        if (['DC', 'DR'].includes(job.jobType)) {
          // generate job start time
          await super.patch(job._id, {
            jobStartDate: dateNow
          })

          await this.createSBTTADocs(job)

          // write event logs
          const eventLogs = {
            action: 'jobs-started',
            data: job
          }

          await this.app.service('event-logs').create(eventLogs)
        } else if (job.source === 'OET') {
          await this.app.service('optetruck').create({ jobStatus: 'Started', jobId: job._id })

          // generate job start time
          if (job.tripId) {
            await super.patch(null, {
              jobStartDate: dateNow
            }, {
              query: {
                tripId: job.tripId,
                jobType: { $in: inJobType }
              }
            })
          } else {
            await super.patch(job._id, {
              jobStartDate: dateNow
            })
          }
        } else {
          if (job.source === 'OET') await this.app.service('optetruck').create({ jobStatus: 'Started', jobId: job._id })
          // generate job start time

          await super.patch(null, {
            jobStartDate: dateNow
          }, {
            query: {
              tripId: job.tripId,
              jobType: { $in: inJobType }
            }
          })
        }

        if (job.tripId) {
          // get related jobs
          const tripJobs = await super.find({
            query: {
              _id: {
                $ne: job._id
              },
              tripId: job.tripId,
              jobStatus: {
                $nin: ['cancel', 'completed']
              },
              jobType: { $in: inJobType },
              company: job.company
            },
            paginate: false
          })

          if (tripJobs.length > 0) {
            // update jobs status for related jobs
            for (let i = 0; i < tripJobs.length; i++) {
              await super.patch(tripJobs[i]._id, { jobStatus: jobStatusInProgress })

              if (['EXP', 'LOCAL', 'IMP'].includes(tripJobs[i].jobType)) {
                // add tta for auto started job
                try {
                  logger.info('patch related tta docs', jobStatusInProgress, tripJobs[i].truckNumber)

                  const gateLocs = await this.app.service('gate-locs').find({ paginate: false })

                  await this.app.service('tta-docs').create({
                    jobId: tripJobs[i]._id,
                    vehnum: tripJobs[i].truckNumber,
                    status: 'active',
                    msgcount: 0,
                    gates: gateLocs.map(gate => ({
                      gate: gate.gate,
                      inoutsidezone: 'OUT',
                      triggerstatus: 'N'
                    }))
                  })
                } catch (error) {
                  logger.error('Failed to patch related tta docs', error)
                }
              }

              if (tripJobs[i].source === 'OET') await this.app.service('optetruck').create({ jobStatus: 'Started', jobId: tripJobs[i]._id })
            }
          }

          // send inward message
          const timeNow = Date.now()
          const timeNowFormated = moment(timeNow).format('YYYY-MM-DD hh:mm:ss.SSS')
          const jsonBody = {
            type: 'trip',
            direction: 'in',
            text: '',
            out_hdr: null,
            out_trip: null,
            in_yard: null,
            in_hdr: {
              date_time: timeNowFormated,
              area_id: 'PN',
              source: job.truckNumber,
              source_hostname: 'MDT',
              destination: 'HCS',
              submit_time: timeNowFormated
            },
            job: null,
            in_trip: null
          }

          inTrip.Status = 'S'
          inTrip.TripID = job.tripId
          inTrip.PMNo = job.truckNumber

          jsonBody.in_trip = inTrip

          // send jsonBody to service adaptor
          await axios
            .post(serviceAdaptorUrl, jsonBody)
            .catch((error) => {
              logger.error('Oops! something when wrong ', error)
              // roll back job status to new
              this.rollBackJobStatus(job._id, job.tripId, data.jobStatus)
              const newError = new BadRequest('Error when communicate with service adaptor')

              throw newError
            })

          // block for send data to message service
          // get vehicle data
          const [vehicle] = await this.app.service('vehicles').find({
            query: {
              $limit: 1,
              vehicleNo: job.truckNumber
            },
            paginate: false
          })

          if (vehicle) {
            const messageText = 'Started trip ' + job.tripId
            const messageData = {
              company: vehicle.company,
              vehicle: vehicle._id,
              driver: vehicle.driver,
              content: messageText
            }

            // send message via messages service
            await this.sendMessage(messageData, 'psa-message')
          }
          // end block

          // write event logs
          const eventLogs = {
            action: 'jobs-started',
            data: jsonBody
          }

          await this.app.service('event-logs').create(eventLogs)
        }
      } else if (data.jobStatus === jobStatusCompleted) {
        try {
          if (['DC', 'DR'].includes(job.jobType)) {
            // generate job end time
            await super.patch(job._id, {
              jobEndDate: dateNow
            })

            this.updateSBTTADocs(job)

            // write event logs
            const eventLogs = {
              action: 'jobs-completed',
              data: job
            }

            await this.app.service('event-logs').create(eventLogs)
          } else if (job.source === 'OET') {
            await this.app.service('optetruck').create({ jobStatus: 'Completed', jobId: job._id })

            // generate job end time
            await super.patch(job._id, {
              jobEndDate: dateNow
            })
          } else {
            // generate job end time
            await super.patch(job._id, {
              jobEndDate: dateNow
            })
          }

          if (job.tripId) {
            // handle if job status = 'completed'
            const tripJobs = await super.find({
              query: {
                _id: {
                  $ne: job._id
                },
                tripId: job.tripId,
                jobStatus: {
                  $nin: ['cancel', 'rejected']
                },
                company: job.company
              },
              paginate: false
            })

            let allComplete = true

            if (tripJobs.length > 0) {
              for (let i = 0; i < tripJobs.length; i++) {
                if (tripJobs[i].jobStatus !== jobStatusCompleted) {
                  allComplete = false
                }
              }
            }

            if (allComplete) {
              // block for send data to message service
              // get vehicle data
              const [vehicle] = await this.app.service('vehicles').find({
                query: {
                  $limit: 1,
                  vehicleNo: job.truckNumber
                },
                paginate: false
              })

              if (vehicle) {
                const messageText = 'Completed trip ' + job.tripId
                const messageData = {
                  company: vehicle.company,
                  vehicle: vehicle._id,
                  driver: vehicle.driver,
                  content: messageText
                }

                // send message via messages service
                await this.sendMessage(messageData, 'psa-message')
              }
              // end block
            }

            const portnetJobs = await super.find({
              query: {
                _id: {
                  $ne: job._id
                },
                tripId: job.tripId,
                jobStatus: {
                  $nin: ['cancel', 'rejected']
                },
                company: job.company,
                source: { $ne: 'OET' }
              },
              paginate: false
            })

            let allCompletePortnet = true

            if (portnetJobs.length > 0) {
              for (let i = 0; i < portnetJobs.length; i++) {
                if (portnetJobs[i].jobStatus !== jobStatusCompleted) {
                  allCompletePortnet = false
                }
              }
            }

            // send inward message
            if (allCompletePortnet && job.source !== 'OET') {
              const timeNow = Date.now()
              const timeNowFormated = moment(timeNow).format('YYYY-MM-DD hh:mm:ss.SSS')
              const jsonBody = {
                type: 'trip',
                direction: 'in',
                text: '',
                out_hdr: null,
                out_trip: null,
                in_yard: null,
                in_hdr: {
                  date_time: timeNowFormated,
                  area_id: 'PN',
                  source: job.truckNumber,
                  source_hostname: 'MDT',
                  destination: 'HCS',
                  submit_time: timeNowFormated
                },
                in_trip: null,
                job: null
              }

              inTrip.Status = 'C'
              inTrip.TripID = job.tripId
              inTrip.PMNo = job.truckNumber

              jsonBody.in_trip = inTrip

              const self = this
              // send jsonBody to service adaptor
              await axios.post(serviceAdaptorUrl, jsonBody)
                .catch(function (error) {
                  logger.error('Oops! something wrong ', error)
                  // rollback status job to in progress
                  self.rollBackJobStatus(job._id, job.tripId, data.jobStatus)
                  const newError = new BadRequest('Error when communicate with service adaptor')
                  throw newError
                })

              // write event logs
              const eventLogs = {
                action: 'jobs-completed',
                data: jsonBody
              }

              await this.app.service('event-logs').create(eventLogs)
            }
          }

          if (job.source === 'OET') await this.app.service('job-combine').finalCombine(job, data)
        } catch (error) {
          throw new Error('Complete job failed:' + error)
        }
      } else if (data.jobStatus === jobStatusReject) {
        await this.app.service('job-combine').finalCombine(job, data)

        if (job.source === 'OET') {
          await this.app.service('optetruck').create({ jobStatus: 'Rejected', jobId: job._id })
        }

        const [vehicle] = await this.app.service('vehicles').find({
          query: {
            $limit: 1,
            vehicleNo: job.truckNumber
          },
          paginate: false
        })

        if (vehicle) {
          let arrContainer = ' '

          if (job.jobType === 'DC') { arrContainer += job.ucrN } else if (job.jobType === 'DR') { arrContainer += job.containerNumber }

          if (arrContainer.length !== 1) {
            await this.app.service('notifications').create({
              type: 'vehicle-dispatch',
              channel: 'push',
              to: vehicle.driver,
              description: `Your SmartBooking job for${arrContainer} is rejected.`
            }).catch(() => null)
          }
        }
      } else if (jobStatusPickupDelivery.indexOf(data.jobStatus) !== -1) {
        if (job.source === 'OET') await this.app.service('optetruck').create({ jobStatus: data.jobStatus, jobId: job._id })
        if (data.jobStatus === 'completed pickup' && job.jobType.toLowerCase() === 'import') await this.app.service('job-combine').finalCombine(job, data)
      }
    }

    return job
  }

  async sendMessage (data, type) {
    const msgData = {
      type,
      flagFrom: 'inward',
      company: data.company,
      vehicle: data.vehicle,
      messages: {
        type: 'text',
        sender: data.driver,
        content: data.content
      }
    }

    if (type === 'group') {
      msgData.messages.sender = data.sender
      msgData.flagFrom = 'normal'
    }

    if (type === 'psa-message') {
      const [conversation] = await this.app.service('conversations').find({
        query: {
          $limit: 1,
          type,
          vehicle: data.vehicle,
          company: data.company
        },
        paginate: false
      })

      if (conversation) {
        msgData.conversationId = conversation._id
      }
    }

    logger.info('Logged message data to make sure send message payload is correct.', msgData)

    await this.app.service('messages').create(msgData)
  }

  // function for roll back status if failed connect to service adaptor
  async rollBackJobStatus (jobId, tripId, status) {
    let jobStatusNotUpdated = ''
    let jobStatusWillUpdateTo = ''
    let tripJobs = null
    const getJob = await super.get(jobId)

    if (status === 'in progress') {
      jobStatusNotUpdated = 'completed'
      jobStatusWillUpdateTo = 'new'
      tripJobs = await super.find({
        query: {
          tripId,
          jobStatus: {
            $ne: jobStatusNotUpdated
          },
          company: getJob.company
        },
        paginate: false
      })
    } else if (status === 'completed') {
      jobStatusWillUpdateTo = 'in progress'
      tripJobs = await super.find({
        query: {
          _id: jobId
        },
        paginate: false
      })
    }

    if (tripJobs.length > 0) {
      // update the jobs status to new
      for (let i = 0; i < tripJobs.length; i++) {
        await super.patch(tripJobs[i]._id, { jobStatus: jobStatusWillUpdateTo })
      }
    }
  }

  async createSBTTADocs (job) {
    logger.debug('Create SB-TTA Docs...', job)

    try {
      const { data: response } = await axios.post(`${this.app.get('sb').baseUrl}/sb-tta-docs`, {
        jobId: job._id,
        bookingN: job.bookingN,
        vehicleN: job.truckNumber,
        status: 'active-far',
        destination: job.depotC
      })

      logger.debug('SB-TTA Docs has been created', response)

      return response
    } catch (error) {
      logger.error('Failed to create TTA docs', error)
    }
  }

  async updateSBTTADocs (job) {
    logger.debug('Update SB-TTA Docs...', job)

    try {
      const { data: response } = await axios.post(`${this.app.get('sb').baseUrl}/sb-tta-docs/complete-job`, {
        jobId: job._id,
        status: 'completed'
      })

      logger.debug('SB-TTA Docs has been updated', response)

      return response
    } catch (error) {
      logger.error('Failed to update TTA docs', error)
    }
  }
}
