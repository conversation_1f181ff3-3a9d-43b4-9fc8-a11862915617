const logger = require('@hooks/logger')
const { BadRequest } = require('@feathersjs/errors')
const moment = require('moment')
const authenticateOrWhitelist = require('@hooks/authenticate-or-whitelist')

const patchResponse = () => async ctx => {
  const data = { ...ctx.data }

  if (!ctx.data.truckNumber) ctx.data.truckNumber = ctx.result.truckNumber
  if (!ctx.data.company) ctx.data.company = ctx.result.company
  if (!ctx.data.text) ctx.data.text = ctx.result.text
  if (!ctx.data._id) ctx.data._id = ctx.id
  if (!ctx.data.jobStatus) ctx.data.jobStatus = ctx.result.jobStatus
  if (!ctx.data.source) ctx.data.source = ctx.result.source
  if (!ctx.data.jobType) ctx.data.jobType = ctx.result.jobType

  // Emit 'patched' event with the updated data
  await ctx.app.service('jobs').emit('patched', { data: ctx.data })

  if (['IMP', 'EXP', 'LOCAL', undefined].includes(ctx.data.jobType) && data.jobStatus) {
    logger.info('patch tta docs', data)
    try {
      switch (data.jobStatus) {
        case 'in progress': {
          logger.info('patch tta docs', data.jobStatus, ctx.data.truckNumber)
          const gateLocs = await ctx.app.service('gate-locs').find({ paginate: false })
          await ctx.app.service('tta-docs').create({
            jobId: ctx.id,
            vehnum: ctx.data.truckNumber,
            status: 'active',
            msgcount: 0,
            gates: gateLocs.map(gate => ({
              gate: gate.gate,
              inoutsidezone: 'OUT',
              triggerstatus: 'N'
            }))
          })
        }
          break
        case 'completed':
          logger.info('patch tta docs', data.jobStatus, ctx.data.truckNumber)
          await ctx.app.service('tta-docs').patch(null, { status: 'completed' }, { query: { jobId: ctx.id, vehnum: ctx.data.truckNumber } })
          break
        case 'cancel':
        case 'rejected':
        case 'withdrawn':
          logger.info('patch tta docs', data.jobStatus, ctx.data.truckNumber)
          await ctx.app.service('tta-docs').patch(null, { status: 'inactive' }, { query: { jobId: ctx.id, vehnum: ctx.data.truckNumber } })
          break
        default:
          // Default case to do nothing
          break
      }
    } catch (error) {
      logger.error('Error patching TTA docs', error)
    }
  }
}

const createResponse = () => ctx => {
  if (ctx.result.length) {
    ctx.result.forEach(res => {
      ctx.app.service('jobs').emit('created', { data: res })
    })
  } else {
    ctx.app.service('jobs').emit('created', { data: ctx.result })
  }
}

const removeResponse = () => ctx => {
  ctx.app.service('jobs').emit('removed', { data: ctx.result })
}

const pushNotif = () => async ctx => {
  if (ctx.result.source === 'OET' && !ctx.result.area_id) {
    const vehicles = await ctx.app.service('vehicles').find({
      query: {
        vehicleNo: ctx.result.truckNumber,
        company: ctx.result.company,
        $select: ['driver']
      },
      paginate: false
    })

    const notification = ctx.method === 'create' ? 'You have new OptETruck job for' : ctx.method === 'remove' ? 'Your OptETruck job cancelled for' : (ctx.data && ctx.data.jobStatus) === 'rejected' ? 'Your OptETruck job rejected for' : 'Your OptETruck job updated for'

    if (ctx.result && (ctx.result.containerNumber || ctx.result.tripId)) {
      let send = true
      let isDriver

      if (ctx.params.user) isDriver = await ctx.app.service('roles-acl').find({ paginate: false, query: { _id: ctx.params.user.roleAcl, $select: ['name'] } })

      const status = (ctx.params.user && isDriver[0].name !== 'Transporter - Driver') ? true : !ctx.params.user

      send = (ctx.method === 'patch' && status && ctx.data && (ctx.data.containerNumber || ctx.data.sealNumber || (ctx.data.weight >= 0 && (ctx.data.weight !== '' && ctx.data.weight !== null && ctx.data.weight !== undefined)) || ctx.data.trailerNumber || ctx.data.pickupLocation || ctx.data.deliveryLocation) && !ctx.data.jobStatus) ? true : ctx.method !== 'patch'

      if (ctx.data && ctx.data.jobStatus === 'rejected') send = true

      if (send) {
        await ctx.app.service('notifications').create({
          type: ctx.method === 'create' ? 'job-dispatched' : ctx.method === 'remove' ? 'job-cancelled' : (ctx.data && ctx.data.jobStatus) === 'rejected' ? 'job-rejected' : 'job-updated',
          channel: 'push',
          to: vehicles[0].driver,
          description: `${notification} ${ctx.result.containerNumber || ctx.result.tripId}.`
        }).catch((err) => logger.error('Something wrong with push notification after modify job', err.stack))
      }
    }
  }
}

const limitDate = () => ctx => {
  const queryDate = ['jobEndDate', 'jobStartDate', 'createdAt']
  let keyQuery = Object.keys(ctx.params.query || {})
  const orKey = keyQuery.includes('$or') ? ctx.params.query.$or.map(v => Object.keys(v)[0]) : []

  keyQuery = orKey.length ? keyQuery.concat(orKey) : keyQuery

  if (keyQuery.filter(v => queryDate.includes(v)).length > 0) {
    ctx.params.query.createdAt = {
      $gt: moment().subtract(30, 'days').startOf('day').toDate()
    }
  }

  return ctx
}

const updateJobState = () => ctx => {
  switch (ctx.data.jobStatus) {
    case 'new':
      ctx.data.jobState = 0
      break
    case 'in progress':
      ctx.data.jobState = 1
      break
    case 'start pickup':
      ctx.data.jobState = 2
      break
    case 'completed pickup':
      ctx.data.jobState = 3
      break
    case 'start delivery':
      ctx.data.jobState = 4
      break
    case 'completed delivery':
      ctx.data.jobState = 5
      break
    case 'cancel':
      ctx.data.jobState = 6
      break
    case 'rejected':
      ctx.data.jobState = 7
      break
    case 'completed':
      ctx.data.jobState = 8
      break
    case 'withdrawn':
      ctx.data.jobState = 9
      break
    case 'not dispatched':
      ctx.data.jobState = 10
      break
    default:
      break
  }

  return ctx
}

const combineJobs = () => async ctx => {
  if (ctx.data && ctx.data.jobStatus === 'in progress' && ctx.data.jobStatusOld && typeof ctx.data.combinedJobs === 'undefined' && (ctx.data.tripId || ctx.data.bookingN)) {
    await ctx.app.service('job-combine').create(ctx.data)

    return ctx
  }
}

const jobCounter = () => async ctx => {
  const { tripId, containerNumber, truckNumber, company } = ctx.data
  const lessDate = moment().subtract(2, 'days').seconds(0).utc(true).toDate()
  const queryNewTripJob = {
    $limit: 1,
    truckNumber,
    jobStatus: {
      $in: ['in progress', 'new']
    },
    company,
    createdAt: { $gte: lessDate },
    containerNumber,
    tripId
  }

  const getJob = await ctx.app.service('jobs').find({
    query: queryNewTripJob,
    paginate: false
  })

  if (!getJob[0] && tripId && containerNumber && truckNumber) {
    await ctx.app.redis.sAdd(`jobCounter:${truckNumber}:${containerNumber}:${tripId}`, '1').then(async res => {
      if (!res) {
        const lessDate = moment().subtract(2, 'days').seconds(0).utc(true).toDate()
        const queryNewTripJob = {
          $limit: 1,
          truckNumber,
          jobStatus: {
            $in: ['in progress', 'new']
          },
          company,
          createdAt: { $gte: lessDate },
          containerNumber
        }
        const getJobData = await ctx.app.service('jobs').find({
          query: queryNewTripJob,
          paginate: false
        })

        if (getJobData[0]) await ctx.app.service('job').patch(getJobData[0]._id, ctx.data)

        return false
      } else {
        await ctx.app.redis.EXPIRE(`jobCounter:${truckNumber}:${containerNumber}:${tripId}`, 86400)
      }
    })
  }

  return ctx
}

const checkStartImp = () => async ctx => {
  const dataJob = await ctx.app.service('jobs').get(ctx.id)

  if (dataJob.jobType === 'IMP' && ctx.data && ctx.data.jobStatus === 'in progress') {
    const checkExp = await ctx.app.service('jobs').find({
      query: {
        tripId: dataJob.tripId,
        jobStatus: {
          $nin: ['completed', 'rejected', 'cancel']
        },
        jobType: 'EXP'
      }
    })

    if (checkExp.total > 0) throw new BadRequest('Please complete the jobs for export containers first.')
  }
}

module.exports = {
  before: {
    all: [authenticateOrWhitelist()],
    find: [limitDate()],
    get: [limitDate()],
    create: [jobCounter()],
    update: [],
    patch: [updateJobState(), checkStartImp(), async ctx => {
      if (ctx.params.user) {
        ctx.data.role = ctx.params.user.roleAcl
        ctx.data.sender = ctx.params.user._id
      }

      const dataJob = await ctx.app.service('jobs').get(ctx.id)

      logger.debug('data job before patch', dataJob)

      if (ctx.data.jobStatus === 'in progress' && dataJob.jobStatus !== 'new') {
        const newError = new BadRequest('Start job available for new job only.')

        throw newError
      }

      const statusInProgress = ['in progress', 'start pickup', 'completed pickup', 'start delivery', 'completed delivery']
      const completeErr = new BadRequest('Complete job available for in progress job only.')
      const sberr = new BadRequest('Complete job is not allowed, vehicle has not arrived at the depot.')

      if (ctx.data && ctx.data.jobStatus === 'completed') {
        // NEW
        if (statusInProgress.indexOf(dataJob.jobStatus) === -1) {
          throw completeErr
        }

        if ((dataJob && dataJob.bookingN) && ['1OUT', '2IN'].includes(dataJob.haulierMovementC) && dataJob.source !== 'OET') {
          throw sberr
        }
      }

      if (ctx.data && ctx.data.haulierMovementC) {
        if (ctx.data.haulierMovementC === '1IN') {
          if (statusInProgress.indexOf(dataJob.jobStatus) === -1) {
            const newError = new BadRequest('Entering depot failed, job status not in progress.')

            throw newError
          }
        } else if (ctx.data.haulierMovementC === '1OUT' || ctx.data.haulierMovementC === '2OUT') {
          if (statusInProgress.indexOf(dataJob.jobStatus) === -1) {
            const newError = new BadRequest('Exiting depot failed, job status not in progress.')

            throw newError
          }
        }
      }

      if (['withdrawn', 'cancel', 'completed', 'rejected'].includes(dataJob.jobStatus)) {
        if (ctx.data && ctx.data.jobStatus) {
          const newError = new BadRequest(`Can't update job status, current status is ${dataJob.jobStatus}`)

          throw newError
        }
      }
    }],
    remove: []
  },

  after: {
    all: [],
    find: [async ctx => {
      if (typeof ctx.result.data !== 'undefined') {
        await ctx.result.data.map(val => {
          if (!val.survey) val.survey = 'NO'

          return val
        })
      }

      return ctx
    }],
    get: [async ctx => {
      if (!ctx.result.survey) ctx.result.survey = 'NO'

      return ctx
    }],
    create: [pushNotif(), createResponse()],
    update: [],
    patch: [async ctx => {
      if (ctx.params.user) {
        const data = ctx.data
        data._id = ctx.result._id
        if (ctx.result.bookingN) {
          if (!data.bookingN) data.bookingN = ctx.result.bookingN
        } else if (ctx.result.tripId) {
          if (!data.tripId) data.tripId = ctx.result.tripId
        }
        const isDriver = await ctx.app.service('roles-acl').find({ paginate: false, query: { _id: ctx.params.user.roleAcl } })
        if (isDriver[0].name !== 'Transporter - Driver') {
          if (data && data.jobStatus !== 'rejected') {
            await ctx.app.service('activity-logs').create({
              user: (ctx.params.user && ctx.params.user._id) || null,
              action: ctx.method,
              module: 'job',
              data
            }).catch(err => logger.error('Something wrong with create activity after patch job', err.stack))
          }

          if ((data.containerNumber || data.sealNumber || data.trailerNumber) && !data.jobStatus && (ctx.result.jobType === 'DC' || ctx.result.jobType === 'DR')) {
            if (ctx.result.truckNumber) {
              const vehicles = await ctx.app.service('vehicles').find({
                query: {
                  vehicleNo: ctx.result.truckNumber,
                  company: ctx.result.company
                },
                paginate: false
              })

              await ctx.app.service('notifications').create({
                type: 'jobs-updated',
                channel: 'push',
                to: vehicles[0].driver,
                description: `Your SmartBooking job for ${ctx.result.containerNumber} is updated.`
              }).catch((err) => logger.error('Something wrong with push notification after patch job', err.stack))
            }
          }
        } else if (!data.jobStatus) {
          await ctx.app.service('activity-logs').create({
            user: (ctx.params.user && ctx.params.user._id) || null,
            action: ctx.method,
            module: 'job',
            data
          }).catch(err => logger.error('Something wrong with create activity after patch job', err.stack))
        }

        data._id = ctx.result._id
        data.tripId = ctx.result.tripId
        data.jobStatusOld = ctx.result.jobStatus
        data.containerNumber = (ctx.data && ctx.data.containerNumber) ? ctx.data.containerNumber : (ctx.result.containerNumber) ? ctx.result.containerNumber : ctx.result.batchNumber
        data.truckNumber = ctx.result.truckNumber
        if (data.jobStatus === 'rejected') {
          await ctx.app.service('activity-logs').create({
            user: (ctx.params.user && ctx.params.user._id) || null,
            action: ctx.method,
            module: 'job',
            data
          }).catch(err => logger.error('Error occur on create activity log rejected job ', err.stack))

          if (ctx.params.user && !['ios', 'android'].includes(ctx.params.user.device.os) && data.containerNumber && ctx.result && ctx.result.area_id) {
            const vehicles = await ctx.app.service('vehicles').find({
              query: {
                vehicleNo: data.truckNumber,
                company: ctx.result.company
              },
              paginate: false
            })

            const msgData = {
              type: 'private',
              company: vehicles[0].company,
              members: [vehicles[0].driver],
              messages: {
                type: 'job-rejected',
                content: data.containerNumber
              }
            }
            await ctx.app.service('messages').create(msgData)
          }
        }
      }
    }, pushNotif(), patchResponse(), combineJobs()],
    remove: [pushNotif(), removeResponse()]
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
