const ObjectId = require('mongoose').Types.ObjectId
const { NotAcceptable, MethodNotAllowed } = require('@feathersjs/errors')
const msg = require('@utils/commonMsg')
const logger = require('@hooks/logger')

/* eslint-disable no-unused-vars */
exports.UsersRestricted = class UsersRestricted {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async find (params) {
    const arrObjectId = ['_id', 'company', 'role', 'roleAcl']
    const arrRegex = ['fullname', 'username']
    const paramsQuery = {
      query: {
        $or: [{ company: { $exists: true } }],
        $populate: [{ path: 'company', select: ['_id', 'name', 'status'] }, { path: 'role', select: ['_id', 'name'] }],
        $select: ['_id', 'fullname', 'username', 'status', 'company', 'role'],
        $sort: { fullname: 1 }
      },
      user: params.user
    }

    if (params.user.company) {
      paramsQuery.query.company = { $in: params.user.company }
      if (params.query && params.query.company) delete params.query.company
    }

    if (params.query) {
      if (Number(params.query.$limit) === -1) paramsQuery.paginate = false
      else if (params.query.$limit) paramsQuery.query.$limit = Number(params.query.$limit)

      if (params.query.$skip) paramsQuery.query.$skip = Number(params.query.$skip)

      if (params.query.status && (params.query.status === 'true' || params.query.status === 'false')) paramsQuery.query.status = params.query.status === 'true'

      Object.keys(params.query).forEach(key => {
        if (!arrRegex.includes(key) && !arrObjectId.includes(key) && key !== '$limit' && key !== '$skip' && key !== 'status') throw new MethodNotAllowed('Invalid query command')
        if (arrObjectId.includes(key)) {
          if (!ObjectId.isValid(params.query[key])) throw new NotAcceptable(msg[`${key.toUpperCase()}_IS_OBJECT_ID`])
          paramsQuery.query[key] = { $in: params.query[key] }
        }
        if (arrRegex.includes(key)) paramsQuery.query[key] = { $regex: params.query[key], $options: 'i' }
      })
    }

    try {
      const users = await this.app.service('users').find(paramsQuery)

      return users
    } catch (e) {
      logger.error(e)
    }
  }

  async get (id, params) {
    try {
      const user = await this.app.service('users').get(id, {
        query: {
          $or: [{ company: { $exists: true } }],
          $populate: [{ path: 'company', select: ['_id', 'name', 'status'] }, { path: 'role', select: ['_id', 'name'] }],
          $select: ['_id', 'fullname', 'username', 'status', 'company', 'role']
        },
        user: params.user
      })

      return user
    } catch (e) {
      logger.error(e)
    }
  }
}
