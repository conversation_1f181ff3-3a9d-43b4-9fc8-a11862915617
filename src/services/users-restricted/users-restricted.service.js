// Initializes the `users-restricted` service on path `/users-restricted`
const { UsersRestricted } = require('./users-restricted.class')
const hooks = require('./users-restricted.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/usersRestricted', new UsersRestricted(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('usersRestricted')

  service.hooks(hooks)
}
