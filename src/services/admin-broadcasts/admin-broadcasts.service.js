// Initializes the `admin-broadcasts` service on path `/messages/broadcast`
const { AdminBroadcasts } = require('./admin-broadcasts.class')
const hooks = require('./admin-broadcasts.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/broadcasts', new AdminBroadcasts(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('broadcasts')

  service.hooks(hooks)
}
