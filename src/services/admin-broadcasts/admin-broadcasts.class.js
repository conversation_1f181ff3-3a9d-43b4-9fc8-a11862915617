/* eslint-disable no-unused-vars */
const { ObjectId } = require('mongoose').Types
const _ = require('lodash')

exports.AdminBroadcasts = class AdminBroadcasts {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async find (params) {
    const broadcastConv = await this.getConversation()
    if (!broadcastConv) return []

    const stages = []
    const { $sort, $limit, $skip, ...query } = params.query
    query.conversationId = broadcastConv._id

    if (Object.keys(query).length) {
      const getQuery = this.app.service('messages').Model.findOne(query)
      await getQuery.exec().then(res => res)

      const filteredQuery = getQuery.getFilter()
      stages.push({ $match: filteredQuery })
    }

    if (typeof $sort !== 'undefined') {
      Object.keys($sort).forEach(v => {
        $sort[v] = parseFloat($sort[v])
      })

      stages.push({ $sort })
    }

    if (typeof $skip !== 'undefined') stages.push({ $skip: parseFloat($skip) })

    stages.push({
      $limit: parseFloat($limit || this.app.get('paginate').default)
    })

    const totalMessages = await this.app.service('messages').find({ query })
      .then(res => res.total)
    const messages = await this.app.service('messages').Model.aggregate([
      ...stages,
      {
        $lookup: {
          from: 'users',
          localField: 'sender',
          foreignField: '_id',
          as: 'sender'
        }
      },
      {
        $unwind: {
          path: '$sender', preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'createdBy'
        }
      },
      {
        $unwind: {
          path: '$createdBy', preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          _id: '$_id',
          content: '$content',
          conversationId: '$conversationId',
          draft: '$draft',
          sender: '$sender',
          createdBy: '$createdBy',
          createdAt: '$createdAt',
          sentAt: '$sentAt'
        }
      }
    ])

    return { data: messages, total: totalMessages }
  }

  async create (data, params) {
    const conversationId = await this.getConversation()

    if (data.messages && data.messages.draft) delete data.messages.sender

    return this.app.service('messages').create({
      type: 'admin-broadcast',
      conversationId,
      messages: data.messages,
      flagFrom: data.flagFrom
    })
  }

  async patch (id, data, params) {
    if (typeof data.draft !== 'undefined' && data.draft) delete data.sender

    const message = await this.app.service('messages').patch(ObjectId(id), data)

    return this.app.service('messages').emit('submitted', message)
  }

  async getConversation () {
    const [conversation] = await this.app.service('conversations').find({
      paginate: false,
      query: {
        type: 'admin-broadcast'
      }
    })

    if (!conversation) return null
    return conversation
  }
}
