// Initializes the `sb-entryexit` service on path `/sb-entryexit`
const { SbEntryexit } = require('./sb-entryexit.class')
const hooks = require('./sb-entryexit.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/sb-entryexit', new SbEntryexit(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('sb-entryexit')

  service.hooks(hooks)
}
