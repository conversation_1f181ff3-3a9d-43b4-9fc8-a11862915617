const axios = require('axios')
const { BadRequest, Forbidden, GeneralError, NotAuthenticated, NotFound } = require('@feathersjs/errors')
const logger = require('@hooks/logger')

exports.SbEntryexit = class SbEntryexit {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async create (data) {
    try {
      const { data: rest } = await axios.post(`${this.app.get('sb').baseUrl}/sb-entryexit`, data)

      logger.debug(`SmartBookings (/sb-entryexit) response: ${JSON.stringify(rest)}`)

      return rest
    } catch (error) {
      const status = error.response?.status || 500
      const message = error.response?.data?.message || error.message || 'Unknown error'

      logger.error(`Error from /sb-entryexit: [${status}] ${message}`)

      switch (status) {
        case 400:
          throw new BadRequest(message)
        case 401:
          throw new NotAuthenticated(message)
        case 403:
          throw new Forbidden(message)
        case 404:
          throw new NotFound(message)
        default:
          throw new GeneralError(message)
      }
    }
  }
}
