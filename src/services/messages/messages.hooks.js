const { authenticate } = require('@feathersjs/authentication').hooks
const moment = require('moment')
const logger = require('@hooks/logger')

const removeHtmlTag = () => async ctx => {
  const flagFrom = (ctx.result.total > 0) ? ctx.result.data[0].flagFrom : ''
  if (ctx.result.total > 0 && flagFrom === 'broadcast') {
    ctx.result.data.forEach((item, index) => {
      item.content = item.content.replace(/<(?!br|div|b|i|u|strike|\/div|\/b|\/i|\/u|\/strike\s*\/?)[^>]+>/g, '')
    })
  }
}

const createResponse = () => ctx => {
  ctx.result = {
    flagFrom: ctx.result.flagFrom,
    conversationId: ctx.result.conversationId,
    conversations: ctx.result.conversations,
    content: ctx.result.content,
    sender: ctx.result.sender,
    recipientStatus: ctx.result.recipientStatus,
    type: ctx.result.type,
    createdAt: ctx.result.createdAt,
    updatedAt: ctx.result.updatedAt,
    isSent: ctx.result.isSent,
    _id: ctx.result._id,
    isDriver: ctx.result.isDriver,
    draft: ctx.result.draft
  }

  return ctx
}

const patchResponse = () => ctx => {
  if (Array.isArray(ctx.result)) {
    ctx.result = ctx.result.map(item => {
      return {
        flagFrom: item.flagFrom,
        conversationId: item.conversationId,
        conversations: item.conversations,
        content: item.content,
        sender: item.sender,
        recipientStatus: item.recipientStatus,
        type: item.type,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        isSent: item.isSent,
        _id: item._id,
        isDriver: item.isDriver,
        draft: item.draft
      }
    })
  } else {
    ctx.result = {
      flagFrom: ctx.result.flagFrom,
      conversationId: ctx.result.conversationId,
      conversations: ctx.result.conversations,
      content: ctx.result.content,
      sender: ctx.result.sender,
      recipientStatus: ctx.result.recipientStatus,
      type: ctx.result.type,
      createdAt: ctx.result.createdAt,
      updatedAt: ctx.result.updatedAt,
      isSent: ctx.result.isSent,
      _id: ctx.result._id,
      isDriver: ctx.result.isDriver,
      draft: ctx.result.draft
    }
  }

  return ctx
}

module.exports = {
  before: {
    all: [
      authenticate('jwt')
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [async ctx => {
      try {
        const queryParam = ctx.params.query
        if (queryParam) {
          // due to heavy query must remove to make system not down
          if (queryParam.recipientStatus && queryParam.recipientStatus.$not) {
            const msgArray = []
            const startDate = moment().subtract(2, 'days').seconds(0).utc(true).toDate()
            const endDate = moment().toDate()
            delete queryParam.recipientStatus
            queryParam.createdAt = { $gte: startDate, $lt: endDate }
            const seachData = await ctx.app.service('messages').find({ query: queryParam })
            if (seachData && seachData.data) {
              seachData.data.forEach(msg => {
                let readBf = false
                if (msg.recipientStatus) {
                  msg.recipientStatus.forEach(rcp => {
                    const tmpId = queryParam.sender.$ne.toString()
                    const rcptmpId = rcp.user.toString()
                    if (tmpId === rcptmpId) {
                      readBf = true
                    }
                  })
                }
                if (!readBf) {
                  msgArray.push(msg._id)
                }
              })
            }
            if (msgArray && msgArray.length > 0) {
              ctx.params.query._id = { $in: msgArray }
            } else {
              ctx.params.query.content = '8888CDAS8888'
            }
          }
        }
      } catch (e) {
        logger.error('Error occur on temporarily handling', e)
        ctx.params.query.content = '8888CDAS8888'
      }
    }],
    remove: []
  },

  after: {
    all: [async ctx => {
      if (ctx.result) {
        if (Array.isArray(ctx.result)) {
          await Promise.all(ctx.result.map(async v => {
            if (v.conversationId) v.conversations = await ctx.app.service('conversations').get(v.conversationId).catch(() => null)
            if (v.sender) v.sender = await ctx.app.service('users').get(v.sender).catch(() => null)

            return v
          }))
        } else {
          if (ctx.result.conversationId) ctx.result.conversations = await ctx.app.service('conversations').get(ctx.result.conversationId).catch(() => null)
          if (ctx.result.sender) ctx.result.sender = await ctx.app.service('users').get(ctx.result.sender).catch(() => null)
        }
      }
      return ctx
    }],
    find: [removeHtmlTag()],
    get: [],
    create: [async ctx => {
      if (ctx.result.sender) {
        ctx.result.isDriver = await ctx.app.service('vehicles').find({
          query: { driver: ctx.result.sender._id }
        }).then(res => !!(res.total))
      } else {
        ctx.result.isDriver = false
      }

      return ctx
    }, createResponse()],
    update: [],
    patch: [async ctx => {
      if (ctx.result && Array.isArray(ctx.result)) {
        await Promise.all(ctx.result.map(async v => {
          if (v.sender) {
            v.isDriver = await ctx.app.service('vehicles').find({
              query: { driver: v.sender._id }
            }).then(res => !!(res.total))
          } else {
            v.isDriver = false
          }

          return v
        }))
      }
    }, patchResponse()],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
