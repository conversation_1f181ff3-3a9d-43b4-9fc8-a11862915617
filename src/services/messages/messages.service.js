// Initializes the `messages` service on path `/messages`
const { Messages } = require('./messages.class')
const createModel = require('../../models/messages.model')
const hooks = require('./messages.hooks')
const _ = require('lodash')
const { authenticate } = require('@feathersjs/authentication').hooks
const logger = require('@hooks/logger')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$exists', '$elemMatch', '$not', '$regex', '$options', '$populate'],
    events: ['read', 'submitted'],
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/messages', new Messages(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('messages')

  app.use('/messages/vehicle/:id', {
    async find (params) {
      const id = params.route.id

      if (!id) {
        logger.error('Vehicle id is required to find messages by vehicle')
      }

      const messages = await service.findVehicleMessages(id, params)

      return messages
    },
    async get (id, params) {
      const messages = await service.findVehicleMessages(id, params)

      return messages
    }
  })

  app.service('messages/vehicle/:id').hooks({
    before: {
      all: [authenticate('jwt')]
    },
    after: {
      find: [async ctx => {
        await Promise.all(ctx.result.map(async v => {
          if (v.senderId && v.sender !== 'CTR') {
            v.isDriver = await ctx.app.service('vehicles').find({
              query: { driver: v.senderId }
            }).then(res => !!(res.total))
          } else if (v.flagFrom === 'inward') {
            v.isDriver = true
          } else {
            v.isDriver = false
          }

          return v
        }))
      }],
      get: [async ctx => {
        await Promise.all(ctx.result.map(async v => {
          if (v.senderId && v.sender !== 'CTR') {
            v.isDriver = await ctx.app.service('vehicles').find({
              query: { driver: v.senderId }
            }).then(res => !!(res.total))
          } else if (v.flagFrom === 'inward') {
            v.isDriver = true
          } else {
            v.isDriver = false
          }

          return v
        }))
      }]
    }
  })

  // route for sending messages to multiple vehicles
  app.use('/send-message/vehicles', {
    async create (data) {
      const multipleVehicles = await service.sendMultipleVehicle(data)
      return multipleVehicles
    }
  })

  service.hooks(hooks)

  service.on('created', message => {
    logger.info(`Message from ${(message.sender && message.sender.fullname) || 'Unknown'} is sent`)
  })

  const messageChannel = () => async (data, context) => {
    logger.debug('Received message to push', data)

    if (['inward', 'ctr-message'].includes(data.flagFrom)) {
      return app.channel('admins')
    }

    const members = []
    let driver = null
    const conversations = await app.service('conversations').get(data.conversationId)

    if (
      (typeof data.draft !== 'undefined' && data.draft) ||
      (conversations.type === 'admin-broadcast' && context.method === 'patch')
    ) return

    let sender

    if (data.sender) {
      sender = await app.service('users').get(data.sender).catch(null)
      if (!_.isUndefined(sender)) {
        sender = ''
      }
    }

    const getRoleAdmin = await app.service('roles').find({
      query: {
        $limit: 1,
        name: 'Administrator'
      },
      paginate: false
    })
    const roleAdmin = getRoleAdmin[0]
    const getAdminUsers = await app.service('users').find({
      query: {
        role: roleAdmin._id
      },
      paginate: false
    })

    if (getAdminUsers.length > 0) {
      for (let i = 0; i < getAdminUsers.length; i++) {
        members.push(String(getAdminUsers[i]._id))
      }
    }

    if (['job-expired', 'job-rejected', 'notification'].includes(data.type)) {
      members.push(String(data.conversations.members[0]))
    }

    // this part is to avoid controller login to app and receive others vehicle messages
    const conversationMembers = await app.service('users').find({
      query: {
        _id: { $in: conversations.members }
      },
      paginate: false
    })

    for (let i = 0; i < conversationMembers.length; i++) {
      const member = conversationMembers[i]

      if (String(member._id) !== String(sender)) {
        if (member.device && member.device.os && member.device.os.toLowerCase() !== 'computer') { continue }
        members.push(String(member._id))
      }
    }
    // end here

    if (!_.isUndefined(conversations.vehicle)) {
      const vehicles = await app.service('vehicles').get(conversations.vehicle)

      if (String(vehicles.driver) !== String(sender)) {
        members.push(String(vehicles.driver))
        driver = vehicles.driver
        logger.info('the message send to', driver)
      }
    }

    const newChan = app.channel('authenticated')
      .filter(connection => {
        if (members.includes(String(connection.user._id))) {
          if (String(connection.user._id) === String(driver)) driver = null
          return true
        } else {
          return false
        }
      })

    let isBroadcastMessage = false

    await context.app.service('conversations').get(data.conversationId, {
      query: {
        $or: [
          { type: 'broadcast' },
          { type: 'admin-broadcast' }
        ]
      }
    })
      .then(() => { isBroadcastMessage = true })
      .catch(() => null)

    // Update sent date
    if (conversations.type === 'admin-broadcast' && !data.sentAt) app.service('messages').patch(data._id, { sentAt: new Date().toISOString() })

    if (!isBroadcastMessage) {
      return [
        newChan,
        app.channel('admins')
      ]
    } else {
      return context.app.channel('urgentBroadcastMembers')
    }
  }

  service.publish(messageChannel())
  service.publish('submitted', messageChannel())
}
