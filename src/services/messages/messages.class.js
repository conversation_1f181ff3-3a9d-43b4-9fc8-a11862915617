const { Service } = require('feathers-mongoose')
const { _ } = require('lodash')
const toObjectId = require('mongoose').Types.ObjectId
const MessageResult = require('../../beans/messageResult')
const logger = require('@hooks/logger')

exports.Messages = class Messages extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async sendMultipleVehicle (data, params) {
    for (let i = 0; i < data.vehicles.length; i++) {
      const vehicleId = data.vehicles[i]
      const company = await this.app.service('vehicles').get(vehicleId).then(v => { return v.company })
      // find conversation id
      const conversation = await this.app.service('conversations').find({
        query: {
          type: 'group',
          vehicle: vehicleId,
          company
        },
        paginate: false
      })

      const sendParam = {
        type: 'group',
        vehicle: vehicleId,
        company,
        messages: {
          type: data.messages.type,
          sender: data.messages.sender,
          content: data.messages.content
        },
        flagFrom: 'broadcast'
      }

      if (conversation.length > 0) {
        sendParam.conversationId = conversation[0]._id
      }

      await this.app.service('messages').create(sendParam)
    }

    return data
  }

  async create (data, params) {
    // Check if conversation exist
    let conversation = null

    if (data.conversationId) {
      conversation = await this.app.service('conversations').get(data.conversationId).catch(() => {
        return null
      })
    }

    // Set conversation if conversation not exists
    if (!conversation) {
      let conversationData = {}

      // get roles
      const roleControllerID = await this.app.service('roles-acl').find({
        query: {
          $select: ['_id'],
          modules: { $elemMatch: { name: 'Messaging', allowRead: true } }
        }
      }).then(res => res.data)

      switch (data.type) {
        case 'private':
          conversationData = {
            type: data.type,
            company: data.company,
            members: data.members,
            createdBy: data.messages.sender
          }

          break
        case 'broadcast':
          conversationData = { type: data.type }

          break
        case 'group': {
          const companyControllers = await this.app.service('users').find({
            query: {
              company: data.company,
              roleAcl: { $in: roleControllerID }
            },
            paginate: false
          })

          const controllers = []

          _.forEach(companyControllers, function (value) {
            controllers.push(value._id)
          })

          conversationData = {
            type: data.type,
            vehicle: data.vehicle,
            company: data.company,
            createdBy: data.messages.sender,
            members: controllers
          }

          break
        }
        case 'psa-message': {
          const companyMembers = await this.app.service('users').find({
            query: {
              company: data.company,
              roleAcl: { $in: roleControllerID }
            },
            paginate: false
          })

          const compControllers = []
          _.forEach(companyMembers, function (value) {
            compControllers.push(value._id)
          })

          conversationData = {
            type: data.type,
            vehicle: data.vehicle,
            company: data.company,
            members: compControllers
          }

          break
        }
        case 'admin-broadcast':
          conversationData = { type: data.type }

          break
        default:
          throw new Error('unknown message type')
      }

      conversation = await this.app.service('conversations').create(conversationData)
    }

    // insert into messages
    const messageData = {
      type: data.messages.type,
      conversationId: conversation._id,
      content: data.messages.content,
      sender: data.messages.sender,
      createdBy: data.messages.createdBy || null
    }

    if (data.flagFrom) {
      messageData.flagFrom = data.flagFrom
    }

    if (
      typeof data.messages.draft !== 'undefined' &&
      data.messages.draft
    ) messageData.draft = data.messages.draft

    // Send Push Notification if driver not online
    let driver = null
    const members = []
    let sender

    if (data.messages.sender) sender = await this.app.service('users').get(data.messages.sender).catch(null)

    if (sender && sender.device && (sender.device.os === 'android' || sender.device.os === 'ios')) {
      messageData.vehicle = data.vehicle
    }

    for (let i = 0; i < conversation.members.length; i++) {
      if (!_.isUndefined(sender)) {
        if (String(conversation.members[i]) !== String(sender)) {
          members.push(String(conversation.members[i]))
        }
      } else {
        members.push(String(conversation.members[i]))
      }
    }

    if (!_.isUndefined(conversation.vehicle)) {
      const vehicles = await this.app.service('vehicles').get(conversation.vehicle)

      if (!_.isUndefined(sender)) {
        if (String(vehicles.driver) !== String(sender)) {
          members.push(String(vehicles.driver))
          driver = vehicles.driver
        }
      } else {
        members.push(String(vehicles.driver))
        driver = vehicles.driver
      }
    }

    // await this.app.channel('authenticated')
    //   .filter(connection => {
    //     if(members.includes(String(connection.user._id))) {
    //       if(String(connection.user._id) === String(driver)) driver = null;
    //     }
    //   });

    const createMessage = await super.create(messageData)

    if (driver) {
      const { device } = await this.app.service('users').get(driver)

      if (!_.isUndefined(device) && device != null) {
        if (device.FCMId) {
          let notifTitle = ''

          if (conversation.type === 'psa-message') {
            notifTitle = 'PSA Message'
          } else if (conversation.type === 'broadcast') {
            notifTitle = 'PortNet - Broadcast'
          } else {
            notifTitle = (sender) ? sender.fullname : 'Unkown'
          }

          const fcmPayload = {
            notification: {
              title: notifTitle,
              body: data.messages.content.replace(/<br\D>/g, '\n')
            },
            data: {
              type: conversation.type
            },
            token: device.FCMId,
            message: {
              id: createMessage._id,
              to: driver
            }
          }

          await this.app.service('pushNotif').create(fcmPayload)
        }
      }
    }

    return createMessage
  }

  async getVehicleMessageQuery (vehicle, params) {
    const conversations = await this.app.service('conversations')
      .findVehicleConversations(vehicle)
      .then(res => res.map(v => v._id))
      .catch(() => [])
    const isAdmin = !(params.user.company && params.user.group)
    const flagFrom = !isAdmin
      ? { flagFrom: { $nin: ['inward', 'ctr-message'] } }
      : {}

    return {
      ...params.query,
      conversationId: conversations.length ? { $in: conversations } : { $exists: false },
      content: { $ne: '' },
      $or: [
        { draft: false },
        { draft: { $exists: false } }
      ],
      ...flagFrom
    }
  }

  async findVehicleMessages (vehicle, params) {
    const resultArray = []
    const start = Date.now()

    try {
      const query = await this.getVehicleMessageQuery(vehicle, params)
      const limit = parseFloat(query.$limit) || 10
      const sort = { createdAt: -1, sentAt: -1 }

      if (query.$sort) {
        Object.keys(query.$sort).forEach(v => {
          sort[v] = parseFloat(query.$sort[v])
        })
      }

      query.$sort = sort
      query.$limit = limit
      query.$populate = ['sender', 'conversationId', 'vehicle']

      const vehiclesDriverParam = { query: { vehicle: toObjectId(vehicle) } }
      const vehicleDriverResult = await this.app.service('vehicle-drivers').find({ paginate: false, ...vehiclesDriverParam })

      logger.debug('vehicleDriverResult', vehicleDriverResult)

      const messages = await super._find({
        paginate: false,
        query
      })

      for (const obj of messages) {
        let isSent = false

        if (params.user && obj.sender) {
          isSent = obj.sender._id.toString() === params.user._id.toString()
        }

        const msgType = obj.conversationId.type

        let driverSender = null

        if (obj.vehicle) {
          driverSender = obj.vehicle
        } else {
          for (const driverObj of vehicleDriverResult) {
            if ((obj.sender && driverObj.driver) && driverObj.driver._id.toString() === obj.sender._id.toString()) {
              driverSender = driverObj
              break
            }
          }
        }

        if (['psa-message', 'broadcast'].includes(msgType)) {
          obj.sender = 'Portnet'
        } else if (msgType === 'admin-broadcast') {
          obj.sender = 'CDAS'
        } else if (obj.flagFrom === 'ctr-message') {
          obj.sender = 'CTR'
        }

        if (['admin-broadcast', 'broadcast'].includes(obj.type)) obj.sender += ' (Broadcast)'

        const rs = new MessageResult(obj._id, obj.content, '', '', isSent, obj.conversationId._id,
          msgType, obj.createdAt, obj.recipientStatus, obj.flagFrom, driverSender)

        if (obj.sender instanceof Object) {
          rs.senderId = obj.sender._id
          rs.sender = obj.sender.fullname
        } else {
          rs.sender = obj.sender
        }

        resultArray.push(rs)
      }
    } catch (e) {
      logger.error('Error occur on findVehicleMessages', e)
    }

    const secs = Math.floor((Date.now() - start) / 1000)

    logger.info('findVehicleMessages total processing time', secs)

    return resultArray
  }
}
