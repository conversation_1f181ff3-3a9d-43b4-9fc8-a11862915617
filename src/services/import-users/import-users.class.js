const { BadRequest } = require('@feathersjs/errors')
const ExcelJS = require('exceljs')
const ejs = require('ejs')
const { Readable } = require('stream')
const fs = require('fs')
const AdmZip = require('adm-zip')
const { promisify } = require('util')
const renderFile = promisify(ejs.renderFile)
const { S3Client, GetObjectCommand, DeleteObjectCommand, PutObjectCommand } = require('@aws-sdk/client-s3')
const logger = require('@hooks/logger')

const zip = new AdmZip()

// Set the region
const s3 = new S3Client({
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  },
  region: 'ap-southeast-1'
})

exports.ImportUsers = class ImportUsers {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async get (id, params) {
    const { group } = params.query
    if (id === 'getTemplate') {
      if (!group) throw new BadRequest('Group NotFound')

      if (group instanceof Array) {
        const totalgroup = await this.app.service('groups').find({ query: { level: 1 } }).then(res => res.total)
        const orgTypeTrans = await this.app.service('roles').find({ query: { name: 'Transporter' } }).then(res => res.data)
        let children = await this.app.service('groups').find({ query: { level: 2, parent: { $in: group }, organizationType: orgTypeTrans[0]._id } }).then(res => res.data)

        children = children.map(e => String(e.parent)).map((e, i, final) => final.indexOf(e) === i && i).filter((e) => children[e]).map(e => children[e].parent)

        logger.debug('total group ', children.length)
        // if (total !== group.length) throw new Error('Some of group not exists!')

        const preValue = []
        const type = (totalgroup === group.length) ? 'all' : ''
        let filenames = []

        filenames = await Promise.all(children.map(g => {
          return this.generateTemplate(g, preValue, type, orgTypeTrans[0]._id)
        }))

        if (type === 'all') {
          filenames = await this.zipAllFiles()
        }

        return filenames
      } else {
        const res = await this.app.service('groups').get(group).catch(e => {
          throw new Error('Group not exists!')
        })

        return res
      }
    } else throw new BadRequest('Id NotFound')
  }

  async create (data, params) {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)))
    }

    return data
  }

  async generateTemplate (groupId, preValue = [], type, orgType = null) {
    try {
      // get the datas and prepare data
      const group = await this.app.service('groups').get(groupId)
      let companies = await this.app.service('groups').find({ query: { level: 2, parent: groupId, organizationType: orgType ? { $in: orgType } : { $exists: true } } })

      companies = companies.data

      const companiesName = companies.map(v => { return v.name })
      const organizationType = companies.map(val => { return val.organizationType })
      const roles = await this.app.service('roles').find({
        query: {
          _id: { $in: organizationType }
        }
      })
      const { data } = await this.app.service('roles-acl').find({ query: { _id: { $in: roles.data[0].rolesAcl } } })
      const rolesName = data.map(v => { return v.name })

      // prepare excel
      const filename = `${group.name.split(' ').join('-')} - Users-Import.xlsx`
      // const filename = 'template-users.xlsx';
      const workbook = new ExcelJS.Workbook()

      workbook.creator = 'Rejing'
      workbook.created = new Date()

      const ws = workbook.addWorksheet('Import Users')
      let row

      // Style sheet
      const formatAlignCenter = { vertical: 'middle', horizontal: 'center', wrapText: true }
      const formatTextBold = { size: 12, bold: true }

      ws.getColumn(1).width = 5
      ws.getColumn(1).width = 5
      ws.getColumn(2).width = 25
      ws.getColumn(3).width = 15
      ws.getColumn(4).width = 15
      ws.getColumn(5).width = 15
      ws.getColumn(6).width = 25
      ws.getColumn(7).width = 10
      ws.getColumn(8).width = 15
      ws.getColumn(9).width = 15
      ws.getColumn(10).width = 15
      ws.getColumn(11).width = 25

      ws.addRow([groupId])
      ws.getRow(1).hidden = true

      // START SET HEADER
      row = ws.addRow([
        'S/N',
        'Fullname*',
        'Username*',
        null,
        'Mobile Number*',
        'Email',
        '2FA Channel',
        null,
        'Company*',
        'Role*',
        'User Account Status*'
      ])

      const curRow = row._number
      ws.getCell(`B${curRow}`).value = {
        richText: [
          { font: { size: 12, bold: true }, text: 'Fullname*' },
          { font: { size: 9 }, text: '\rExample: Lee Hui Ling' }
        ]
      }
      ws.getCell(`D${curRow}`).value = {
        richText: [
          { font: { size: 12, bold: true }, text: 'Mobile Number Country Code*' },
          { font: { size: 9 }, text: '\rExample: +65' }
        ]
      }
      ws.getCell(`E${curRow}`).value = {
        richText: [
          { font: { size: 12, bold: true }, text: 'Mobile Number*' },
          { font: { size: 9 }, text: '\rExample: ********' }
        ]
      }
      ws.getCell(`F${curRow}`).value = {
        richText: [
          { font: { size: 12, bold: true }, text: 'Email' },
          { font: { size: 9, bold: true }, text: '\rPlease add email if 2 FA channel is email.' },
          { font: { size: 9, bold: true }, text: '\rOtherwise, leave blank.' },
          { font: { size: 9 }, text: '\rExample: lee123 @company.com.sg' }
        ]
      }
      ws.getCell(`H${curRow}`).value = {
        richText: [
          { font: { size: 12, bold: true }, text: 'Password*' },
          { font: { size: 9 }, text: '\rPlease assign a default password' },
          { font: { size: 9 }, text: '\r(minimum 8 character)' },
          { font: { size: 9 }, text: '\rExample: Welcome123' }
        ]
      }

      // _______ sixe auto
      let indxRow = 'A'

      ws.columns.forEach((column, index) => {
        const wert = ws.getCell(`${indxRow}${curRow}`)
        if (typeof wert.value === 'object') {
          if (wert.value && typeof wert.value.richText !== 'undefined') {
            let sizeWidth = 12

            wert.value.richText.forEach((x, index) => {
              if (typeof x.text !== 'undefined' && x.text) {
                if (sizeWidth < x.text.length) {
                  sizeWidth = x.text.length
                }
              }
            })

            if (column.width < sizeWidth) {
              column.width = sizeWidth
            }
          }
        } else if (typeof wert.value === 'string') {
          if (column.width < wert.value.length) {
            column.width = wert.value.length
          }
        }

        indxRow = String.fromCharCode(indxRow.charCodeAt(0) + 1)
      })
      // _______ ./sixe auto

      row.getCell(1).alignment = formatAlignCenter
      row.getCell(2).alignment = formatAlignCenter
      row.getCell(3).alignment = formatAlignCenter
      row.getCell(4).alignment = formatAlignCenter
      row.getCell(5).alignment = formatAlignCenter
      row.getCell(6).alignment = formatAlignCenter
      row.getCell(7).alignment = formatAlignCenter
      row.getCell(8).alignment = formatAlignCenter
      row.getCell(9).alignment = formatAlignCenter
      row.getCell(10).alignment = formatAlignCenter
      row.getCell(11).alignment = formatAlignCenter
      row.getCell(12).alignment = formatAlignCenter
      row.getCell(13).alignment = formatAlignCenter
      row.getCell(16).alignment = formatAlignCenter

      for (let i = 1; i <= 13; i++) {
        row.getCell(i).font = formatTextBold
      }
      // END SET HEADER

      // SET BODY TABLE
      if (preValue.length === 0) {
        row = ws.addRow([])
        row.getCell('B').numFmt = '@'
        row.getCell('C').numFmt = '@'
        row.getCell('D').numFmt = '@'
        row.getCell('F').numFmt = '@'
        row.getCell('H').numFmt = '@'
        row.getCell('G').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"E-mail, SMS, Push Notification"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('I').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${companiesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('J').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${rolesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('K').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"Active, Inactive"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
      }

      // set value FAILED import

      preValue.forEach(el => {
        row = ws.addRow(el)
        row.getCell('B').numFmt = '@'
        row.getCell('C').numFmt = '@'
        row.getCell('D').numFmt = '@'
        row.getCell('F').numFmt = '@'
        row.getCell('H').numFmt = '@'
        row.getCell('G').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"E-mail, SMS, Push Notification"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('I').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${companiesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('J').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${rolesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('K').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"Active, Inactive"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
      })

      // repeat logic Body
      for (let i = 0; i < 500; i++) {
        row = ws.addRow([])
        row.getCell('B').numFmt = '@'
        row.getCell('C').numFmt = '@'
        row.getCell('D').numFmt = '@'
        row.getCell('F').numFmt = '@'
        row.getCell('H').numFmt = '@'
        row.getCell('G').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"E-mail, SMS, Push Notification"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('I').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${companiesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('J').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${rolesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('K').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"Active, Inactive"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
      }
      // End set body

      if (type === 'all') {
        await workbook.xlsx.writeFile(`${this.app.get('public')}/import-users/${filename}`)
      } else {
        // write file non upload aws s3
        // await workbook.xlsx.writeFile(`${this.app.get('public')}/import-users/${filename}`);

        // write file upload to aws s3
        const buffer = await workbook.xlsx.writeBuffer()
        const readable = new Readable()

        readable._read = () => { } // _read is required but you can noop it
        readable.push(buffer)
        readable.push(null)

        // prosess upload to s3
        const uploadParams = { Bucket: this.app.get('storageBucket'), Key: '', Body: readable }

        uploadParams.Key = `template/${filename}`

        logger.debug('start upload to s3')

        try {
          await s3.send(new PutObjectCommand(uploadParams))
          logger.debug('success upload to S3')

          const fileName = `${this.app.get('public')}/import-users/${filename}`

          try {
            fs.unlinkSync(fileName)
            logger.info(`File ${fileName} deleted successfully`)
          } catch (e) {
            logger.error(`Error occur on remove ${fileName}`, e)
          }
        } catch (err) {
          logger.error('Oops! something wrong', err)
        }
      }
      return filename
    } catch (e) {
      // statements
      logger.error('Oops! something wrong', e)
    }
  }

  async readTemplate (filename) {
    try {
      // prepare before import data users
      const collectedUsers = []; const collectedEmail = []; const collectedUsername = []; const collectedPhone = []
      const roleMap = {}; const companiesMap = {}

      const workbook = new ExcelJS.Workbook()

      // get data from s3
      const params = { Bucket: this.app.get('storageBucket'), Key: `import/${filename}` }
      const s3Response = await s3.send(new GetObjectCommand(params))

      await workbook.xlsx.read(s3Response.Body)

      const ws = workbook.getWorksheet(1)
      const companies = await this.app.service('groups').find({ paginate: false, query: { level: 2 } })

      companies.forEach(val => {
        companiesMap[val.name] = val
      })

      const grubRoles = await this.app.service('roles').Model.aggregate([{
        $unwind: '$rolesAcl'
      }, {
        $lookup: {
          from: 'rolesacls',
          localField: 'rolesAcl',
          foreignField: '_id',
          as: 'rolesAcl'
        }
      }, {
        $addFields: {
          rolesAclName: '$rolesAcl.name'
        }
      }, {
        $unwind: '$rolesAclName'
      }, {
        $addFields: {
          rolesAclId: '$rolesAcl._id'
        }
      }, {
        $unwind: '$rolesAclId'
      }])
      grubRoles.forEach(val => {
        roleMap[val.rolesAclName] = val
      })

      let groupId

      ws.eachRow(async (row, rowNumber) => {
        let { values } = row

        if (rowNumber > 2) {
          values = values.map(v => {
            if (v && typeof v === 'object') v = v.text

            return v
          })

          collectedUsers.push(values)

          if (values[6]) collectedEmail.push(values[6])

          if (values[3]) collectedUsername.push(values[3])

          if (values[4] && values[5]) collectedPhone.push((values[4].includes('+') ? values[4] : '+' + values[4]) + (values[5] || ''))
        } else if (rowNumber === 1) {
          groupId = values[1].replace(/[^a-zA-Z0-9]/g, '')
        }
      })

      const importResult = await this.startImport({
        rawData: collectedUsers,
        collectedEmail,
        collectedUsername,
        collectedPhone,
        companiesMap,
        roleMap
      })

      const group = await this.app.service('groups').get(groupId, { paginate: false })
      let failedFilename

      if (importResult.invalidUser.length > 0) failedFilename = await this.generateTemplate(groupId, importResult.invalidUser)

      return {
        group: group.name,
        valid: importResult.validUser.length,
        invalid: importResult.invalidUser.length,
        filename: failedFilename || null
      }
    } catch (e) {
      // statements
      logger.error('Oops! something wrong', e)
    }
  }

  async startImport ({ rawData, collectedEmail, collectedUsername, collectedPhone, companiesMap, roleMap }) {
    try {
      const existUsername = (await this.app.service('users').find({
        paginate: false,
        query: {
          $limit: collectedUsername.length,
          username: {
            $in: collectedUsername
          }
        }
      })).map(u => u.username)
      const usernameExistsExcel = this.findDuplicate(collectedUsername)
      const existMobile = (await this.app.service('users').find({
        paginate: false,
        query: {
          $limit: collectedPhone.length,
          mobile: {
            $in: collectedPhone
          }
        }
      })).map(u => u.mobile)
      const mobileExistsExcel = this.findDuplicate(collectedPhone)
      const existEmail = (await this.app.service('users').find({
        paginate: false,
        query: {
          $limit: collectedEmail.length,
          email: {
            $in: collectedEmail
          }
        }
      })).map(u => u.email)
      const emailExistsExcel = this.findDuplicate(collectedEmail)

      const validUser = []
      const invalidUser = []
      const usernameExcel = []
      const mobileExcel = []
      const emailExcel = []
      const pattern = /^(?=[a-zA-Z0-9@._%+-]{6,254}$)[a-zA-Z0-9._%+-]{1,64}@(?:[a-zA-Z0-9-]{1,63}\.){1,8}[a-zA-Z]{2,63}$/
      const patternUser = /^[a-z0-9_.-]*$/
      const patternMobile = /^[+][0-9]*$/

      rawData.forEach((values) => {
        let validation = []

        const email = (values[6]) ? values[6] : ''
        const mobile = (values[4] && values[5]) ? (values[4].includes('+') ? values[4] : '+' + values[4]) + (values[5] || '') : undefined

        // start validate data
        // check null data
        if (!values[2]) validation.push('- Fullname is required')

        if (!values[3]) validation.push('- Username is required')

        if (!values[4]) validation.push('- Mobile Number Country Code is required')

        if (!values[5]) validation.push('- Mobile is required')

        if (!values[7]) validation.push('- 2FA is required')

        if (!values[8]) validation.push('- Password is required')

        if (!values[9]) validation.push('- Company is required')

        if (!values[10]) validation.push('- roles is required')

        if (!values[11]) validation.push('- Status is required')

        // check exsis data
        if (values[8] && values[8].length < 8) validation.push('- Minimum password length is 8 character')

        if (values[3] && !patternUser.test(values[3])) validation.push('- Invalid username format, use lowercase, digits, (_), (.), or (-) ')

        if (existUsername.includes(values[3])) validation.push('- Username already exists')

        if (usernameExistsExcel.includes(values[3])) {
          if (usernameExcel.includes(values[3])) validation.push('- Username duplicate inside excel file')
          usernameExcel.push(values[3])
        }

        if (existMobile.includes(mobile)) validation.push('- Mobile already exists')

        if (mobile && !patternMobile.test(mobile)) validation.push('- Invalid format mobile number')

        if (mobileExistsExcel.includes(mobile)) {
          if (mobileExcel.includes(mobile)) validation.push('- Mobile number duplicate inside excel file')
          mobileExcel.push(mobile)
        }

        if (values[7] === 'E-mail' && !email) validation.push('- Email is required if 2FA channel is E-mail')

        if (email && existEmail.includes(email)) validation.push('- Email already exists')

        if (email && emailExistsExcel.includes(email)) {
          if (emailExcel.includes(email)) validation.push('- Email duplicate inside excel file')
          emailExcel.push(email)
        }

        if (email && !pattern.test(email)) validation.push('- Invalid email format')
        // end validate data

        if (validation.length > 0) {
          if (!values[11]) values[11] = null
          validation = validation.join('\n')
          values.push(validation)
          invalidUser.push(values)
        } else {
          const user = {
            fullname: values[2],
            username: values[3],
            mobile,
            email,
            preferredComm: values[7] === 'SMS' ? 'sms' : values[7] === 'Push Notification' ? 'push' : 'email',
            password: values[8],
            company: companiesMap[values[9]]._id,
            role: roleMap[values[10]]._id,
            roleAcl: roleMap[values[10]].rolesAclId,
            group: companiesMap[values[9]].parent,
            status: (values[11] === 'Active')
          }

          validUser.push(user)

          try {
            this.app.service('users').create(user)
          } catch (e) {
            logger.error('Oops! something wrong', e)
          }
        }
      })

      return {
        validUser,
        invalidUser
      }
    } catch (e) {
      logger.error('Oops! something wrong', e)
    }
  }

  async prepareImport (filenames, user) {
    const fileUpload = []
    const promises = filenames.map(f => {
      fileUpload.push(f)
      return this.readTemplate(f)
    })

    try {
      const allImport = await Promise.all(promises)

      logger.debug(`allImport: ${JSON.stringify(allImport)}`)

      const attachments = []

      allImport.forEach(val => {
        if (val.filename) {
          const params = { Bucket: this.app.get('storageBucket'), Key: `template/${val.filename}` }
          const stream = s3.send(new GetObjectCommand(params))

          s3.on('error', function (err) {
            logger.error('Oops! something wrong', err)
          })
          attachments.push({ filename: val.filename, content: stream })
        }
      })

      renderFile(`${this.app.get('template').email}/upload-users-success.ejs`, {
        subject: 'Import Users Result',
        name: user.fullname,
        result: allImport
      }).then((html) => {
        const payloadEmail = {
          from: this.app.get('smtp').sender,
          to: user.email,
          subject: 'Import Users Result',
          html
        }
        if (attachments.length > 0 && attachments[0].filename) payloadEmail.attachments = attachments

        this.app
          .service('mailer')
          .create(payloadEmail)

        setTimeout(async () => {
          try {
            await Promise.all([
              ...allImport.filter(val => val.filename).map(val => this.deleteFileAWS(`template/${val.filename}`)),
              ...fileUpload.filter(el => el).map(el => this.deleteFileAWS(`import/${el}`))
            ])
          } catch (cleanupErr) {
            logger.error('Cleanup error:', cleanupErr)
          }
        }, 5000)
      }).catch(err => logger.error('Oops! something wrong', err))
    } catch (error) {
      logger.error('Oops! something wrong', error)
    }
  }

  async zipAllFiles () {
    try {
      const pathTemplate = './public/import-users/'
      const pathZip = './public/'

      if (fs.existsSync(pathTemplate)) {
        if (!fs.existsSync(pathZip)) fs.mkdirSync(pathZip, { recursive: true }, err => { logger.error('Oops! something wrong ', err) })

        const zipFileName = 'template-users.zip'
        const files = fs.readdirSync(pathTemplate)

        // Use for...of to allow await inside loop
        for (const file of files) {
          if (/^.*.xls.*$/.test(file)) {
            const fileName = `${pathTemplate}${file}`

            await zip.addLocalFile(fileName)
          }
        }

        zip.toBuffer()

        // Use synchronous writeZip, then upload and cleanup in the main async flow
        zip.writeZip(`${pathZip}${zipFileName}`)
        logger.info('Zip file successfully created')

        if (files.length) {
          for (let i = 0; i < files.length; i++) {
            const file = files[i]

            if (file.includes('.zip')) {
              const buffer = fs.readFileSync(`${pathZip}${file}`, null)
              const uploadParams = { Bucket: this.app.get('storageBucket'), Key: '', Body: buffer, ContentType: 'application/zip' }

              uploadParams.Key = `template/${zipFileName}`

              logger.debug('start upload to s3')

              try {
                await s3.send(new PutObjectCommand(uploadParams))
                logger.debug('already uploaded')

                const fileName = `${pathZip}${file}`

                try {
                  fs.unlinkSync(fileName)
                  logger.info(`File ${fileName} deleted successfully`)
                } catch (e) {
                  logger.error(`Error occur on remove ${fileName}`, e)
                }
              } catch (err) {
                logger.error('Oops! something wrong', err)
              }
            }
          }
        }

        return [zipFileName]
      }
    } catch (e) {
      logger.error('Error occur on zipAllFiles', e)
    }
  }

  async deleteFileAWS (s) {
    const deleteParam = {
      Bucket: this.app.get('storageBucket'),
      Key: s
    }

    try {
      await s3.send(new DeleteObjectCommand(deleteParam))
      logger.debug('success delete file s3')
    } catch (err) {
      logger.error('Oops! something wrong', err)
    }
  }

  findDuplicate (arra1) {
    const object = {}
    const result = []

    arra1.forEach(function (item) {
      if (item.length > 0) {
        if (!object[item]) {
          object[item] = 0
        }

        object[item] += 1
      }
    })

    for (const prop in object) {
      if (object[prop] >= 2) {
        result.push(prop)
      }
    }

    return result
  }
}
