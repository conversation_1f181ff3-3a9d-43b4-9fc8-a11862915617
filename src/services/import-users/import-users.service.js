// Initializes the `import users` service on path `/import-users`
const { ImportUsers } = require('./import-users.class')
const hooks = require('./import-users.hooks')
const multer = require('multer')
const multerSharp = require('@multigunagemilang/multer-sharp-addon')
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3')
const fs = require('fs')
const logger = require('@hooks/logger')

const s3 = new S3Client({
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  },
  region: 'ap-southeast-1'
})

module.exports = function (app) {
  const confStorage = {
    destination: (_req, _file, cb) => cb(null, `${app.get('import')}/users/`), // where the files are being stored
    filename: (_req, file, cb) => cb(null, `${Date.now()}-${file.originalname}`) // getting the file name
  }

  const storage = multerSharp(confStorage)
  const upload = multer({ storage })

  const paginate = app.get('paginate')

  const options = {
    paginate
  }

  // Initialize our service with any options it requires
  app.use('/import-users',
    new ImportUsers(options, app),
    upload.array('files'), async (req, _res, next) => {
      const { method } = req

      if (method === 'POST' || method === 'PATCH') {
        req.feathers.files = req.files // transfer the received files to feathers

        const files = req.files.map(f => {
          return f.filename
        })

        // Use Promise.all to upload all files in parallel and collect results
        const uploadResults = await Promise.all(req.files.map(async (v) => {
          const filename = v.filename

          logger.debug('upload import: ', filename)

          const path = `${app.get('import')}/users/${filename}`
          const params = {
            Bucket: app.get('storageBucket'),
            Key: `import/${filename}`,
            Body: fs.createReadStream(path)
          }

          try {
            await s3.send(new PutObjectCommand(params))
            return { filename, success: true }
          } catch (err) {
            logger.error('S3 upload failed:', err)
            return { filename, success: false, error: err.message }
          }
        }))

        const failedUploads = uploadResults.filter(r => !r.success)

        if (failedUploads.length > 0) {
          _res.json({
            status: 'partial',
            message: 'Some files failed to upload.',
            failed: failedUploads.map(f => ({ filename: f.filename, error: f.error }))
          })
        } else {
          app.service('import-users').prepareImport(files, req.res.hook.params.user)
          _res.json({
            status: 'success',
            message: 'All files uploaded. We\'re processing your file(s) now. We will notify you the result after process have done.'
          })
        }
      } else {
        next()
      }
    })

  // Get our initialized service so that we can register hooks
  const service = app.service('import-users')

  service.hooks(hooks)
}
