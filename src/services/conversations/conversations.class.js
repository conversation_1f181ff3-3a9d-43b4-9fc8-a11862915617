const { Service } = require('feathers-mongoose')

exports.Conversations = class Conversations extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async findVehicleConversations (vehicle) {
    const driverVehicle = await this.app.service('vehicles').get(vehicle)
      .catch(() => null)

    if (!driverVehicle) return []

    return super.find({
      paginate: false,
      query: {
        $or: [
          {
            type: { $in: ['group', 'psa-message'] },
            vehicle: driverVehicle._id,
            $or: [{ company: driverVehicle.company }, { company: null }]
          }
        ]
      }
    })
  }
}
