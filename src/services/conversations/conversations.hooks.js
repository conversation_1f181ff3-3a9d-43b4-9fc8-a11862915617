const logger = require('@hooks/logger')
const { authenticate } = require('@feathersjs/authentication').hooks

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [async ctx => {
      const query = {
        type: ctx.data.type,
        vehicle: ctx.data.vehicle,
        company: ctx.data.company,
        members: {
          $all: ctx.data.members
        },
        $sort: { createdAt: -1 },
        $limit: 1
      }

      const conv = await ctx.app.service('conversations').find({ query })

      if (conv.total) ctx.result = conv.data[0]

      return ctx
    }],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [async ctx => {
      if (ctx.params.query.$limit === '9999999' && ctx.params.query.type === 'group') {
        logger.info('before ctx.params.query.limit', ctx.result.data.length)
        if (ctx.result.data && ctx.result.data.length > 200) {
          for (let i = 0; i < ctx.result.data.length; i++) {
            const conversationId = ctx.result.data[i]._id
            const messageCount = await ctx.app.service('messages').find({ paginate: false, query: { conversationId, $select: ['_id'] } })
            if (messageCount && messageCount.length <= 0) {
              ctx.result.data.splice(i, 1)
            }
          }
        }
        logger.info('after ctx.params.query.limit', ctx.result.data.length)
      }
    }],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
