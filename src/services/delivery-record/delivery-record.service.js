// Initializes the `delivery-record` service on path `/delivery-record`
const { DeliveryRecord } = require('./delivery-record.class')
const createModel = require('../../models/delivery-record.model')
const hooks = require('./delivery-record.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/delivery-record', new DeliveryRecord(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('delivery-record')

  service.hooks(hooks)
}
