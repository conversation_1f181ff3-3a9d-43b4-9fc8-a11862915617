/* eslint-disable no-unused-vars */
exports.Company = class Company {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async find (params) {
    const query = { level: 2, $sort: { name: 1 } }

    if (params.query.name) query.name = { $regex: params.query.name, $options: 'i' }

    let data = await this.app.service('groups').find({ query, paginate: false })

    data = data.map(v => {
      v = {
        _id: v._id,
        name: v.name,
        status: v.status,
        trailerShareStatus: v.trailerShareStatus
      }

      return v
    })

    return data
  }
}
