// Initializes the `job-counter` service on path `/job-counter`
const { JobCounter } = require('./job-counter.class')
const createModel = require('../../models/job-counter.model')
const hooks = require('./job-counter.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/job-counter', new JobCounter(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('job-counter')

  service.hooks(hooks)
}
