const speakeasy = require('speakeasy')
const crypto = require('crypto')

/* eslint-disable no-unused-vars */
exports.Otp = class Otp {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async remove (id, params) {
    return { id }
  }

  async generate (secret) {
    // Generate random salt & save it to redis
    const salt = crypto.randomBytes(12).toString('hex')
    this.app.redis.set(`${secret}`, salt)

    // generate otp based on secret & random salt
    const token = speakeasy.totp({
      secret: `${secret + salt}`
    })

    return token
  }

  async validate (params) {
    const { secret, token } = params
    // Get the random salt that generated previously for given secret
    const salt = await this.app.redis.get(secret)
    // Validate OTP code
    const result = speakeasy.totp.verify({
      secret: `${secret + salt}`,
      token,
      window: (this.app.get('otpValidity') / 30)
    })

    if (!result) throw new Error('Your OTP code invalid or has been expired!')

    return result
  }
}
