// Initializes the `get-files` service on path `/get-files`
const { GetFile } = require('./get-files.class.js')
const hooks = require('./get-files.hooks')

const { S3Client, GetObjectCommand } = require('@aws-sdk/client-s3')

const s3 = new S3Client({
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  },
  region: 'ap-southeast-1'
})

module.exports = function (app) {
  const options = {
    whitelist: ['$exists', '$and', '$not', '$regex', '$options']
  }

  app.use('/get-files', new GetFile(options, app), async (req, _res, next) => {
    const { method } = req

    if (method === 'GET') {
      const key = req.query.path
      const filename = req.query.name

      if (!key) {
        return _res.json({
          success: false,
          error: 'Path not Found'
        })
      }

      const param = {
        Bucket: app.get('storageBucket'),
        Key: key
      }

      try {
        const s3Response = await s3.send(new GetObjectCommand(param))

        if (filename) {
          _res.attachment(filename)
        } else {
          _res.attachment(key)
        }

        _res.send(s3Response.Body)
      } catch (err) {
        _res.json({
          success: false,
          error: 'Page Not Found'
        })
      }
    } else {
      next()
    }
  })

  // Get our initialized service so that we can register hooks
  const service = app.service('get-files')

  service.hooks(hooks)
}
