// const { authenticate } = require('@feathersjs/authentication').hooks;

module.exports = {
  before: {
    // all: [ authenticate('jwt') ],
    all: [],
    find: [async ctx => {
      if (ctx.params.query.group || ctx.params.query.company) {
        const users = await ctx.app.service('users').find({
          paginate: false,
          query: { [ctx.params.query.company ? 'company' : 'group']: ctx.params.query.company || ctx.params.query.group }
        })
          .then(res => {
            if (res.length) return res.map(v => v._id)
          })
          .catch(() => [])

        ctx.params.query.user = await users.length ? { $in: users } : { $exists: false }
        delete ctx.params.query.company
        delete ctx.params.query.group
      }

      return ctx
    }],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
