// Initializes the `activity-logs` service on path `/activity-logs`
const { ActivityLogs } = require('./activity-logs.class')
const createModel = require('../../models/activity-logs.model')
const hooks = require('./activity-logs.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$options', '$regex', '$exists']
  }

  // Initialize our service with any options it requires
  app.use('/activity-logs', new ActivityLogs(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('activity-logs')

  service.hooks(hooks)
}
