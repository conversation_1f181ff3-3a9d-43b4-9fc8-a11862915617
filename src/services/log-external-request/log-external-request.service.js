// Initializes the `logExternalRequest` service on path `/log-external-request`
const { LogExternalRequest } = require('./log-external-request.class')
const createModel = require('../../models/log-external-request.model')
const hooks = require('./log-external-request.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/log-external-request', new LogExternalRequest(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('log-external-request')

  service.hooks(hooks)
}
