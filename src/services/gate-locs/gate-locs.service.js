// Initializes the `gate-locs` service on path `/gate-locs`
const { GateLocs } = require('./gate-locs.class')
const createModel = require('../../models/gate-locs.model')
const hooks = require('./gate-locs.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$populate'],
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/gate-locs', new GateLocs(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('gate-locs')

  service.hooks(hooks)
}
