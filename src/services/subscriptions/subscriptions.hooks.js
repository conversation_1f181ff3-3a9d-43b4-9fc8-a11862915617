const { authenticate } = require('@feathersjs/authentication').hooks

const checkVehicleQuota = () => async (ctx) => {
  if (ctx.data.licenseNo) {
    const subs = await ctx.app.service('subscriptions').get(ctx.id)
    const vehicles = await ctx.app.service('vehicles').find({
      query: { licenseList: { $elemMatch: { idLicense: subs.license, portNet: true } }, company: subs.company.toString() }
    })

    if (vehicles.total > ctx.data.licenseNo) {
      throw new Error(`Please reduce ${vehicles.total - ctx.data.licenseNo} vehicles that have been subscribed`)
    }
  }

  return ctx
}

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [
      async ctx => {
        const subscription = await ctx.app.service('subscriptions').find({
          query: {
            company: ctx.data.company,
            license: ctx.data.license,
            endDate: {
              $gte: new Date()
            }
          },
          paginate: false
        })
        if (subscription.length > 0) throw new Error('Company subscription is already exist and active')

        ctx.data.createdBy = (ctx.params.user && ctx.params.user._id) || null
      }
    ],
    update: [ctx => {
      if (ctx.data.module === 'companySubscriptions') {
        ctx.data.companySubUpdatedBy = (ctx.params.user && ctx.params.user._id) || null
        ctx.data.companySubUpdatedAt = new Date()
      } else if (ctx.data.module === 'vehicleSubscriptions') {
        ctx.data.vehSubUpdatedBy = (ctx.params.user && ctx.params.user._id) || null
        ctx.data.vehSubUpdatedAt = new Date()
      }
      return ctx
    }, checkVehicleQuota()],
    patch: [ctx => {
      if (ctx.data.module === 'companySubscriptions') {
        ctx.data.companySubUpdatedBy = (ctx.params.user && ctx.params.user._id) || null
        ctx.data.companySubUpdatedAt = new Date()
      } else if (ctx.data.module === 'vehicleSubscriptions') {
        ctx.data.vehSubUpdatedBy = (ctx.params.user && ctx.params.user._id) || null
        ctx.data.vehSubUpdatedAt = new Date()
      }
      return ctx
    }, checkVehicleQuota()],
    remove: [
      async ctx => {
        const subscription = await ctx.app.service('subscriptions').get(ctx.id)
          .catch(() => null)

        if (subscription) {
          const vehicles = await ctx.app.service('vehicles').find({
            query: {
              company: subscription.company,
              licenseList: { $elemMatch: { idLicense: subscription.license, portNet: true } }
            }
          })

          if (vehicles.total) throw new Error('Subscription is in use')
        }
      }
    ]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
