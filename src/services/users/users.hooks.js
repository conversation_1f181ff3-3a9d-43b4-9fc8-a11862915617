const { authenticate } = require('@feathersjs/authentication').hooks
const { userValidation } = require('../../schema/users')
const search = require('feathers-mongodb-fuzzy-search')
const _ = require('lodash')
const { BadRequest } = require('@feathersjs/errors')
const { iff, isProvider } = require('feathers-hooks-common')
const { hashPassword, protect } = require('@feathersjs/authentication-local').hooks

const checkUserInactiveStatus = () => async ctx => {
  if (typeof ctx.data.status !== 'undefined' && ctx.data.status === false) {
    const prevStatus = await ctx.app.service('users').get(ctx.id)
      .then(res => res.status || null)
      .catch(() => null)

    // Emit status updated event
    if (prevStatus !== ctx.data.status) ctx.app.service('users').emit('inactive', { _id: ctx.id, status: ctx.data.status })
  }

  return ctx
}

const addMemberConversation = () => async ctx => {
  if (ctx.data.roleAcl) {
    // assign new user with role transporter-controller into conversations
    const getRole = await ctx.app.service('roles-acl').find({
      query: {
        modules: { $elemMatch: { name: 'Messaging', allowRead: true } },
        _id: ctx.result.roleAcl
      }
    })

    if (getRole.data.length > 0) {
      const conversations = await ctx.app.service('conversations').find({
        query: {
          company: ctx.result.company,
          $or: [
            { type: 'group' },
            { type: 'psa-message' }
          ]
        },
        paginate: false
      })

      if (conversations.length > 0) {
        for (let i = 0; i < conversations.length; i++) {
          await ctx.app.service('conversations').patch(
            { _id: conversations[i]._id },
            {
              $push: {
                members: ctx.result._id
              }
            }
          )
        }
      }
    }
  }
}

const logOnlineStatus = require('@hooks/log-online-status')

const removeMemberConversation = () => async ctx => {
  if (ctx.data.roleAcl) {
    const user = await ctx.app.service('users').get(ctx.id)
    const conversations = await ctx.app.service('conversations').find({
      query: {
        company: user.company,
        $or: [
          { type: 'group' },
          { type: 'psa-message' }
        ]
      },
      paginate: false
    })

    if (conversations.length > 0) {
      for (let i = 0; i < conversations.length; i++) {
        await ctx.app.service('conversations').patch(
          { _id: conversations[i]._id },
          {
            $pull: {
              members: { $in: [ctx.id] }
            }
          }
        )
      }
    }
  }

  return ctx
}
const removeVehicleDriver = () => async ctx => {
  if (ctx.data.roleAcl) {
    const user = await ctx.app.service('users').get(ctx.id, {
      query: {
        $populate: 'roleAcl'
      }
    })

    if (!user.roleAcl.forCdas) {
      if (ctx.data.company.toString() !== user.company.toString()) {
        const vehicle = await ctx.app.service('vehicles').find({
          query: {
            driver: ctx.id
          },
          paginate: false
        })

        if (vehicle.length > 0) {
          await ctx.app.service('vehicles').patch(vehicle[0]._id, {
            company: vehicle[0].company,
            driver: null,
            group: vehicle[0].group,
            status: vehicle[0].status,
            vehicleNo: vehicle[0].vehicleNo,
            updatedBy: ctx.params.user._id
          }, { user: ctx.params.user })
        }
      }
    }
  }
}

module.exports = {
  before: {
    all: [
      search(),
      search({ // regex search on given fields
        fields: ['username', 'email']
      })
    ],
    find: [
      authenticate('jwt'),
      async ctx => {
        // hook before search role
        const roleParams = ctx.params.query.role

        if (!_.isUndefined(ctx.params.query.role)) {
          const roles = ['Administrator', 'Controller', 'Driver']
          // if roles params is array
          if (_.isArray(roleParams)) {
            const roleId = []
            for (let i = 0; i < roleParams.length; i++) {
              if (roles.includes(roleParams[i])) {
                const getRole = await ctx.app.service('roles').find({
                  query: {
                    name: roleParams[i]
                  },
                  $limit: 1,
                  paginate: false
                })
                if (getRole.length > 0) {
                  roleId.push(getRole[0]._id)
                }
              }
            }
            ctx.params.query.role = roleId
            return ctx
          }

          // if roles params is single data
          if (roles.includes(roleParams)) {
            const getRole = await ctx.app.service('roles').find({
              query: {
                name: ctx.params.query.role
              },
              $limit: 1,
              paginate: false
            })
            if (getRole.length > 0) {
              ctx.params.query.role = getRole[0]._id
            }
            return ctx
          }
        }
      }
    ],
    get: [authenticate('jwt')],
    create: [
      authenticate('jwt'),
      userValidation,
      hashPassword('password'),
      async ctx => {
        // email validation
        if (ctx.data.email !== '') {
          const user = await ctx.app.service('users').find({
            query: {
              email: ctx.data.email
            }
          })

          if (user.total > 0) {
            const newError = new BadRequest('Email is already used', {
              email: ctx.data.email
            })

            throw newError
          }
        }

        // mobile validation
        const getUserByMobile = await ctx.app.service('users').find({
          query: {
            mobile: ctx.data.mobile
          }
        })

        if (getUserByMobile.total > 0) {
          const newError = new BadRequest('Mobile number is already used', {
            mobile: ctx.data.mobile
          })

          throw newError
        }

        // username validation
        const getUserByUsername = await ctx.app.service('users').find({
          query: {
            username: ctx.data.username
          }
        })

        if (getUserByUsername.total > 0) {
          const newError = new BadRequest('Username is already used', {
            username: ctx.data.username
          })

          throw newError
        }
      }
    ],
    update: [
      hashPassword('password'),
      authenticate('jwt'),
      async ctx => {
        // fullname validation
        if (!_.isUndefined(ctx.data.fullname)) {
          if (ctx.data.fullname === '') {
            const newError = new BadRequest('Fullname is required', {
              fullname: ctx.data.fullname
            })

            throw newError
          }
        }

        // email validation
        if (!_.isUndefined(ctx.data.email)) {
          if (ctx.data.email !== '') {
            const user = await ctx.app.service('users').find({
              query: {
                email: ctx.data.email,
                _id: {
                  $ne: ctx.id
                }
              }
            })

            if (user.total > 0) {
              const newError = new BadRequest('Email is already used', {
                email: ctx.data.email
              })

              throw newError
            }
          }
        }

        // mobile validation
        if (!_.isUndefined(ctx.data.mobile)) {
          const getUserByMobile = await ctx.app.service('users').find({
            query: {
              mobile: ctx.data.mobile,
              _id: {
                $ne: ctx.id
              }
            }
          })

          if (getUserByMobile.total > 0) {
            const newError = new BadRequest('Mobile number is already used', {
              mobile: ctx.data.mobile
            })

            throw newError
          }
        }

        // username validation
        if (!_.isUndefined(ctx.data.username)) {
          const getUserByUsername = await ctx.app.service('users').find({
            query: {
              username: ctx.data.username,
              _id: {
                $ne: ctx.id
              }
            }
          })

          if (getUserByUsername.total > 0) {
            const newError = new BadRequest('Username is already used', {
              username: ctx.data.username
            })

            throw newError
          }
        }
      },
      checkUserInactiveStatus()
    ],
    patch: [
      hashPassword('password'),
      authenticate('jwt'),
      async ctx => {
        // fullname validation
        if (!_.isUndefined(ctx.data.fullname)) {
          if (ctx.data.fullname === '') {
            const newError = new BadRequest('Fullname is required', {
              fullname: ctx.data.fullname
            })

            throw newError
          }
        }

        // email validation
        if (!_.isUndefined(ctx.data.email)) {
          if (ctx.data.email !== '') {
            const user = await ctx.app.service('users').find({
              query: {
                email: ctx.data.email,
                _id: {
                  $ne: ctx.id
                }
              }
            })

            if (user.total > 0) {
              const newError = new BadRequest('Email is already used', {
                email: ctx.data.email
              })

              throw newError
            }
          }
        }

        // mobile validation
        if (!_.isUndefined(ctx.data.mobile)) {
          const getUserByMobile = await ctx.app.service('users').find({
            query: {
              mobile: ctx.data.mobile,
              _id: {
                $ne: ctx.id
              }
            }
          })

          if (getUserByMobile.total > 0) {
            const newError = new BadRequest('Mobile number is already used', {
              mobile: ctx.data.mobile
            })

            throw newError
          }
        }

        // username validation
        if (!_.isUndefined(ctx.data.username)) {
          const getUserByUsername = await ctx.app.service('users').find({
            query: {
              username: ctx.data.username,
              _id: {
                $ne: ctx.id
              }
            }
          })

          if (getUserByUsername.total > 0) {
            const newError = new BadRequest('Username is already used', {
              username: ctx.data.username
            })

            throw newError
          }
        }
      },
      checkUserInactiveStatus(), removeMemberConversation(), removeVehicleDriver()
    ],
    remove: [authenticate('jwt')]
  },

  after: {
    all: [
      iff(isProvider('external'), protect('passwordHistories')),
      // Make sure the password field is never sent to the client
      // Always must be the last hook
      protect('password')
    ],
    find: [],
    get: [],
    create: [addMemberConversation()],
    update: [],
    patch: [logOnlineStatus(), addMemberConversation()],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
