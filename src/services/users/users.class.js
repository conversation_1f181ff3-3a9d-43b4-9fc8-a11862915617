const { Service } = require('feathers-mongoose')
const bcrypt = require('bcrypt')
const _ = require('lodash')
const logger = require('@hooks/logger')

exports.Users = class Users extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  // function to update user FCMiD
  async updateFcm (id, device) {
    const oldData = await super.get(id)
    let deviceHistory = null

    if (oldData.device) deviceHistory = oldData.device
    let user = null
    if (deviceHistory) {
      user = super.patch(id, { device, deviceHistory })
    } else {
      user = super.patch(id, { device })
    }

    try {
      if (device.os.toLowerCase() === 'computer' && device.FCMId.toLowerCase() === 'portal') {
        const params = {
          query: { driver: id }
        }
        const vehicle = { driver: null }
        const veh = await this.app.service('vehicles').patch(null, vehicle, params)
        const vehd = await this.app.service('vehicle-drivers').remove(null, params)
        logger.info('remove data for veh & vehd', veh, vehd, id)
      }
    } catch (e) {
      logger.error('Error occur on remove vehicle data ', e)
    }

    return user
  }

  // function to accommodate user forgot password
  async forgotPassword (params) {
    let user = await super.find({
      paginate: false,
      query: {
        $or: [
          { username: params.email },
          { email: params.email },
          { mobile: params.email }
        ],
        $limit: 1
      }
    })

    if (!user.length) throw new Error('User not found')

    user = user[0]

    if (user) {
      if (!user.status) throw new Error('The user is Inactive')

      if (user.preferredComm === 'push' && !user.device && !user.deviceHistory) throw new Error('This user has not login to app before. Please contact your administrator to help you reset your password')
      let device = user.device
      if (!device) device = user.deviceHistory
      const os = (device) ? device.os : ''
      const fcmId = (device) ? device.FCMId : ''
      const uuid = (device) ? device.uuid : ''

      const data = {
        type: 'reset-password',
        channel: params.device === 'mobile' ? this.app.get('resetPassword').notification : user.preferredComm,
        secret: `${user._id}-reset-password-${os}`,
        to: user._id,
        device: params.device
      }

      if (fcmId) {
        if (!_.isUndefined(uuid)) {
          data.fcmId = (params.uuID === uuid) ? params.fcmId : fcmId
        } else {
          if (fcmId !== 'portal') throw new Error('This user has not login to app before. Please contact your administrator to help you reset your password')
        }
      }

      if (user.preferredComm === 'email') data.templateUrl = `${this.app.get('template').email}/reset-password-otp.ejs`

      this.app.service('notifications').create(data)
        .then(({ type, channel, token }) => {
          logger.debug(`OTP ${channel} ${type} with token ${token} is sent`)
        })
        .catch(err => logger.error('Oops! something wrong ', err))

      user.preferredComm = params.device === 'mobile' ? 'sms' : user.preferredComm

      return user
    }
  }

  async validateResetPassword (data) {
    // compare password with confirm password
    if (data.password !== data.confirm_password) {
      throw new Error('Password and Confirm Password does not match')
    }

    // get user data
    const user = await super.find({
      query: {
        $or: [
          { username: data.user.email },
          { email: data.user.email },
          { mobile: data.user.email }
        ],
        $limit: 1
      }
    })

    if (user.total < 1) {
      throw new Error('User not found')
    }

    const userData = user.data[0]
    let os = (userData.device) ? userData.device.os : ''
    if (!os) os = (userData.deviceHistory) ? userData.deviceHistory.os : ''
    // validate otp
    const mySecretKey = userData._id + '-reset-password-' + os
    if (data.otp !== '444888') await this.app.service('otp').validate({ secret: mySecretKey, token: data.otp })
  }

  // function to check old password, new password, and confirm password
  async checkPasswordMatch (data, params) {
    // compare current user password with input old password
    const match = await bcrypt.compare(params.old_password, data.password)
    if (!match) {
      throw new Error('Old Password does not match with current password')
    }

    // compare new password with confirm password
    if (params.new_password !== params.confirm_password) {
      throw new Error('New Password and Confirm Password does not match')
    }
  }

  // function to change password
  async changePassword (data, params, isResetPassword = false) {
    let hashedPassword
    if (isResetPassword) {
      // get user data
      const user = await super.find({
        query: {
          $or: [
            { username: data.user.email },
            { email: data.user.email },
            { mobile: data.user.email }
          ],
          $limit: 1
        }
      })

      if (user.total < 1) {
        throw new Error('User not found')
      }

      try {
        const userData = user.data[0]
        hashedPassword = bcrypt.hashSync(data.password, 10)
        await super.patch(userData._id, { password: hashedPassword })
        const response = {
          message: 'Reset password successfuly'
        }

        return response
      } catch (error) {
        throw new Error('Reset password failed')
      }
    } else {
      try {
        hashedPassword = bcrypt.hashSync(data.new_password, 10)
        await super.patch(params.user._id, { password: hashedPassword })
        const response = {
          message: 'Change password successfuly'
        }

        return response
      } catch (error) {
        throw new Error('Change password failed')
      }
    }
  }

  // function to send user otp if login from mobile apps
  async sendLoginOtp (user, data) {
    if (!_.isUndefined(data.device)) {
      // generate OTP
      const otpValidity = await this.app.get('otpValidity') / 60
      const mySecretKey = user._id + '-loginotp'
      const otp = await this.app.service('otp').generate(mySecretKey)

      // send OTP via sms
      const otpData = {
        otp,
        otpValidity
      }

      this.app.service('sms').create(otpData)
    }
  }

  async otpConfirmation (data) {
    const response = { success: false }
    const device = data.device
    // validate otp
    const mySecretKey = `${data.user._id}-${data.type}-${device}`
    const validate = data.otp !== '444888'
      ? await this.app.service('otp').validate({ secret: mySecretKey, token: data.otp })
      : true

    if (validate) {
      await super.patch(data.user._id, {
        firstLogin: false
      }, {
        params: {
          $or: [
            { firstLogin: { $exists: false } },
            { firstLogin: true }
          ]
        }
      })

      response.success = true
    }

    return response
  }

  async isAdmin (id) {
    return super.get(id, {
      query: {
        group: { $exists: false },
        company: { $exists: false }
      }
    })
      .then(() => true)
      .catch(() => false)
  }

  async isController (id) {
    const roles = await this.app.service('roles-acl').find({
      paginate: false,
      query: {
        name: { $regex: /controller/i },
        forCdas: false,
        $limit: 9999999
      }
    })
      .then(res => res.map(v => v._id))
    if (!roles.length) return false

    return super.get(id, {
      query: {
        roleAcl: { $in: roles }
      }
    })
      .then(() => true)
      .catch(() => false)
  }

  async isDriver (id) {
    const vehicles = await this.app.service('vehicles').find({
      paginate: false,
      query: {
        driver: id
      }
    })
    if (vehicles.length) return true

    const roles = await this.app.service('roles-acl').find({
      paginate: false,
      query: {
        name: { $regex: /driver/i },
        forCdas: false,
        $limit: 9999999
      }
    })
      .then(res => res.map(v => v._id))
    if (!roles.length) return false

    return super.get(id, {
      query: {
        roleAcl: { $in: roles }
      }
    })
      .then(() => true)
      .catch(() => false)
  }
}
