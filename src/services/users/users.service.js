// Initializes the `users` service on path `/users`
const { Users } = require('./users.class')
const createModel = require('../../models/users.model')
const hooks = require('./users.hooks')
const { authenticate } = require('@feathersjs/authentication').hooks
const logger = require('@hooks/logger')

module.exports = async function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    events: ['inactive'],
    whitelist: ['$text', '$search', '$regex', '$options', '$exists', '$populate']
  }

  // Initialize our service with any options it requires
  app.use('/users', new Users(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('users')

  /// BLOCK CUSTOM ROUTE ///
  // route for forgot password
  app.post('/users/forgot-password', async (req, res) => {
    try {
      const user = await service.forgotPassword(req.body)
      res.json(user)
    } catch ({ message }) {
      res.status(500).json({ message })
    }
  })

  // route for change password
  app.use('/users/reset-password', {
    async create (data, params) {
      const user = await service.changePassword(data, params, true)

      return user
    }
  })

  app.use('/users/reset-user-password', {
    async create (data, params) {
      const user = await service.changePassword(data, params, true)

      return user
    }
  })

  // route for change password
  app.use('/users/change-password', {
    async create (data, params) {
      const user = await service.changePassword(data, params, false)

      return user
    }
  })

  // route for user request otp
  app.use('/authentication/otp-request', {
    async create (data) {
      const user = await app.service('users').get(data.user._id)
      const device = data.device
      const dataSend = {
        type: data.type,
        channel: user.preferredComm,
        secret: `${user._id}-${data.type}-${device}`,
        to: user._id,
        device
      }

      if (user.preferredComm === 'email') {
        switch (data.type) {
          case 'first-login':
            dataSend.type = 'login'
            dataSend.templateUrl = await `${app.get('template').email}/otp-notifications.ejs`
            break

          case 'reset-password':
            dataSend.templateUrl = await `${app.get('template').email}/reset-password-otp.ejs`
            break

          case 'change-password':
            dataSend.templateUrl = await `${app.get('template').email}/change-password-otp.ejs`
            break

          case 'create-user':
            dataSend.templateUrl = await `${app.get('template').email}/create-user-otp.ejs`
            break

          case 'update-profile':
            dataSend.templateUrl = await `${app.get('template').email}/change-profile-otp.ejs`
            break
        }
      }

      return await app.service('notifications').create(dataSend)
        .then(({ type, channel, token }) => {
          logger.debug(`OTP ${channel} ${type} with token ${token} is sent`)

          return { success: true }
        })
        .catch(err => logger.error('Oops! something wrong ', err))
    }
  })

  // route for handle otp confirmation
  app.use('/authentication/otp-confirmation', {
    async create (data, params) {
      const otpConfirmation = await service.otpConfirmation(data, params)

      return otpConfirmation
    }
  })
  /// END BLOCK CUSTOM ROUTE ///

  /// BLOCK CUSTOM HOOKS ///
  // hooks for change password
  app.service('/users/change-password').hooks({
    before: {
      all: [
        authenticate('jwt'),
        ctx => ctx.app.service('users').checkPasswordMatch(ctx.params.user, ctx.data)
      ]
    }
  })

  // hooks for reset password
  app.service('/users/reset-password').hooks({
    before: {
      all: [
        ctx => ctx.app.service('users').validateResetPassword(ctx.data)
      ]
    }
  })
  /// END BLOCK CUSTOM HOOKS ///

  app.service('/users/reset-user-password').hooks({
    after: {
      all: [
        async ctx => {
          const record = {
            _id: ctx.data.userpatch._id,
            user: ctx.data.userpatch.username,
            username: ctx.data.user.email
          }

          const activityLogs = {
            action: 'Patch',
            module: 'Reset User Password',
            user: record._id,
            data: record
          }

          await ctx.app.service('activity-logs').create(activityLogs)
        }
      ]
    }
  })

  service.publish('inactive', (data) => app.channel(`userIds/${data._id}`))

  service.hooks(hooks)
}
