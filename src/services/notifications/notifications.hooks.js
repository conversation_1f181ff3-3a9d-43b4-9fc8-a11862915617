const { disallow, iff, isProvider } = require('feathers-hooks-common')
const authenticateOrWhitelist = require('@hooks/authenticate-or-whitelist')

module.exports = {
  before: {
    all: [],
    find: [disallow()],
    get: [disallow()],
    create: [iff(isProvider('external'), authenticateOr<PERSON>hitelist())],
    update: [disallow()],
    patch: [disallow()],
    remove: [disallow()]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
