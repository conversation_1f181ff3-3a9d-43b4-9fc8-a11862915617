/* eslint-disable no-unused-vars */
const ejs = require('ejs')
const { promisify } = require('util')
const renderFile = promisify(ejs.renderFile)
const _ = require('lodash')
const logger = require('@hooks/logger')

exports.Notifications = class Notifications {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
    this.events = ['sent']
  }

  async create (data) {
    try {
      if (!data || !data.channel) throw new Error('Channel is not specified')

      const token = await this.getToken(data.secret)
      let title = data.title || ''
      let description = data.description || ''
      const type = _.startCase(data.type)

      if (!title) title = `[CTR - ${(type === 'First Login') ? 'Login' : type}]${token ? ' OTP Request' : ''}`
      if (!description && !!token) description = `Use OTP code ${token.token} for your ${_.lowerCase(type)}. This code will expire in ${token.expiredMinutes} minutes.`

      const user = await this.app.service('users').get(data.to)
        .catch(() => null)

      const device = data.device
      if (device === 'mobile') { // mobile user
        if (data.channel === 'email') {
          data.channel = 'sms'
        }
      }

      if (data.channel === 'email') {
        if (!data.templateUrl) throw new Error('Template URL is required')

        let emailData = {
          user: {
            fullname: (user && user.fullname) || '',
            username: (user && user.username) || '',
            email: (user && user.email) || ''
          }
        }

        if (token) {
          emailData.token = token.token
          emailData.tokenExpiryMinutes = token.expiredMinutes
        }

        if (data.data) emailData = { ...emailData, ...data.data }

        const html = await renderFile(data.templateUrl, emailData)
        await this.app.service('mailer').create({
          from: this.app.get('smtp').sender,
          to: user ? user.email : data.to,
          subject: title,
          html
        })
          .then(res => logger.debug('email ', res))
      }

      if (data.channel === 'sms') {
        await this.app.service('sms').create({
          mobile: user ? user.mobile : data.to,
          message: `${token ? '[CTR - ' + type + ']' : title} ${description}`
        })
          .then(res => logger.debug('Sms ', res))
          .catch(err => logger.error('Oops! something wrong ', err))
      }

      if (data.channel === 'push') {
        let pushData = {
          notification: {
            title,
            body: description
          },
          data: {
            type: data.type,
            notification_foreground: 'true'
          },
          token: user ? (user.device && user.device.FCMId) : data.to
        }

        if (!token || data.type === 'reset-password') {
          if (data.type === 'reset-password') {
            if (data.fcmId) pushData.token = data.fcmId
          }

          if (['vehicle-dispatch', 'jobs-updated', 'job-withdrawn', 'job-dispatched', 'job-cancelled', 'job-updated', 'job-rejected'].indexOf(data.type) !== -1) {
            pushData = {
              notification: {
                title,
                body: description
              },
              data: {
                type: data.type
              },
              token: user ? (user.device && user.device.FCMId) : data.to
            }
          }
          await this.app.service('pushNotif').create(pushData)
        } else if (data.type === 'first-login') {
          if (user.device && user.deviceHistory) {
            if (user.device.uuid !== user.deviceHistory.uuid) {
              pushData.token = user.deviceHistory.FCMId
            }
          }

          await this.app.service('pushNotif').create(pushData)
        } else {
          this.app.service('notifications').emit('sent', pushData)
        }
      }

      const res = { type: data.type, channel: data.channel }
      if (token) res.token = token.token

      return res
    } catch (err) {
      logger.error('Oops! something wrong ', err)
    }
  }

  async getToken (secret = '') {
    if (!secret) return null

    const token = await this.app.service('otp').generate(secret)
    const expiredMinutes = Math.round(parseInt(this.app.get('otpValidity')) / 60)

    return { token, expiredMinutes }
  }
}
