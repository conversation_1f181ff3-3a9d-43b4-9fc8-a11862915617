// Initializes the `notifications` service on path `/notifications`
const { Notifications } = require('./notifications.class')
const hooks = require('./notifications.hooks')
const { ObjectId } = require('mongoose').Types

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate'),
    events: ['sent']
  }

  // Initialize our service with any options it requires
  app.use('/notifications', new Notifications(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('notifications')

  service.publish('sent', async (data) => {
    const query = {
      $or: [
        { 'device.FCMId': data.token }
      ]
    }
    if (ObjectId.isValid(data.token)) query.$or.push({ _id: data.token.toString() })

    const userId = await app.service('users').find({
      query,
      paginate: false
    })
      .then(res => res[0]._id)

    return app.channel(`userIds/${userId}`)
  })

  service.hooks(hooks)
}
