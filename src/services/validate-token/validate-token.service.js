// Initializes the `validate-token` service on path `/validate-token`
const { ValidateToken } = require('./validate-token.class')
const hooks = require('./validate-token.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/validate-token', new ValidateToken(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('validate-token')

  service.hooks(hooks)
}
