const { MethodNotAllowed } = require('@feathersjs/errors')

/* eslint-disable no-unused-vars */
exports.ValidateToken = class ValidateToken {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async find (params) {
    const removeField = ['password', 'passwordHistories', 'createdAt', 'updatedAt', '__v', 'device', 'isOnline', 'updatedBy', 'firstLogin', 'deviceHistory']

    for (let i = 0; i < removeField.length; i++) {
      delete params.user[removeField[i]]
    }

    if (!params.headers.authorization) throw new MethodNotAllowed('you\'re not allowed to access')

    await this.app.service('authentication').verifyAccessToken(params.headers.authorization)

    const response = { accessToken: params.headers.authorization, user: params.user }

    return response
  }
}
