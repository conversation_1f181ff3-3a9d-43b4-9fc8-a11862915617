// Initializes the `vehicle-groups` service on path `/vehicle-groups`
const { VehicleGroups } = require('./vehicle-groups.class')
const createModel = require('../../models/vehicle-groups.model')
const hooks = require('./vehicle-groups.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$options', '$regex', '$exists', '$populate']
  }

  // Initialize our service with any options it requires
  app.use('/vehicle-groups', new VehicleGroups(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('vehicle-groups')

  service.hooks(hooks)
}
