const { authenticate } = require('@feathersjs/authentication').hooks

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [
      async ctx => {
        const vehicleGroup = await ctx.app.service('vehicle-groups').find({
          query: {
            vehicleGroupName: ctx.data.vehicleGroupName,
            company: ctx.data.company
          },
          paginate: false
        })
        if (vehicleGroup.length > 0) {
          throw new Error('Group name \'' + ctx.data.vehicleGroupName + '\' is already exist')
        }
      }
    ],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
