const { authenticate } = require('@feathersjs/authentication').hooks
const logger = require('@hooks/logger')
module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [
      async ctx => {
        const user = await ctx.app.service('users').find({ query: { roleAcl: ctx.id } })

        if (user.total) throw new Error('Role is in use')
      }
    ]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [
      async ctx => {
        // assign new user with role transporter-controller into conversations
        const getRole = await ctx.app.service('roles-acl').find({
          query: {
            modules: { $elemMatch: { name: 'Messaging', allowRead: true } },
            _id: ctx.result._id
          }
        })
        if (getRole.data.length > 0) {
          const getUser = await ctx.app.service('users').find({
            query: {
              roleAcl: ctx.result._id
            },
            $orderby: { company: -1 }
          })

          for (let i = 0; i < getUser.data.length; i++) {
            const vehicle = await ctx.app.service('vehicles').find({
              query: {
                $select: ['_id'],
                company: getUser.data[i].company
              }
            }).then(res => res.data)

            const conversations = await ctx.app.service('conversations').find({
              query: {
                company: getUser.data[i].company,
                members: { $ne: getUser.data[i]._id },
                vehicle: { $in: vehicle },
                $or: [
                  { type: 'group' },
                  { type: 'psa-message' }
                ]
              },
              paginate: false
            })

            if (conversations.length > 0) {
              logger.debug('user Id ', getUser.data[i]._id)
              for (let j = 0; j < conversations.length; j++) {
                await ctx.app.service('conversations').patch(
                  { _id: conversations[j]._id },
                  {
                    $push: {
                      members: getUser.data[i]._id
                    }
                  }
                )
              }
            }
          }
        }
      }
    ],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
