// Initializes the `roles-acl` service on path `/roles-acl`
const { RolesAcl } = require('./roles-acl.class')
const createModel = require('../../models/roles-acl.model')
const hooks = require('./roles-acl.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch'],
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/roles-acl', new RolesAcl(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('roles-acl')

  service.hooks(hooks)
}
