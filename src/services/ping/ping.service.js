// Initializes the `testConnection` service on path `/test-connection`
const { Ping } = require('./ping.class')
const hooks = require('./ping.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/test-connection/ping', new Ping(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('test-connection/ping')

  service.publish('ping', (data) => {
    const channel = app.channel('authenticated').filter(conn => {
      return conn.user._id.toString() === data.driver
    })

    return channel
  })

  service.publish('ping timeout', () => {
    return app.channel('admins')
  })

  service.hooks(hooks)
}
