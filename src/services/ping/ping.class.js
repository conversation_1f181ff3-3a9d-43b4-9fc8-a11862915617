/* eslint-disable no-unused-vars */
exports.Ping = class Ping {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
    this.events = ['ping', 'ping timeout']
  }

  async create (data, params) {
    const response = { message: 'ok' }
    const driver = await this.app.service('users').get(data.driver).catch(() => null)
    const failedReasons = await this.validateData(data, driver)

    if (failedReasons.length === 2) {
      await this.updateConnectionHistory({
        status: false,
        vehicle: data.vehicle,
        company: data.company,
        failedReasons
      })

      this.app.service('test-connection/pong').emit('pong', { ...data, failedReasons, status: false })

      return response
    }

    let wsIdx = -1
    let pushIdx = -1

    for (let i = 0; i < failedReasons.length; i++) {
      if (failedReasons[i].channel === 'ws') {
        wsIdx = i
      } else if (failedReasons[i].channel === 'push') {
        pushIdx = i
      }
    }

    if (wsIdx < 0) {
      failedReasons[failedReasons.length] = { channel: 'ws', message: 'No response from client' }
    }
    if (pushIdx < 0) {
      failedReasons[failedReasons.length] = { channel: 'push', message: 'No response from client' }
    }

    const connHistory = await this.updateConnectionHistory({
      status: wsIdx > -1 && pushIdx > -1,
      vehicle: data.vehicle,
      company: data.company,
      failedReasons
    })

    if (wsIdx < 0) {
      this.app.service('test-connection/ping').emit('ping', data)
    }
    if (pushIdx < 0) {
      const fcmPayload = {
        data: {
          type: 'ping'
        },
        token: driver.device.FCMId
      }
      await this.app.service('pushNotif').create(fcmPayload)
    }

    setTimeout(async () => {
      const conn = await this.app.service('connection-histories').get(connHistory._id)
      if (!conn.status) {
        this.app.service('test-connection/ping').emit('ping timeout', {
          ...data,
          status: conn.status,
          failedReasons: conn.failedReasons
        })
      }
    }, 30000)

    return response
  }

  async validateData (data, driver) {
    const failedReasons = []

    if (!driver) {
      return [
        {
          channel: 'ws',
          message: 'Vehicle has no driver'
        },
        {
          channel: 'push',
          message: 'Vehicle has no driver'
        }
      ]
    }

    if (!this.app.channel(`userIds/${data.driver}`)) {
      failedReasons.push({
        channel: 'ws',
        message: 'The socket connection is closed'
      })
    }

    if (!driver.device || !driver.device.FCMId) {
      failedReasons.push({
        channel: 'push',
        message: 'Driver doesn\'t have FCM token'
      })
    }

    return failedReasons
  }

  async updateConnectionHistory (data) {
    const [conn] = await this.app.service('connection-histories').find({
      paginate: false,
      $limit: 1,
      query: {
        vehicle: data.vehicle
      }
    })

    if (conn) {
      return await this.app.service('connection-histories').patch(conn._id, {
        company: data.company,
        status: false,
        failedReasons: data.failedReasons
      })
    } else {
      return await this.app.service('connection-histories').create(data)
    }
  }
}
