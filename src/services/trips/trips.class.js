const { Service } = require('feathers-mongoose')
const moment = require('moment')
const axios = require('axios')
const { GeneralError } = require('@feathersjs/errors')
const logger = require('@hooks/logger')

const inTrip = {
  TripID: '',
  Cntr1: '',
  Seal1: '',
  Purp1: '',
  KD1: '',
  Nom1: '',
  Cntr2: '',
  Seal2: '',
  Purp2: '',
  KD2: '',
  Nom2: '',
  Cntr3: '',
  Purp3: '',
  KD3: '',
  Nom3: '',
  Cntr4: '',
  Purp4: '',
  KD4: '',
  Nom4: '',
  Trailer: '',
  TrailerSize: '',
  TrailerType: '',
  ActualSize: '',
  ULW: '',
  MLW: '',
  Status: '',
  PMNo: '',
  TTABT: '',
  TTAPPT: '',
  ContactNo: '',
  Text1: '',
  Text2: ''
}

exports.Trips = class Trips extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async survey (data) {
    const serviceAdaptorUrl = this.app.get('serviceAdaptorUrl')
    if (data.containers.length > 0) {
      let cntr1 = ''
      let text1 = ''
      let cntr2 = ''
      let text2 = ''
      let jobAreaId = ''
      let jobSource = ''
      for (let i = 0; i < data.containers.length; i++) {
        // get job data
        const job = await this.app.service('jobs').find({
          query: {
            $limit: 1,
            tripId: data.tripId,
            containerNumber: data.containers[i].containerNumber,
            jobStatus: 'in progress'
          },
          paginate: false
        })

        if (job.length > 0) {
          jobAreaId = job[0].area_id
          jobSource = job[0].source
          await this.app.service('jobs').patch(job[0]._id, { survey: 'YES' })
          if (i === 0) {
            cntr1 = job[0].containerNumber
            text1 = data.containers[i].conditions
          } else if (i === 1) {
            cntr2 = job[0].containerNumber
            text2 = data.containers[i].conditions
          }
        }
      }
      // send inward message
      const timeNow = Date.now()
      const timeNowFormated = moment(timeNow).format('YYYY-MM-DD hh:mm:ss.SSS')
      const jsonBody = {
        type: 'trip',
        direction: 'in',
        text: '',
        out_hdr: null,
        out_trip: null,
        in_yard: null,
        in_hdr: {
          date_time: timeNowFormated,
          area_id: jobAreaId,
          source: data.vehicleNumber,
          source_hostname: 'MDT',
          destination: jobSource,
          submit_time: timeNowFormated
        },
        in_trip: null,
        job: null
      }

      inTrip.Status = 'X'
      inTrip.TripID = data.tripId
      inTrip.PMNo = data.vehicleNumber
      inTrip.Cntr1 = cntr1
      inTrip.Text1 = text1
      inTrip.Cntr2 = cntr2
      inTrip.Text2 = text2

      jsonBody.in_trip = inTrip
      // send jsonBody to service adaptor
      await axios.post(serviceAdaptorUrl, jsonBody)
        .catch(function (error) {
          logger.error('Oops! something wrong', error)
          const newError = new GeneralError('Error when communicate with service adaptor')

          throw newError
        })

      // write event logs
      const eventLogs = {
        action: 'trips-survey',
        data: jsonBody
      }
      await this.app.service('event-logs').create(eventLogs)

      // block for send data to message service
      // get vehicle data
      const getVehicleData = await this.app.service('vehicles').find({
        query: {
          $limit: 1,
          vehicleNo: data.vehicleNumber
        },
        paginate: false
      })

      if (getVehicleData.length > 0) {
        const messageText = 'eSurvey ' + data.tripId + ' ' + cntr1 + ' ' + text1 + ' ' + cntr1 + ' ' + text1
        const messageData = {
          flagFrom: 'inward',
          type: 'psa-message',
          vehicle: getVehicleData[0]._id,
          company: getVehicleData[0].company,
          messages: {
            type: 'text',
            content: messageText,
            sender: getVehicleData[0].driver
          }
        }

        // get conversation data
        const getConversation = await this.app.service('conversations').find({
          query: {
            $limit: 1,
            type: 'psa-message',
            vehicle: getVehicleData[0]._id,
            company: getVehicleData[0].company
          },
          paginate: false
        })

        if (getConversation.length > 0) {
          messageData.conversationId = getConversation[0]._id
        }

        // send message via messages service
        await this.app.service('messages').create(messageData)
      }
      // end block
    }
    return data
  }

  async inPosition (data) {
    const serviceAdaptorUrl = this.app.get('serviceAdaptorUrl')
    const timeNow = Date.now()
    const timeNowFormated = moment(timeNow).format('YYYY-MM-DD hh:mm:ss.SSS')
    const jsonBody = {
      type: 'yard',
      direction: 'in',
      text: '',
      out_hdr: null,
      out_trip: null,
      in_trip: null,
      job: null,
      in_hdr: {
        date_time: timeNowFormated,
        area_id: 'PN',
        source: data.vehicleNumber,
        source_hostname: 'MDT',
        destination: 'HLARSYS',
        submit_time: timeNowFormated
      },
      in_yard: {
        sender_system_name: 'MDT',
        sender_info: data.vehicleNumber,
        event_dt: timeNowFormated,
        trans_id: 'HLAR',
        pm_m: data.vehicleNumber,
        terminal_c: '',
        block_n: data.block_n,
        slot_n: data.slot_n,
        latitude: data.lat,
        longitude: data.long,
        north_south: data.north_south,
        east_west: data.east_west,
        trigger_system_c: '',
        filler: ''
      }
    }

    // send jsonBody to service adaptor
    await axios.post(serviceAdaptorUrl, jsonBody)
      .catch(function (error) {
        logger.error('Oops! something wrong', error)
        const newError = new GeneralError('Error when communicate with service adaptor')

        throw newError
      })

    // block for send data to message service
    // get vehicle data
    const getVehicleData = await this.app.service('vehicles').find({
      query: {
        $limit: 1,
        vehicleNo: data.vehicleNumber
      },
      paginate: false
    })

    if (getVehicleData.length > 0) {
      const messageText = 'In Position ' + data.vehicleNumber + ' ' + data.lat + ' ' + data.long
      const messageData = {
        flagFrom: 'inward',
        type: 'psa-message',
        vehicle: getVehicleData[0]._id,
        company: getVehicleData[0].company,
        messages: {
          type: 'text',
          content: messageText,
          sender: getVehicleData[0].driver
        }
      }

      // get conversation data
      const getConversation = await this.app.service('conversations').find({
        query: {
          $limit: 1,
          type: 'psa-message',
          vehicle: getVehicleData[0]._id,
          company: getVehicleData[0].company
        },
        paginate: false
      })

      if (getConversation.length > 0) {
        messageData.conversationId = getConversation[0]._id
      }

      // send message via messages service
      await this.app.service('messages').create(messageData)

      // write event logs
      const eventLogs = {
        action: 'trips-in-position',
        data: jsonBody
      }
      await this.app.service('event-logs').create(eventLogs)
    }

    return data
  }

  async pregateEnquiry (data) {
    const serviceAdaptorUrl = this.app.get('serviceAdaptorUrl')
    const timeNow = Date.now()
    const timeNowFormated = moment(timeNow).format('YYYY-MM-DD hh:mm:ss.SSS')
    const jsonBody = {
      type: 'trip',
      direction: 'in',
      text: '',
      out_hdr: null,
      out_trip: null,
      in_yard: null,
      in_hdr: {
        date_time: timeNowFormated,
        area_id: 'PN',
        source: data.vehicleNumber,
        source_hostname: 'MDT',
        destination: 'HCS',
        submit_time: timeNowFormated
      },
      in_trip: null
    }

    inTrip.Status = 'E'
    inTrip.PMNo = data.vehicleNumber
    inTrip.TripID = data.tripId

    jsonBody.in_trip = inTrip
    // insert to event log
    const eventLogs = {
      action: 'jobs-pregate-enquiry',
      data: jsonBody
    }
    await this.app.service('event-logs').create(eventLogs)

    // send jsonBody to service adaptor
    await axios.post(serviceAdaptorUrl, jsonBody)
      .catch(function (error) {
        logger.error('Oops! something wrong', error)
        const newError = new GeneralError('Error when communicate with service adaptor')

        throw newError
      })

    // block for send data to message service
    // get vehicle data
    const getVehicleData = await this.app.service('vehicles').find({
      query: {
        $limit: 1,
        vehicleNo: data.vehicleNumber
      },
      paginate: false
    })

    if (getVehicleData.length > 0) {
      const messageText = 'Pregate Enquiry ' + data.vehicleNumber + ' ' + data.tripId
      const messageData = {
        flagFrom: 'inward',
        type: 'psa-message',
        vehicle: getVehicleData[0]._id,
        company: getVehicleData[0].company,
        messages: {
          type: 'text',
          content: messageText,
          sender: getVehicleData[0].driver
        }
      }

      // get conversation data
      const getConversation = await this.app.service('conversations').find({
        query: {
          $limit: 1,
          type: 'psa-message',
          vehicle: getVehicleData[0]._id,
          company: getVehicleData[0].company
        },
        paginate: false
      })

      if (getConversation.length > 0) {
        messageData.conversationId = getConversation[0]._id
      }

      // send message via messages service
      await this.app.service('messages').create(messageData)
    }
    // end block

    return data
  }

  async ddDlEnquiry (data) {
    const serviceAdaptorUrl = this.app.get('serviceAdaptorUrl')
    const timeNow = Date.now()
    const timeNowFormated = moment(timeNow).format('YYYY-MM-DD hh:mm:ss.SSS')
    const jsonBody = {
      type: 'trip',
      direction: 'in',
      text: '',
      out_hdr: null,
      out_trip: null,
      in_yard: null,
      in_hdr: {
        date_time: timeNowFormated,
        area_id: 'PN',
        source: data.vehicleNumber,
        source_hostname: 'MDT',
        destination: 'HCS',
        submit_time: timeNowFormated
      },
      in_trip: null
    }

    inTrip.Status = 'D'
    inTrip.PMNo = data.vehicleNumber
    inTrip.Cntr1 = data.cntrOne
    inTrip.Cntr2 = data.cntrTwo
    inTrip.ContactNo = data.contactNo

    jsonBody.in_trip = inTrip

    // insert to event log
    const eventLogs = {
      action: 'trips-dd-dl-enquiry',
      data: jsonBody
    }
    await this.app.service('event-logs').create(eventLogs)

    // send jsonBody to service adaptor
    await axios.post(serviceAdaptorUrl, jsonBody)
      .catch(function (error) {
        logger.error('Oops! something wrong', error)
        const newError = new GeneralError('Error when communicate with service adaptor')

        throw newError
      })

    // block for send data to message service
    // get vehicle data
    const getVehicleData = await this.app.service('vehicles').find({
      query: {
        $limit: 1,
        vehicleNo: data.vehicleNumber
      },
      paginate: false
    })

    if (getVehicleData.length > 0) {
      const messageText = 'DD/DL Enquiry ' + data.vehicleNumber + ' ' + data.cntrOne + ' ' + data.cntrTwo + ' ' + data.contactNo
      const messageData = {
        flagFrom: 'inward',
        type: 'psa-message',
        vehicle: getVehicleData[0]._id,
        company: getVehicleData[0].company,
        messages: {
          type: 'text',
          content: messageText,
          sender: getVehicleData[0].driver
        }
      }

      // get conversation data
      const getConversation = await this.app.service('conversations').find({
        query: {
          $limit: 1,
          type: 'psa-message',
          vehicle: getVehicleData[0]._id,
          company: getVehicleData[0].company
        },
        paginate: false
      })

      if (getConversation.length > 0) {
        messageData.conversationId = getConversation[0]._id
      }

      // send message via messages service
      await this.app.service('messages').create(messageData)
    }

    return data
  }

  async createPregate (data) {
    const serviceAdaptorUrl = this.app.get('serviceAdaptorUrl')
    const timeNow = Date.now()
    const timeNowFormated = moment(timeNow).format('YYYY-MM-DD hh:mm:ss.SSS')
    const jsonBody = {
      type: 'trip',
      direction: 'in',
      text: '',
      out_hdr: null,
      out_trip: null,
      in_yard: null,
      in_hdr: {
        date_time: timeNowFormated,
        area_id: 'PN',
        source: data.vehicleNumber,
        source_hostname: 'MDT',
        destination: 'HCS',
        submit_time: timeNowFormated
      },
      in_trip: null
    }

    inTrip.Cntr1 = data.cntrOne
    inTrip.Purp1 = data.purpOne
    inTrip.KD1 = data.kdOne
    inTrip.Nom1 = data.nomOne
    inTrip.Cntr2 = data.cntrTwo
    inTrip.Purp2 = data.purpTwo
    inTrip.KD2 = data.kdTwo
    inTrip.Nom2 = data.nomTwo
    inTrip.Cntr3 = data.cntrThree
    inTrip.Purp3 = data.purpThree
    inTrip.KD3 = data.kdThree
    inTrip.Nom3 = data.nomThree
    inTrip.Cntr4 = data.cntrFour
    inTrip.Purp4 = data.purpFour
    inTrip.KD4 = data.kdFour
    inTrip.Nom4 = data.nomFour
    inTrip.Trailer = data.trailer
    inTrip.TrailerType = data.trailerType
    inTrip.TrailerSize = data.trailerSize
    inTrip.ULW = data.ulw
    inTrip.MLW = data.mlw
    inTrip.Status = 'V'
    inTrip.PMNo = data.vehicleNumber

    jsonBody.in_trip = inTrip

    // insert to event log
    const eventLogs = {
      action: 'trips-create-pregate',
      data: jsonBody
    }
    await this.app.service('event-logs').create(eventLogs)

    // send jsonBody to service adaptor
    await axios.post(serviceAdaptorUrl, jsonBody)
      .catch(function (error) {
        logger.error('Oops! something wrong', error)

        const newError = new GeneralError('Error when communicate with service adaptor')

        throw newError
      })

    // block for send data to message service
    // get vehicle data
    const getVehicleData = await this.app.service('vehicles').find({
      query: {
        $limit: 1,
        vehicleNo: data.vehicleNumber
      },
      paginate: false
    })

    if (getVehicleData.length > 0) {
      const messageText = 'Create pregate ' + data.trailer + ' ' + data.trailerSize + ' ' + data.trailerType + ' ' + data.cntrOne + ' ' + data.purpOne + ' ' + data.kdOne + ' ' + data.nomOne + ' ' + data.cntrTwo + ' ' + data.purpTwo + ' ' + data.kdTwo + ' ' + data.nomTwo + ' ' + data.cntrThree + ' ' + data.purpThree + ' ' + data.kdThree + ' ' + data.nomThree + ' ' + data.cntrFour + ' ' + data.purpFour + ' ' + data.kdFour + ' ' + data.nomFour
      const messageData = {
        flagFrom: 'inward',
        type: 'psa-message',
        vehicle: getVehicleData[0]._id,
        company: getVehicleData[0].company,
        messages: {
          type: 'text',
          content: messageText,
          sender: getVehicleData[0].driver
        }
      }

      // get conversation data
      const getConversation = await this.app.service('conversations').find({
        query: {
          $limit: 1,
          type: 'psa-message',
          vehicle: getVehicleData[0]._id,
          company: getVehicleData[0].company
        },
        paginate: false
      })

      if (getConversation.length > 0) {
        messageData.conversationId = getConversation[0]._id
      }

      // send message via messages service
      await this.app.service('messages').create(messageData)
    }

    return data
  }
}
