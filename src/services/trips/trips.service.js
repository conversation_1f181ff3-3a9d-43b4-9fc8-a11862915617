// Initializes the `trips` service on path `/trips`
const { Trips } = require('./trips.class')
const createModel = require('../../models/trips.model')
const hooks = require('./trips.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$exists'],
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/trips', new Trips(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('trips')

  // route for survey
  app.use('/trips/survey', {
    async create (data) {
      const survey = await service.survey(data)

      return survey
    }
  })

  // route for in position
  app.use('/trips/inposition', {
    async create (data) {
      const inposition = await service.inPosition(data)

      return inposition
    }
  })

  // route for pregate enquiry
  app.use('/trips/pregate-enquiry', {
    async create (data) {
      const pregateEnquiry = await service.pregateEnquiry(data)

      return pregateEnquiry
    }
  })

  // route for DD/DL Enquiry
  app.use('/trips/dd-dl-enquiry', {
    async create (data) {
      const ddDlEnquiry = await service.ddDlEnquiry(data)

      return ddDlEnquiry
    }
  })

  app.use('/trips/create-pregate', {
    async create (data) {
      const createPregate = await service.createPregate(data)

      return createPregate
    }
  })

  service.hooks(hooks)
}
