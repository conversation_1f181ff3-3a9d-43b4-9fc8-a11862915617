// Initializes the `usersOnline` service on path `/users-online`
const { UsersOnline } = require('./users-online.class')
const hooks = require('./users-online.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/users-online', new UsersOnline(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('users-online')

  service.publish('resultUserOnline', () => app.channel('authenticated'))

  service.hooks(hooks)
}
