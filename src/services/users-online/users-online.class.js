/* eslint-disable no-unused-vars */
const logger = require('@hooks/logger')

exports.UsersOnline = class UsersOnline {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
    this.events = ['broadcast', 'resultUserOnline']
  }

  async find (params) {
    const deviceOnline = {
      mobile: 0,
      portal: 0
    }
    const activeUsers = await this.app.service('users').Model.aggregate([
      {
        $match: {
          device: {
            $ne: null
          }
        }
      },
      {
        $group: {
          _id: '$device.os',
          count: {
            $sum: 1
          }
        }
      }
    ])
    activeUsers.forEach((item) => {
      if (item._id === 'android' || item._id === 'ios') {
        deviceOnline.mobile += item.count
      } else {
        deviceOnline.portal += item.count
      }
    })
    logger.debug(`Online user mobile: ${deviceOnline.mobile}, portal: ${deviceOnline.portal}`)
    await this.app.service('users-online').emit('resultUserOnline', deviceOnline)

    return []
  }
}
