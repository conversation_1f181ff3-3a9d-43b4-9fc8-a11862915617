const { Service } = require('feathers-mongoose')

exports.SystemSettings = class SystemSettings extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async manageSettings (data, params) {
    const getSetting = await this.app.service('system-settings').find({
      paginate: false
    })
    if (getSetting.length > 0) {
      await this.app.service('system-settings').patch(getSetting[0]._id, data, params)
    } else {
      await this.app.service('system-settings').create(data, params)
    }
    return data
  }
}
