const { authenticate } = require('@feathersjs/authentication').hooks
const { disallow, iff } = require('feathers-hooks-common')

const isTracking = () => async ctx => {
  if (ctx.params.headers) {
    if (ctx.params.headers.tracking) { return false } else { return true }
  } else {
    return true
  }
}

module.exports = {
  before: {
    all: [iff(isTracking(), authenticate('jwt'))],
    find: [],
    get: [],
    create: [],
    update: [ctx => {
      ctx.data.updatedBy = ctx.params.user ? ctx.params.user._id : null

      return ctx
    }],
    patch: [ctx => {
      ctx.data.updatedBy = ctx.params.user ? ctx.params.user._id : null

      return ctx
    }],
    remove: disallow()
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
