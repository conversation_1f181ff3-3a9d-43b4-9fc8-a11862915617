// Initializes the `system-settings` service on path `/system-settings`
const { SystemSettings } = require('./system-settings.class')
const createModel = require('../../models/system-settings.model')
const hooks = require('./system-settings.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$options', '$regex', '$exists']
  }

  // Initialize our service with any options it requires
  app.use('/system-settings', new SystemSettings(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('system-settings')

  app.use('/system-settings/manage', {
    async create (data, params) {
      const manageSettings = await service.manageSettings(data, params)

      return manageSettings
    }
  })

  service.publish('patched', () => app.channel('authenticated'))

  service.hooks(hooks)
}
