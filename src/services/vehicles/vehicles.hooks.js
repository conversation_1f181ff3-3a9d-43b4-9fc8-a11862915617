const _ = require('lodash')
const { BadRequest } = require('@feathersjs/errors')
const logger = require('@hooks/logger')
const checkPagination = require('@hooks/check-pagination')
const authenticateOrWhitelist = require('@hooks/authenticate-or-whitelist')

const changeTotalVehicle = () => ctx => {
  ctx.app.service('vehicles').emit('total-vehicles', { method: ctx.method, data: ctx.result })
}

const removeVehicleDriverData = () => async ctx => {
  if ((ctx.method === 'patch' && !ctx.data.driver) || ctx.method === 'remove') {
    logger.debug(`delete vehicle-drivers data:${ctx.id}`)
    await ctx.app.service('vehicle-drivers').remove(null, {
      query: {
        vehicle: ctx.id
      }
    })
  }
}

const vehicleTakeOver = () => async ctx => {
  // handle driver validation
  if (!_.isUndefined(ctx.data.driver)) {
    if (ctx.data.driver != null) {
      // get current vehicle driver to prepare send notification via socket
      const getVehicleDriver = await ctx.app.service('vehicles').get(ctx.id)
      let vehicleDriver = null
      if (getVehicleDriver.driver != null) {
        vehicleDriver = getVehicleDriver.driver
      }

      // driver validation
      const getVehicle = await ctx.app.service('vehicles').find({
        query: {
          driver: ctx.data.driver,
          _id: {
            $ne: ctx.id
          }
        },
        paginate: false
      })
      if (getVehicle.length > 0) {
        // check if the driver still have a jobs
        const vehicle = getVehicle[0]
        const getJobs = await ctx.app.service('jobs').find({
          query: {
            truckNumber: vehicle.vehicleNo,
            $or: [
              { jobStatus: 'new' },
              { jobStatus: 'in progress' }
            ]
          },
          paginate: false
        })

        if (getJobs.length > 0) {
          const newError = new BadRequest('The driver still on a trip', {
            driver: ctx.data.driver
          })

          throw newError
        }

        // take out driver from the old vehicle
        await ctx.app.service('vehicles').patch(vehicle._id, { driver: null }, ctx.params)
      }

      // send notification via socket to current driver
      if (vehicleDriver != null) {
        const msgData = {
          type: 'private',
          company: getVehicleDriver.company,
          members: [
            vehicleDriver
          ],
          messages: {
            type: 'notification',
            sender: ctx.data.driver,
            content: 'vehicle take over'
          }
        }

        await ctx.app.service('messages').create(msgData)
      }
    }
  }
}

module.exports = {
  before: {
    all: [authenticateOrWhitelist()],
    find: [checkPagination()],
    get: [],
    create: [
      async ctx => {
        const getVehicle = await ctx.app.service('vehicles').find({
          query: {
            vehicleNo: ctx.data.vehicleNo,
            company: ctx.data.company
          },
          paginate: false
        })

        // check if vehicle number exist
        if (getVehicle.length > 0) {
          const newError = new BadRequest('Vehicle number already exists in this company')
          throw newError
        }

        // check if create vehicle status active
        if (ctx.data.status) {
          // get active vehicle in other company
          const getVehicleActive = await ctx.app.service('vehicles').find({
            query: {
              vehicleNo: ctx.data.vehicleNo,
              company: {
                $ne: ctx.data.company
              },
              status: true
            },
            paginate: false
          })

          // get company name
          if (getVehicleActive.length) {
            const company = await ctx.app.service('groups').find({
              query: {
                _id: getVehicleActive[0].company
              },
              paginate: false
            })

            // check if vehicle number exist
            if (getVehicleActive.length > 0) {
              const newError = new BadRequest(`Found active vehicle ${ctx.data.vehicleNo} in company ${company[0].name}`)
              throw newError
            }
          }

          if (ctx.data.driver) {
            const hasVehicle = await ctx.app.service('vehicles').find({
              query: {
                driver: ctx.data.driver
              },
              paginate: false
            })

            if (hasVehicle.length) {
              await ctx.app.service('vehicles').patch(hasVehicle[0]._id, { driver: null })
            }
          }
        }
      }
    ],
    update: [
      async ctx => {
        const getVehicle = await ctx.app.service('vehicles').find({
          query: {
            vehicleNo: ctx.data.vehicleNo,
            company: ctx.data.company,
            _id: {
              $ne: ctx.id
            }
          },
          paginate: false
        })

        // check if vehicle number exist
        if (getVehicle.length > 0) {
          const newError = new BadRequest('Vehicle number already exists in this company')
          throw newError
        }

        // check if change vehicle status active
        if (ctx.data.status) {
          // get active vehicle in other company
          const getVehicleActive = await ctx.app.service('vehicles').find({
            query: {
              vehicleNo: ctx.data.vehicleNo,
              company: {
                $ne: ctx.data.company
              },
              status: true
            },
            paginate: false
          })

          // get company name
          if (getVehicleActive.length) {
            const company = await ctx.app.service('groups').find({
              query: {
                _id: getVehicleActive[0].company
              },
              paginate: false
            })

            // check if vehicle number exist
            if (getVehicleActive.length > 0) {
              const newError = new BadRequest(`Found active vehicle ${ctx.data.vehicleNo} in company ${company[0].name}`)
              throw newError
            }
          }
        }
      }
    ],
    patch: [
      async ctx => {
        const getVehicle = await ctx.app.service('vehicles').find({
          query: {
            vehicleNo: ctx.data.vehicleNo,
            company: ctx.data.company,
            _id: {
              $ne: ctx.id
            }
          },
          paginate: false
        })

        // check if vehicle number exist
        if (getVehicle.length > 0) {
          const newError = new BadRequest('Vehicle number already exists in this company')
          throw newError
        }

        // check if change vehicle status active
        if (ctx.data.status) {
          // get active vehicle in other company
          const getVehicleActive = await ctx.app.service('vehicles').find({
            query: {
              vehicleNo: ctx.data.vehicleNo,
              company: {
                $ne: ctx.data.company
              },
              status: true
            },
            paginate: false
          })

          // get company name
          if (getVehicleActive.length) {
            const company = await ctx.app.service('groups').find({
              query: {
                _id: getVehicleActive[0].company
              },
              paginate: false
            })

            // check if vehicle number exist
            if (getVehicleActive.length > 0) {
              const newError = new BadRequest(`Found active vehicle ${ctx.data.vehicleNo} in company ${company[0].name}`)
              throw newError
            }
          }

          if (ctx.data.driver) {
            const hasVehicle = await ctx.app.service('vehicles').find({
              query: {
                driver: ctx.data.driver
              },
              paginate: false
            })

            if (hasVehicle.length) {
              await ctx.app.service('vehicles').patch(hasVehicle[0]._id, { driver: null })
            }
          }
        }
      },
      removeVehicleDriverData(),
      vehicleTakeOver()
    ],
    remove: [removeVehicleDriverData()]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [changeTotalVehicle()],
    update: [],
    patch: [],
    remove: [changeTotalVehicle()]
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
