const { Service } = require('feathers-mongoose')
const logger = require('@hooks/logger')
const toObjectId = require('mongoose').Types.ObjectId
const mongoose = require('mongoose')
const VehicleResult = require('../../beans/vehicleResult')
const _ = require('lodash')

exports.Vehicles = class Vehicles extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async findVehicles (params) {
    return await super.find({
      query: params.query
    })
  }

  // function to get driver vehicle
  async driverVehicle (id) {
    const vehicle = await super.find({
      query: {
        $limit: 1,
        $sort: {
          createdAt: -1
        },
        driver: id
      }
    })
    if (vehicle.total > 0) {
      return vehicle.data[0]
    }

    return {}
  }

  // function to provide vehicle confirmation
  async vehicleConfirmation (data) {
    const response = {}

    // get data new vehice
    const dataVehicle = await super.find({
      query: {
        $limit: 1,
        $sort: {
          createdAt: -1
        },
        vehicleNo: data.vehicleNo,
        company: data.company
      },
      paginate: false
    })

    // validate data exist
    if (dataVehicle.length < 1) {
      response.success = false
      response.message = 'Data vehicle not found'

      return response
    }

    const vehicle = dataVehicle[0]
    const dateNow = new Date()

    // validate vehicle is active
    if (vehicle.status === false) {
      response.success = false
      response.message = 'Vehicle is inactive'

      return response
    }

    // validate vehicle portNet
    if (vehicle.portNet === false) {
      let portNet = false
      for (let i = 0; i < vehicle.licenseList.length; i++) {
        if (vehicle.licenseList[i].portNet === true) {
          portNet = true
        }
      }

      if (portNet === false) {
        response.success = false
        response.message = 'This vehicle portNet is not active'

        return response
      }
    }

    // validate subscription license
    let vehicleSubscription = await this.app.service('subscriptions').find({
      query: {
        $limit: 1,
        company: data.company,
        license: vehicle.license
      },
      paginate: false
    })

    let vehicleSubscriptionNew

    if (vehicleSubscription.length < 1) {
      vehicleSubscriptionNew = await this.app.service('subscriptions').find({
        query: {
          $limit: 1,
          company: data.company,
          license: {
            $in: vehicle.licenseList.map(v => v.idLicense)
          }
        },
        paginate: false
      })

      if (vehicleSubscriptionNew.length < 1) {
        response.success = false
        response.message = 'This vehicle does not has a license'

        return response
      }
    }

    // validate vehicle is licensed or not
    if (vehicleSubscription.length < 1) vehicleSubscription = vehicleSubscriptionNew

    if (dateNow > vehicleSubscription[0].endDate) {
      response.success = false
      response.message = 'Vehicle license is expired'

      return response
    }

    // get data old vehicle
    const dataOldVehicle = await super.find({
      query: {
        $limit: 1,
        $sort: {
          createdAt: -1
        },
        company: data.company,
        driver: data.driver
      }
    })

    // take out driver the from old vehicle
    // send notification via socket to current driver
    if (dataOldVehicle.total > 0) {
      const oldVehicle = dataOldVehicle.data[0]
      // update driver old vehicle to null
      await super.patch(oldVehicle._id, { driver: null })
    }

    // check new vehicle has driver or not
    if (dataVehicle[0].driver !== null) {
      if (dataVehicle[0].driver !== data.driver) {
        const msgData = {
          type: 'private',
          company: dataVehicle[0].company,
          members: [
            dataVehicle[0].driver
          ],
          messages: {
            type: 'notification',
            sender: data.driver,
            content: 'vehicle take over'
          }
        }
        await this.app.service('messages').create(msgData)
      }
    }

    // update vehicle data
    const updateVehicle = await super.patch(vehicle._id, { driver: data.driver })
    if (updateVehicle) {
      const conversation = await this.app.service('conversations').find({ paginate: false, query: { type: 'group', vehicle: vehicle._id } })

      const user = await this.app.service('users').get(data.driver)

      const msgData = {
        type: 'group',
        flagFrom: 'ctr-message',
        company: updateVehicle.company,
        vehicle: updateVehicle._id,
        messages: {
          type: 'text',
          sender: user._id,
          content: `Driver ${user.fullname} has logged in`
        }
      }

      if (conversation.length > 0) msgData.conversationId = conversation[0]._id
      await this.app.service('messages').create(msgData)
    }

    // insert into vehicle drivers
    const vechileDriver = {
      vehicle: vehicle._id,
      driver: data.driver
    }
    await this.app.service('vehicle-drivers').create(vechileDriver)

    // activity logs
    const activityLogs = {
      action: 'confirmation',
      module: 'vehicles',
      user: data.driver,
      data: updateVehicle
    }
    this.app.service('activity-logs').create(activityLogs)

    // return reponse
    response.success = true
    response.vehicle = updateVehicle

    // get participantId
    let participantId = ''
    const company = await this.app.service('groups').get(data.company)
    const uen = company.groupInfo.uen || ''
    if (uen) {
      const cdiparticipant = await this.app.service('cdi-participants').find({
        query: {
          $limit: 1,
          uen
        },
        paginate: false
      })

      if (cdiparticipant.length) {
        const participantIds = cdiparticipant[0]._id
        participantId = participantIds
      }
    }

    response.participantId = participantId

    return response
  }

  async vehicleSubscription (data, params) {
    const response = {}

    // check license is active or not
    const license = await this.app.service('licences').get(data.subscriptions.license)
    if (license.status === false) {
      response.success = false
      response.message = 'This license is inactive'

      return response
    }

    // check if license is expired
    const dateNow = new Date()
    const dateExpired = new Date(data.subscriptions.endDate)
    if (dateNow > dateExpired) {
      throw new Error('This company license already expired. Please contact your administrator.')
    }

    // validate vehicle max limit
    let totalVehicle = 0
    for (let i = 0; i < data.vehicles.length; i++) {
      if (data.vehicles[i].portNet === true) {
        totalVehicle++
      }
    }

    if (totalVehicle > data.subscriptions.licenseNo) {
      throw new Error('Your vehicle exceeds the vehicle subscription quota')
    }

    // update portNet vehicle
    let licenseList = []

    for (let i = 0; i < data.vehicles.length; i++) {
      licenseList = await super.find({
        paginate: false,
        query: {
          _id: data.vehicles[i]._id
        }
      }).then(res => {
        if (res[0].licenseList) {
          return res[0].licenseList
        } else {
          return []
        }
      })

      let checkLicense = 0
      if (licenseList.length > 0) {
        for (let j = 0; j < licenseList.length; j++) {
          if (licenseList[j].idLicense) {
            if (licenseList[j].idLicense.toString() === data.subscriptions.license) {
              licenseList[j].portNet = data.vehicles[i].portNet
              checkLicense++
            }
          }
        }
      }

      if (checkLicense === 0) licenseList.push({ idLicense: data.subscriptions.license, portNet: data.vehicles[i].portNet })

      const vehicleData = {
        licenseList
      }

      await super.patch(data.vehicles[i]._id, vehicleData)
    }

    // Activity logs
    const name = await this.app.service('groups').get(data.subscriptions.company)
      .then(res => res.name)
      .catch(err => logger.error('Oops! something wrong ', err))
    const vehicles = await this.app.service('vehicles').find({
      paginate: false,
      query: {
        _id: data.vehicles.filter(v => v.portNet).length ? { $in: data.vehicles.filter(v => v.portNet).map(v => v._id) } : { $exists: false }
      }
    })
      .then(res => {
        if (!res.length) return null
        return res.map(v => v.vehicleNo).join(', ')
      })
      .catch(err => logger.error('Oops! something wrong ', err))

    // to patch updated by
    this.app.service('subscriptions').patch(data.subscriptions._id, {
      module: 'vehicleSubscriptions'
    }, params)

    await this.app.service('activity-logs').create({
      user: (params.user && params.user._id) || null,
      action: 'patch',
      module: 'vehicle-subscriptions',
      data: {
        name,
        vehicles
      }
    })

    // return reponse
    response.success = true
    response.vehicles = data

    return response
  }

  async findVehicleThreads (params) {
    const vehicles = []
    try {
      if (!params.query.company) return []

      Object.keys(params.query).forEach(function (key) {
        if (key === 'company' || key === 'group') {
          params.query[key] = toObjectId(params.query[key])
        } else if (key === '_id') {
          params.query[key].$in = params.query[key].$in.map(d => mongoose.Types.ObjectId(d))
        } else {
          params.query[key] = Boolean(params.query[key])
        }
      })

      params.query.$populate = ['driver']
      const query = params.query

      const vehiclesResult = await super._find({ paginate: false, query })
      delete query.status
      query.type = { $ne: 'private' }
      const conversation = await this.app.service('conversations').find({ paginate: false, query })
      logger.debug('conversation result', conversation)

      for (const veh of vehiclesResult) {
        const conversationsIds = []
        let latestMessageDate = null
        for (let i = 0; i < conversation.length; i++) {
          const conv = conversation[i]
          if (conv.vehicle && veh._id.toString() === conv.vehicle.toString()) {
            conversationsIds.push(conv._id)
            conversation.splice(i, 1)
          }
        }

        if (veh.driver) {
          delete veh.driver.password
        }

        const query = {
          $limit: 1,
          conversationId: conversationsIds,
          $select: ['_id', 'sentAt', 'createdAt'],
          $sort: { sentAt: -1, createdAt: -1 }
        }
        const msg = await this.app.service('messages').find({ query })
        if (msg.total) {
          latestMessageDate = msg.data[0].sentAt || msg.data[0].createdAt
        }

        vehicles.push(new VehicleResult(veh._id, veh.status, veh.portNet, veh.vehicleNo, veh.group, veh.company, veh.driver,
          veh.createdBy, veh.createdAt, veh.updatedAt, veh.__v, veh.license, conversationsIds, latestMessageDate, false))
      }
    } catch (e) {
      logger.error('Error occur on findVehicleThreads', e)
    }

    return vehicles
  }

  async find (params) {
    const useAggregate = (params.query && params.query.aggregate === 'true') || false

    if (useAggregate) {
      const objectIdField = ['company', 'driver', 'group', 'license', 'createdBy', 'updatedBy']
      const toNumberType = ['$limit', '$skip', '$sort']
      const toBoleanType = ['status', 'allData']
      const filterParams = await _.pick(params.query, ['vehicleNo', 'company', 'status', 'driver', 'group'])

      // convert to objectId
      Object.keys(filterParams).forEach((key, index) => {
        if (objectIdField.includes(String(key))) {
          filterParams[key] = mongoose.Types.ObjectId(filterParams[key])
        }
      })

      // convert params query
      Object.keys(params.query).forEach((key1, index) => {
        // convert to type number
        if (toNumberType.includes(String(key1))) {
          if (typeof params.query[key1] === 'object') {
            Object.keys(params.query[key1]).forEach((key2, index) => {
              params.query[key1][key2] = parseInt(params.query[key1][key2])
            })
          } else {
            params.query[key1] = parseInt(params.query[key1])
          }
        }

        // convert to type bolean
        if (toBoleanType.includes(String(key1))) {
          if (Object.keys(filterParams).includes(key1)) {
            filterParams[key1] = (filterParams[key1] === 'true')
          }

          params.query[key1] = (params.query[key1] === 'true')
        }
      })

      const query = [
        {
          $match: {
            ...filterParams
          }
        },
        {
          $facet: {
            total: [
              {
                $count: 'count'
              }
            ],
            data: [
              {
                $lookup: {
                  from: 'groups',
                  localField: 'company',
                  foreignField: '_id',
                  as: 'company'
                }
              },
              {
                $unwind: {
                  path: '$company',
                  preserveNullAndEmptyArrays: true
                }
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'driver',
                  foreignField: '_id',
                  as: 'driver'
                }
              },
              {
                $unwind: {
                  path: '$driver',
                  preserveNullAndEmptyArrays: true
                }
              },
              {
                $sort: params.query.$sort
              },
              {
                $limit: params.query.$limit
              },
              {
                $skip: params.query.$skip
              }
            ]
          }
        },
        {
          $unwind: '$total'
        }, {
          $project: {
            data: 1,
            total: '$total.count'
          }
        }
      ]

      if (params.query.allData) {
        query[1].$facet.allData = [
          {
            $lookup: {
              from: 'groups',
              localField: 'company',
              foreignField: '_id',
              as: 'company'
            }
          },
          {
            $unwind: {
              path: '$company',
              preserveNullAndEmptyArrays: true
            }
          },
          {
            $lookup: {
              from: 'users',
              localField: 'driver',
              foreignField: '_id',
              as: 'driver'
            }
          },
          {
            $unwind: {
              path: '$driver',
              preserveNullAndEmptyArrays: true
            }
          },
          {
            $sort: params.query.$sort
          }
        ]
        query[3].$project.allData = 1
      }

      const res = await this.app.service('vehicles').Model.aggregate(query).collation({ locale: 'en', strength: 2 })

      return res[0] || { data: [], total: 0 }
    }

    const res = super.find(params)

    return res
  }
}
