// Initializes the `vehicles` service on path `/vehicles`
const { Vehicles } = require('./vehicles.class')
const createModel = require('../../models/vehicles.model')
const hooks = require('./vehicles.hooks')
const _ = require('lodash')
const logger = require('@hooks/logger')
const { NotAuthenticated } = require('@feathersjs/errors')

const authenticateGetVehicles = () => async ctx => {
  const vehicleKey = await ctx.app.redis.HGETALL(ctx.app.get('vehicleKeyName'))

  if (!ctx.params.headers[vehicleKey.httpHeader]) throw new NotAuthenticated('API Key invalid')

  if (ctx.params.headers[vehicleKey.httpHeader] !== vehicleKey.apiKeyValue) {
    throw new NotAuthenticated('API Key invalid')
  }

  logger.debug(`${ctx.path}: authentication correct`)
}

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    events: ['total-vehicles'],
    whitelist: ['$exists', '$and', '$not', '$regex', '$options', '$populate', '$elemMatch'],
    multi: ['patch', 'remove']
  }

  // Initialize our service with any options it requires
  app.use('/vehicles', new Vehicles(options, app))

  /// BLOCK CUSTOM ROUTE ///
  // route for get driver vehicle
  app.use('/vehicles/driver', {
    async get (id) {
      const response = { vehicle: {} }
      const vehicle = await service.driverVehicle(id)

      if (_.isEmpty(vehicle)) {
        return response
      }

      response.vehicle = vehicle

      return response
    }
  })

  // route for vehicle confirmation
  app.post('/vehicles/confirmation', async (req, res) => {
    try {
      const response = await service.vehicleConfirmation(req.body, res)

      if (response.success === false) {
        res.status(500).json(response)
      } else {
        logger.info(`${req.body.vehicleNo} vehicle confirmed`)
        res.json(response)
      }
    } catch ({ message }) {
      res.status(500).json({ message })
    }
  })

  // route for vehicle subscription
  app.use('/vehicles/subscription', {
    async create (data, params) {
      try {
        const response = await service.vehicleSubscription(data, params)

        return response
      } catch ({ message }) {
        throw new Error(message)
      }
    }
  })

  app.use('/threads', {
    find (params) {
      return service.findVehicleThreads(params)
    }
  })

  app.use('/get-vehicles', {
    find (params) {
      return service.findVehicles(params)
    }
  })

  // Get our initialized service so that we can register hooks
  const service = app.service('vehicles')
  const threadsService = app.service('threads')
  const vehSubsService = app.service('vehicles/subscription')
  const getVehicleService = app.service('get-vehicles')

  const hooksGetVehicles = {
    before: {
      all: [authenticateGetVehicles()]
    }
  }

  const LogPublish = () => async (data, context) => {
    let users = []
    const query = { company: data.data.company }

    users = await app.service('users').find({ query, paginate: false })
    users = users.map(v => String(v._id))

    const newChan = app.channel('authenticated').filter(connection => {
      if (users.includes(String(connection.user._id))) {
        return true
      } else {
        return false
      }
    })

    return [
      newChan,
      app.channel('admins')
    ]
  }

  service.publish('total-vehicles', LogPublish())

  service.hooks(hooks)
  threadsService.hooks(hooks)
  vehSubsService.hooks(hooks)
  getVehicleService.hooks(hooksGetVehicles)
}
