const { authenticate } = require('@feathersjs/authentication').hooks

const checkOrganizationInactiveStatus = () => async ctx => {
  if (ctx.data.status && ctx.data.status !== 'active') ctx.app.service('groups').emit('inactive', { data: ctx.data, id: ctx.id })
}

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [],
    update: [checkOrganizationInactiveStatus()],
    patch: [checkOrganizationInactiveStatus()],
    remove: [
      async ctx => {
        const group = await ctx.app.service('groups').get(ctx.id)
          .catch(() => null)

        if (group) {
          if (group.level === 1) {
            const company = await ctx.app.service('groups').find({
              query: { level: 2, parent: ctx.id }
            })

            if (company.total) throw new Error('Group is in use')
          }

          if (group.level === 2) {
            const user = await ctx.app.service('users').find({
              query: { company: ctx.id }
            })

            const vehicle = await ctx.app.service('vehicles').find({
              query: { company: ctx.id }
            })

            const vehicleGroups = await ctx.app.service('vehicle-groups').find({
              query: { company: ctx.id }
            })

            const companySubs = await ctx.app.service('subscriptions').find({
              query: { company: ctx.id }
            })

            if (user.total || vehicle.total || vehicleGroups.total || companySubs.total) throw new Error('Company is in use')
          }
        }
      }
    ]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
