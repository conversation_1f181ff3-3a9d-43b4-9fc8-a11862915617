// Initializes the `groups` service on path `/groups`
const { Groups } = require('./groups.class')
const createModel = require('../../models/groups.model')
const hooks = require('./groups.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    events: ['inactive'],
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$populate']
  }

  // Initialize our service with any options it requires
  app.use('/groups', new Groups(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('groups')
  const groupChannel = () => async ({ data, id }, ctx) => {
    let users = []

    if (data.status !== 'active') {
      const query = data.level === 1 ? { group: id } : { company: id }
      users = await ctx.app.service('users').find({ query, paginate: false })
      users = users.map(v => String(v._id))
    }

    const newChan = app.channel('authenticated')
      .filter(connection => {
        if (users.includes(String(connection.user._id))) {
          return true
        } else {
          return false
        }
      })

    return [newChan]
  }

  service.publish('inactive', groupChannel())
  service.hooks(hooks)
}
