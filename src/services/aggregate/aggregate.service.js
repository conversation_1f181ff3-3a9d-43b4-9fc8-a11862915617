// Initializes the `aggregate` service on path `/aggregate`
const { Aggregate } = require('./aggregate.class.js')
const hooks = require('./aggregate.hooks')

module.exports = function (app) {
  const paginate = app.get('paginate')

  const options = {
    paginate
  }

  // Initialize our service with any options it requires
  app.use('/aggregate', new Aggregate(options, app))

  // Get our initialized service so that we can register hooks and filters
  const service = app.service('aggregate')

  service.hooks(hooks)
}
