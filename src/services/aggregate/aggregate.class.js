const _ = require('lodash')
const { NotFound } = require('@feathersjs/errors')

const types = {
  toObjectIdMap: require('mongoose').Types.ObjectId,
  toISODateMap: (val) => new Date(val),
  toRegexPattern: (val) => {
    return new RegExp(val.pattern, val.options)
  }
}

exports.Aggregate = class Aggregate {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async create (data, params) {
    if (!data.resource) return Promise.reject(new Error('no resource found'))
    if (!data.aggregate) return Promise.reject(new Error('no aggregate statement found'))

    const doConvert = (idx) => {
      if (!Array.isArray(data[idx])) {
        data[idx] = []
        // return Promise.reject(`${idx} is not an Array`)
      }

      data[idx].forEach(param => {
        const tempData = data.aggregate[param.index]
        if (Array.isArray(param.path)) {
          _.set(tempData, param.path, _.get(tempData, param.path).map(i => {
            return types[idx](i)
          }))
        } else {
          _.set(tempData, param.path, types[idx](_.get(tempData, param.path)))
        }
      })
    }

    if (data) {
      ['toObjectIdMap', 'toISODateMap', 'toRegexPattern'].map(i => doConvert(i))
    }

    const model = this.app.service(data.resource) ? this.app.service(data.resource).Model : null
    if (!model) return new NotFound({ message: `Model ${data.resource} not found` })

    return model
      .aggregate(data.aggregate)
      .then(data => data)
  }
}
