// Initializes the `tta-msg-queues` service on path `/tta-msg-queues`
const { TtaMsgQueues } = require('./tta-msg-queues.class')
const createModel = require('../../models/tta-msg-queues.model')
const hooks = require('./tta-msg-queues.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$populate'],
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/tta-msg-queues', new TtaMsgQueues(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('tta-msg-queues')

  service.hooks(hooks)
}
