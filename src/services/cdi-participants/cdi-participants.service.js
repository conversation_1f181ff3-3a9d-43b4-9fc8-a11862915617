// Initializes the `cdi-participants` service on path `/cdi-participants`
const { CdiParticipants } = require('./cdi-participants.class')
const createModel = require('../../models/cdi-participants.model')
const hooks = require('./cdi-participants.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/cdi-participants', new CdiParticipants(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('cdi-participants')

  service.hooks(hooks)
}
