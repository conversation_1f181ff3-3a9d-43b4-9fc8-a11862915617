const authenticateOrWhitelist = require('@hooks/authenticate-or-whitelist')
const { disallow } = require('feathers-hooks-common')
const checkPagination = require('@hooks/check-pagination')

module.exports = {
  before: {
    all: [authenticateOrWhitelist()],
    find: [checkPagination()],
    get: [],
    create: [],
    update: [disallow()],
    patch: [],
    remove: [disallow()]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
