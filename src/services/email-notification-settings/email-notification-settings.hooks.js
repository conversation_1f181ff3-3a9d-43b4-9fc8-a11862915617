const { authenticate } = require('@feathersjs/authentication').hooks
const { MethodNotAllowed, NotAcceptable, GeneralError } = require('@feathersjs/errors')
const logger = require('@hooks/logger')
const moment = require('moment')
const { iff } = require('feathers-hooks-common')
const msg = require('@utils/commonMsg')

async function filterEmailConfig (ctx) {
  let emailType = null; let scheduleType = null

  if (ctx.data.emailNotificationType) {
    const emailNotificationType = await ctx.app.redis.HGETALL('notificationEmailType')
    const resStatus = Object.keys(emailNotificationType).filter(v => emailNotificationType[v] === ctx.data.emailNotificationType)

    if (resStatus.length > 0) emailType = resStatus[0]
    else {
      const { _id } = await ctx.app.service('trailer-config').create({ name: ctx.data.emailNotificationType, type: 'emailNotificationType' })
      emailType = _id
    }
  }

  if (ctx.data.notificationScheduleType) {
    const notificationScheduleType = await ctx.app.redis.HGETALL('notificationScheduleType')
    const resStatus = Object.keys(notificationScheduleType).filter(v => notificationScheduleType[v] === ctx.data.notificationScheduleType)

    if (resStatus.length > 0) scheduleType = resStatus[0]
    else {
      const { _id } = await ctx.app.service('trailer-config').create({ name: ctx.data.notificationScheduleType, type: 'notificationScheduleType' })
      scheduleType = _id
    }
  }

  return { emailType, scheduleType }
}

async function duplicateEmail (ctx) {
  const data = await ctx.app.service('email-notification-settings').find({
    query: {
      company: ctx.data.company,
      emailNotificationType: ctx.data.emailNotificationType
    },
    paginate: false
  })

  if (data.length > 0) throw new NotAcceptable(msg.DUPLICATE_EMAIL_TYPE)
}

const getConfig = () => async ctx => {
  const query = []
  if (ctx.params.query.emailNotificationType) {
    const emailNotificationType = await ctx.app.redis.HGETALL('notificationEmailType')
    const res = Object.keys(emailNotificationType).filter(v => emailNotificationType[v] === ctx.params.query.emailNotificationType)

    query.push({ emailNotificationType: { $in: res } })
  }
  if (ctx.params.query.notificationScheduleType) {
    const notificationScheduleType = await ctx.app.redis.HGETALL('notificationScheduleType')
    const res = Object.keys(notificationScheduleType).filter(v => notificationScheduleType[v] === ctx.params.query.notificationScheduleType)

    query.push({ notificationScheduleType: { $in: res } })
  }

  ctx.params.query.$or = query
  delete ctx.params.query.emailNotificationType
  delete ctx.params.query.notificationScheduleType

  return ctx
}

const validation = () => async ctx => {
  const field = ['company', 'emailNotificationType', 'enabled', 'recipients', 'daysBeforeNotification', 'notificationScheduleType']

  for (let i = 0; i < field.length; i++) {
    if (ctx.data[field[i]] === null || ctx.data[field[i]] === '' || typeof ctx.data[field[i]] === 'undefined') throw new GeneralError(msg[`${field[i].toUpperCase()}_EMPTY`])
  }

  const company = await ctx.app.service('groups').get(ctx.data.company).catch(err => logger.error(msg.GROUP_RETRIEVE, ctx.data.company, err))
  if (company.status !== 'active') throw new NotAcceptable(msg.COMPANY_INACTIVE)

  const notifConf = await ctx.app.redis.keys('notification*')

  if (notifConf.length > 0) await duplicateEmail(ctx)

  // eslint-disable-next-line no-useless-escape
  if (!ctx.data.recipients.match(/^[\w-\.+]+@([\w-]+\.)+[\w-]{2,4}$/g)) throw new NotAcceptable(msg.EMAIL_NOT_VALID)

  if (ctx.data.notificationScheduleType === 'Specific Date' && !ctx.data.notificationDateTime) throw new NotAcceptable(msg.REQUIRED_DATE_TIME)

  ctx.data.createdBy = ctx.params.user._id

  return ctx
}

const createConfig = () => async ctx => {
  const redisNotifcationConf = await ctx.app.redis.keys('notification*')

  if (redisNotifcationConf.length > 0) {
    const { emailType, scheduleType } = await filterEmailConfig(ctx)

    if (emailType) ctx.data.emailNotificationType = emailType
    if (scheduleType) ctx.data.notificationScheduleType = scheduleType
  } else {
    const config = []

    if (ctx.data.emailNotificationType) config.push({ name: ctx.data.emailNotificationType, type: 'emailNotificationType' })
    if (ctx.data.notificationScheduleType) config.push({ name: ctx.data.notificationScheduleType, type: 'notificationScheduleType' })

    const confData = await ctx.app.service('trailer-config').create(config)

    for (let i = 0; i < confData.length; i++) {
      ctx.data[confData[i].type] = confData[i]._id
    }
  }

  if (ctx.data.notificationDateTime) ctx.data.notificationDateTime = moment(ctx.data.notificationDateTime, 'DD-MM-YYYY').format('MM/DD/YYYY HH:mm:ss')

  return ctx
}

const patchConfig = () => async ctx => {
  if (!ctx.id) throw new NotAcceptable(msg.ID_EMPTY)
  Object.keys(ctx.data).forEach(v => {
    if (v !== 'notificationDateTime' && ctx.data[v] === null) throw new GeneralError(msg[`${v.toUpperCase()}_EMPTY`])
  })

  const data = await ctx.app.service('email-notification-settings').get(ctx.id, {
    query: {
      $populate: [
        { path: 'company' }, { path: 'emailNotificationType' }, { path: 'notificationScheduleType' }
      ]
    }
  }).catch(err => logger.error('Oops! something wrong ', err))

  if (!ctx.data.company) ctx.data.company = data.company._id
  if (ctx.data.emailNotificationType && data.emailNotificationType !== ctx.data.emailNotificationType) await duplicateEmail(ctx)

  if (ctx.data.notificationScheduleType && ctx.data.notificationScheduleType === 'Specific Date' && ctx.data.notificationDateTime === null) throw new NotAcceptable(msg.REQUIRED_DATE_TIME)

  if (ctx.data.notificationDateTime) ctx.data.notificationDateTime = moment(ctx.data.notificationDateTime, 'DD-MM-YYYY').format('MM/DD/YYYY HH:mm:ss')

  const { emailType, scheduleType } = await filterEmailConfig(ctx)
  if (emailType) ctx.data.emailNotificationType = emailType
  if (scheduleType) ctx.data.notificationScheduleType = scheduleType

  ctx.updateBy = ctx.params.user._id

  return ctx
}

const deleteData = () => async ctx => {
  if (ctx.params.user.group) {
    const data = await ctx.app.service('email-notification-settings').get(ctx.id, {
      query: {
        $populate: [{ path: 'company' }, { path: 'emailNotificationType' }, { path: 'notificationScheduleType' }]
      }
    }).catch(err => logger.error('Oops! something wrong', err))
    if (data.company._id.toString() !== ctx.params.user.company.toString()) throw new MethodNotAllowed(msg.COMPANY_NOT_SAME)
  }

  return ctx
}

const populate = (type) => ctx => {
  logger.debug('populate email notification settings')
  if (type === 'before') {
    ctx.params.query.$populate = [
      { path: 'company' }, { path: 'emailNotificationType' }, { path: 'notificationScheduleType' }
    ]
  } else {
    if (ctx.result.data && ctx.result.data.length > 0) {
      logger.debug('ctx result find', ctx.result.data)
      ctx.result.data = ctx.result.data.map(v => {
        v.emailNotificationType = v.emailNotificationType.name
        v.notificationScheduleType = v.notificationScheduleType.name

        return v
      })
    } else if (!ctx.result.data && !Array.isArray(ctx.result) && Object.keys(ctx.result).length > 0) {
      logger.debug('ctx result get', ctx.result)
      ctx.result.emailNotificationType = ctx.result.emailNotificationType.name
      ctx.result.notificationScheduleType = ctx.result.notificationScheduleType.name
    }
  }

  return ctx
}

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [iff(ctx => ctx.params.query.emailNotificationType || ctx.params.query.notificationScheduleType, getConfig()), populate('before')],
    get: [populate('before')],
    create: [validation(), createConfig()],
    update: [],
    patch: [patchConfig()],
    remove: [deleteData()]
  },

  after: {
    all: [],
    find: [populate('after')],
    get: [populate('after')],
    create: [],
    update: [],
    patch: [ctx => {
      if (Array.isArray(ctx.result)) ctx.result = ctx.result[0]

      return ctx
    }],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
