// Initializes the `email-notification-settings` service on path `/email-notification-settings`
const { EmailNotificationSettings } = require('./email-notification-settings.class')
const createModel = require('../../models/email-notification-settings.model')
const hooks = require('./email-notification-settings.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$populate'],
    multi: ['patch']
  }

  // Initialize our service with any options it requires
  app.use('/email-notification-settings', new EmailNotificationSettings(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('email-notification-settings')

  service.hooks(hooks)
}
