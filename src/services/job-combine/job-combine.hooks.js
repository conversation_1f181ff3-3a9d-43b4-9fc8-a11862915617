const { authenticate } = require('@feathersjs/authentication').hooks
const { iff } = require('feathers-hooks-common')

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [iff(
      ctx => !!ctx.result,
      async ctx => {
        const jobResult = []

        try {
          const result = ctx.result.filter(v => v._id.toString() !== ctx.data._id)

          for (let i = 0; i < result.length; i++) {
            const res = await ctx.app.service('jobs').get(result[i]._id)

            jobResult.push(res)
          }

          ctx.result = jobResult

          return ctx
        } catch (err) {
          ctx.result = jobResult

          return ctx
        }
      },
      ctx => {
        ctx.result = {}

        return ctx
      }
    )],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
