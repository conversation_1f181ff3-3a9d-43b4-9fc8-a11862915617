const logger = require('@hooks/logger')

/* eslint-disable no-unused-vars */
exports.JobCombine = class JobCombine {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async find (data, params) {
    try {
      const dataJob = await this.app.service('jobs').get(data.query._id)
      const containerN = dataJob.containerNumber || dataJob.batchNumber || dataJob.ucrN
      const formatContainer = containerN.replace(/\s/g, '').replace(/^(.{4})(.*)/, '$1 $2')

      const query = {
        truckNumber: dataJob.truckNumber,
        jobStatus: { $in: ['in progress', 'completed', 'start pickup', 'completed pickup', 'start delivery', 'completed delivery', 'rejected'] },
        combinedJobs: true
      }

      if (dataJob.tripId) {
        query.tripId = dataJob.tripId
        if (dataJob.containerNumber) {
          query.containerNumber = { $regex: new RegExp(formatContainer.replace(/\s/g, '\\s*')) }
        } else if (dataJob.batchNumber || dataJob.ucrN) {
          query.$or = [
            { batchNumber: { $regex: new RegExp(containerN.replace(/\s/g, '')) } },
            { ucrN: { $regex: new RegExp(containerN.replace(/\s/g, '')) } }
          ]
          query.tag = dataJob.tag
        }
      } else {
        query.bookingN = dataJob.bookingN
      }

      const jobs = await this.app.service('jobs').find({
        query,
        paginate: false
      }).then(res => {
        res.sort((a, b) => {
          if (a.source === 'OET' && b.source !== 'OET') {
            return -1
          } else if (b.source === 'OET' && a.source !== 'OET') {
            return 1
          } else {
            return 0
          }
        })

        return res
      })

      return jobs
    } catch (err) {
      logger.error('Something wrong when find combine job ', err)
    }
  }

  async create (data, params) {
    try {
      const dataJob = await this.app.service('jobs').get(data._id)
      if (!(dataJob.tripId || dataJob.bookingN)) return false
      const containerN = dataJob.containerNumber || dataJob.batchNumber || dataJob.ucrN
      const formatContainerNumber = containerN.replace(/\s/g, '').replace(/^(.{4})(.*)/, '$1 $2')

      const query = {
        truckNumber: dataJob.truckNumber,
        jobStatus: { $in: ['in progress', 'new'] }
      }
      if (!data.jobStatus) {
        if (dataJob.containerNumber) query.containerNumber = { $regex: new RegExp(formatContainerNumber.replace(/\s/g, '\\s*')) }
        else if (dataJob.ucrN || dataJob.batchNumber) {
          const batchNumber = dataJob.batchNumber || dataJob.ucrN
          query.$or = [
            { batchNumber },
            { ucrN: batchNumber }
          ]
        }
      }
      if (dataJob.tripId) {
        query.tripId = dataJob.tripId
        const jobTypeMap = {
          IMP: 'Import',
          Import: 'IMP',
          EXP: 'Export',
          Export: 'EXP',
          Local: 'LOCAL',
          LOCAL: 'Local'
        }

        const jobType = jobTypeMap[dataJob.jobType] || ''
        const inJobType = [dataJob.jobType, 'DC', 'DR', null]
        if (jobType) inJobType.push(jobType)
        query.jobType = { $in: inJobType }
      } else {
        query.bookingN = dataJob.bookingN
      }

      const jobs = await this.app.service('jobs').find({
        query,
        paginate: false
      })

      const groupedArray = jobs.reduce((result, obj) => {
        const containerNumber = obj.containerNumber || obj.batchNumber || obj.ucrN
        const formatContainerNumber = containerNumber.replace(/\s/g, '')
        if (!result[formatContainerNumber]) {
          result[formatContainerNumber] = []
        }
        result[formatContainerNumber].push(obj)
        return result
      }, {})

      for (const containerNumber in groupedArray) {
        const tag = []
        const containerGroup = groupedArray[containerNumber]
        containerGroup.sort((a, b) => {
          if (a.source === 'OET' && b.source !== 'OET') {
            return -1
          } else if (b.source === 'OET' && a.source !== 'OET') {
            return 1
          } else {
            return 0
          }
        }).reverse()

        const onlyOET = containerGroup.every(res => res.source === 'OET')
        if (onlyOET) return true

        const hasOET = containerGroup.some(res => (res.ucrN || res.batchNumber) && !res.containerNumber && res.source === 'OET')
        for (const obj of containerGroup) {
          const patchData = { combinedJobs: (containerGroup.length > 1) }
          const getStatus = await this.app.service('jobs').find({
            query: {
              _id: obj._id,
              $select: ['jobStatus', 'tag', 'combinedJobs']
            },
            paginate: false
          })

          if (getStatus[0].jobStatus === 'new') {
            patchData.jobStatus = 'in progress'
            patchData.jobStartDate = Date.now()
          }

          if (!dataJob.containerNumber) {
            if (!hasOET) patchData.combinedJobs = false
            if (getStatus[0].tag) tag.push(getStatus[0].tag)
            if (!getStatus[0].tag && tag.length > 0) {
              patchData.tag = tag[0]
              tag.splice(0, 1)
            }
          }

          if (getStatus[0].combinedJobs) patchData.combinedJobs = true

          await this.app.service('jobs').patch(obj._id, patchData)
        }
      }

      return jobs
    } catch (err) {
      logger.error('Something wrong when job combine ', err)
    }
  }

  async checkUpdateCombine (job, data) {
    try {
      if (!(job.tripId || job.bookingN)) return false
      const jobContainerN = job.containerNumber || job.batchNumber || job.ucrN
      const query = {
        truckNumber: job.truckNumber,
        jobStatus: { $in: ['in progress', 'new'] }
      }
      if (job.tripId) {
        query.tripId = job.tripId
      } else {
        query.bookingN = job.bookingN
      }
      if (!job.containerNumber) {
        query.tag = job.tag
      }

      const jobs = await this.app.service('jobs').find({
        query,
        paginate: false
      })

      const groupedArray = jobs.reduce((result, obj) => {
        const containerNumber = obj.containerNumber || obj.batchNumber || obj.ucrN
        const formatContainerNumber = containerNumber.replace(/\s/g, '')
        if (!result[formatContainerNumber]) {
          result[formatContainerNumber] = []
        }
        result[formatContainerNumber].push(obj)
        return result
      }, {})

      for (const containerNumber in groupedArray) {
        const containerGroup = groupedArray[containerNumber]
        for (const obj of containerGroup) {
          const objContainerN = obj.containerNumber || obj.batchNumber || obj.ucrN
          const patchData = { combinedJobs: true }
          if (jobContainerN.replace(/\s/g, '') === objContainerN.replace(/\s/g, '') && obj.combinedJobs) {
            if (obj.movementId) patchData.containerNumber = data.containerNumber.replace(/\s/g, '')
            else patchData.containerNumber = data.containerNumber.replace(/\s/g, '').replace(/^(.{4})(.*)/, '$1 $2')
            patchData.role = data.role
            patchData.sender = data.sender

            const getStatus = await this.app.service('jobs').find({
              query: {
                _id: obj._id,
                $select: ['jobStatus']
              },
              paginate: false
            })

            if (getStatus[0].jobStatus === 'new') {
              patchData.jobStatus = 'in progress'
              patchData.jobStartDate = Date.now()
            }

            await this.app.service('jobs').patch(obj._id, patchData)
          }
        }
      }
    } catch (err) {
      logger.error('Something wrong when edit container & job combine', err)
    }
  }

  async finalCombine (job, data) {
    try {
      if (!(job.tripId || job.bookingN)) return false
      const dataJobContainerN = job.containerNumber || job.batchNumber || job.ucrN
      const query = {
        truckNumber: job.truckNumber,
        jobStatus: { $in: ['in progress', 'completed'] },
        combinedJobs: true,
        _id: { $ne: job._id }
      }
      if (job.tripId) {
        query.tripId = job.tripId
      } else {
        query.bookingN = job.bookingN
      }

      const jobs = await this.app.service('jobs').find({
        query,
        paginate: false
      })

      // update jobs status for related jobs
      for (let i = 0; i < jobs.length; i++) {
        const jobContainerN = jobs[i].containerNumber || jobs[i].batchNumber || jobs[i].ucrN
        if (dataJobContainerN.replace(/\s/g, '') === jobContainerN.replace(/\s/g, '')) {
          if (data.jobStatus === 'completed pickup' && job.jobType.toLowerCase() === 'import') {
            data.jobStatus = 'completed'
            data.jobState = 8
          }

          if (jobs[i].jobStatus !== 'completed') {
            if (data.jobStatus === 'completed' && jobs[i].source === 'OET') return true
            await this.app.service('jobs').patch(jobs[i]._id, data)
          }
        }
      }
    } catch (err) {
      logger.error('Something wrong when reject job combine', err)
    }
  }
}
