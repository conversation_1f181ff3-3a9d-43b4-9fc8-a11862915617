// Initializes the `job-combine` service on path `/job-combine`
const { Job<PERSON>omb<PERSON> } = require('./job-combine.class')
const hooks = require('./job-combine.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate'),
    whitelist: ['$regex']
  }

  // Initialize our service with any options it requires
  app.use('/job-combine', new JobCombine(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('job-combine')

  service.hooks(hooks)
}
