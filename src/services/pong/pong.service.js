// Initializes the `pong` service on path `/pong`
const { Pong } = require('./pong.class')
const hooks = require('./pong.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/test-connection/pong', new Pong(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('test-connection/pong')

  service.publish('pong', () => {
    return app.channel('admins')
  })

  service.hooks(hooks)
}
