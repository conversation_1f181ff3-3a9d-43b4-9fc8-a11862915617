/* eslint-disable no-unused-vars */
exports.Pong = class Pong {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
    this.events = ['pong']
  }

  async create (data, params) {
    const [conn] = await this.app.service('connection-histories').find({
      paginate: false,
      query: {
        $limit: 1,
        vehicle: data.vehicle,
        company: data.company
      }
    })
    const newConn = {}

    if (conn) {
      const prevFail = conn.failedReasons
      const channelIdx = prevFail.findIndex(v => (v.channel === data.channel))

      if (channelIdx === 0) {
        prevFail.shift()
      } else if (channelIdx === 1) {
        prevFail.pop()
      }

      newConn.status = !prevFail.length
      newConn.failedReasons = prevFail

      await this.app.service('connection-histories').patch(conn._id, newConn)
    }

    this.app.service('test-connection/pong').emit('pong', {
      ...newConn,
      ...data
    })

    return { status: 'ok' }
  }
}
