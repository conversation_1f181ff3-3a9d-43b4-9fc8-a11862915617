// Initializes the `trailer-config` service on path `/trailer-config`
const { TrailerConfig } = require('./trailer-config.class')
const createModel = require('../../models/trailer-config.model')
const hooks = require('./trailer-config.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch', '$populate'],
    multi: ['create']
  }

  // Initialize our service with any options it requires
  app.use('/trailer-config', new TrailerConfig(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-config')

  service.hooks(hooks)
}
