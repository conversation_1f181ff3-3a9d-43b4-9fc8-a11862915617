// Initializes the `bookingsPush` service on path `/sbCtr/bookingsPush`
const { SmartBookings } = require('./smartbookings.class')
const hooks = require('./smartbookings.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate'),
    multi: ['create']
  }

  // Initialize our service with any options it requires
  app.use('/sbCtr/:endpoint', new SmartBookings(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('sbCtr/:endpoint')

  service.hooks(hooks)
}
