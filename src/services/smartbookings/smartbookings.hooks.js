const { NotAuthenticated, NotFound } = require('@feathersjs/errors')
const { disallow } = require('feathers-hooks-common')
const logger = require('@hooks/logger')

const tokenAuthenticate = () => async ctx => {
  const sbApiKey = await ctx.app.redis.HGETALL(ctx.app.get('sb').apiKeyName)
  const [basePath] = ctx.path.split('/:')

  if (!ctx.params.headers[sbApiKey.httpHeader] ||
    (ctx.params.headers[sbApiKey.httpHeader] !== sbApiKey.apiKeyValue)) {
    throw new NotAuthenticated('API Key invalid')
  }

  logger.debug(`${basePath}/${ctx.params.route.endpoint}: authentication correct`)

  if (!ctx.params.route.endpoint || !['bookingsPush', 'bookingsPushCancelled'].includes(ctx.params.route.endpoint)) {
    throw new NotFound('Endpoint not found')
  }

  logger.debug(`${basePath}/${ctx.params.route.endpoint}: endpoint correct`)

  return ctx
}

module.exports = {
  before: {
    all: [],
    find: [disallow()],
    get: [disallow()],
    create: [tokenAuthenticate()],
    update: [disallow()],
    patch: [disallow()],
    remove: [disallow()]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
