const { BadRequest, Forbidden, GeneralError, NotAuthenticated, NotFound } = require('@feathersjs/errors')
const axios = require('axios')
const logger = require('@hooks/logger')

exports.SmartBookings = class SmartBookings {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async create (data, params) {
    if (!params.route.endpoint) {
      throw new BadRequest('Endpoint is required')
    }

    try {
      const { data: rest } = await axios.post(`${this.app.get('sb').baseUrl}/${params.route.endpoint}`, data)

      logger.debug(`SmartBookings (/${params.route.endpoint}) response: ${JSON.stringify(rest)}`)

      return rest
    } catch (error) {
      const status = error.response?.status || 500
      const message = error.response?.data?.message || error.message || 'Unknown error'

      logger.error(`Error from /${params.route.endpoint}: [${status}] ${message}`)

      switch (status) {
        case 400:
          throw new BadRequest(message)
        case 401:
          throw new NotAuthenticated(message)
        case 403:
          throw new Forbidden(message)
        case 404:
          throw new NotFound(message)
        default:
          throw new GeneralError(message)
      }
    }
  }
}
