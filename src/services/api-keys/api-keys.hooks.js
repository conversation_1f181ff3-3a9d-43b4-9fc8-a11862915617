const { authenticate } = require('@feathersjs/authentication').hooks
const { createHmac } = require('crypto')
const { clone } = require('lodash')
const ObjectId = require('mongoose').Types.ObjectId
const msg = require('@utils/commonMsg')
const { NotAcceptable } = require('@feathersjs/errors')

const generateKey = () => async ctx => {
  if (ctx.data && ctx.data.forRegistered && !ctx.data.accessToken) {
    await ctx.app.service('api-keys').checkApiKeys(ctx.data.company)
    const value = ctx.data.company + Date.now()
    const salt = ctx.app.get('authentication').secret
    const hash = createHmac('sha256', salt).update(value).digest('hex')
    ctx.data.accessToken = hash
  } else if (!ctx.data.accessToken) {
    let value = Date.now()
    value = value.toString()
    const salt = ctx.app.get('authentication').secret
    const hash = createHmac('sha256', salt).update(value).digest('hex')
    ctx.data.accessToken = hash
  }

  if (ctx.params.user) ctx.data.createdBy = ctx.params.user._id

  return ctx
}

const manipulateData = () => async ctx => {
  const filter = clone(ctx.params.query)

  const id = (ctx.id) ? { _id: ObjectId(ctx.id) } : {}

  // delete unnecessary queries
  delete filter.$limit
  delete filter.$skip
  delete filter.$sort

  // change to ObjectId
  if (filter['company.parent._id']) filter['company.parent._id'] = ObjectId(filter['company.parent._id'])
  if (filter['company._id']) filter['company._id'] = ObjectId(filter['company._id'])
  if (filter._id) {
    filter['company.parent._id'] = ObjectId(filter._id)
    delete filter._id
  }

  const aggregate = [
    {
      $lookup: {
        from: 'groups',
        localField: 'company',
        foreignField: '_id',
        as: 'company'
      }
    },
    { $unwind: { path: '$company', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'groups',
        localField: 'company.parent',
        foreignField: '_id',
        as: 'company.parent'
      }
    },
    { $unwind: { path: '$company.parent', preserveNullAndEmptyArrays: true } },
    {
      $match: {
        ...filter,
        ...id
      }
    },
    { $sort: { createAt: 1 } }
  ]

  const sbkeys = await ctx.app.service('api-keys').Model.aggregate(aggregate);

  (ctx.id) ? ctx.result = sbkeys[0] : ctx.result.data = sbkeys

  return ctx
}

const insertToRedis = () => async ctx => {
  const result = ctx.result
  const nameApiKey = result.apiKeyName || String(result.company)
  const httpHeader = result.headerKey || 'authorization'

  if (result.status === 'active') {
    await ctx.app.redis.HSET(nameApiKey, ['httpHeader', httpHeader, 'apiKeyValue', result.accessToken])
  } else {
    await ctx.app.redis.del(nameApiKey)
  }
}

const removeFromRedis = () => async ctx => {
  const result = ctx.result

  const nameApiKey = result.apiKeyName || result.company

  ctx.app.redis.del(nameApiKey)
}

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [async ctx => {
      if (ctx.data.apiKeyName) {
        const hasApiKey = await ctx.app.service('api-keys').find({ query: { apiKeyName: { $regex: ctx.data.apiKeyName, $options: 'i' } }, paginate: false })
        if (hasApiKey.length > 0) throw new NotAcceptable(msg.HAS_APIKEYNAME)
      }
    }, generateKey()],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [manipulateData()],
    get: [manipulateData()],
    create: [insertToRedis()],
    update: [],
    patch: [insertToRedis()],
    remove: [removeFromRedis()]
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
