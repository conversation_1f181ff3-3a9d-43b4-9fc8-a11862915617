const { Service } = require('feathers-mongoose')

exports.ApiKeys = class ApiKeys extends Service {
  async checkApiKeys (id) {
    const res = await super.find({
      paginate: false,
      query: {
        company: id,
        $populate: 'company'
      }
    })

    if (res.length) throw new Error(`${res[0].company.level === 1 ? res[0].company.name : res[0].company.level === 2 ? res[0].company.name : res[0].company.name} Api Key already created`)
  }
}
