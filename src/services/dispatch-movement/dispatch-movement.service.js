// Initializes the `dispatchMovement` service on path `/dispatch-movement`
const { DispatchMovement } = require('./dispatch-movement.class')
const hooks = require('./dispatch-movement.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/dispatchMovement', new DispatchMovement(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('dispatchMovement')

  service.hooks(hooks)
}
