const logRequest = require('@hooks/log-external-request')
const apiKeyAuth = require('@hooks/api-key-authentication')
const { disallow } = require('feathers-hooks-common')
const { BadRequest } = require('@feathersjs/errors')
const OptETruckResult = require('../../beans/optETruckResult')
const logger = require('@hooks/logger')

const validator = () => async ctx => {
  const { data } = ctx
  const validation = []
  const validationRegistered = []

  logger.info('Incoming OET /dispatchMovement', data)
  if (!data.movementId) validation.push('movementId')
  if (!data.vehicleRegN) validation.push('vehicleRegN')
  if (!data.driverM) validation.push('driverM')
  if (!data.fromLocationM) validation.push('fromLocationM')
  if (!data.toLocationM) validation.push('toLocationM')
  if (!data.timestamp) validation.push('timestamp')
  if (!data.haulierOrgCode) validation.push('haulierOrgCode')
  if (!data.workOrder) validation.push('workOrder')
  if (!data.vesselM) validation.push('vesselM')
  if (!data.voyageN) validation.push('voyageN')
  if (!data.isoC) validation.push('isoC')
  if (!data.containerRemark) validation.push('containerRemark')
  if (!data.fromAddress) validation.push('fromAddress')
  if (!data.toAddress) validation.push('toAddress')
  if (!data.jobType) validation.push('jobType')
  if (!data.laden) validation.push('laden')

  if (data.vehicleRegN) {
    const findVehicle = await ctx.app.service('vehicles').find({
      paginate: false,
      query: {
        $populate: ['company', 'driver'],
        status: true,
        vehicleNo: data.vehicleRegN
      }
    })

    if (!findVehicle.length) validationRegistered.push('vehicleRegN')

    if (findVehicle.length) {
      const [findUen] = await ctx.app.service('cdi-participants').find({
        paginate: false,
        query: {
          uen: findVehicle[0].company.groupInfo.uen
        }
      })

      if (data.haulierOrgCode !== findUen.code) validationRegistered.push('haulierOrgCode')
    }
  }

  if (validation.length) throw new BadRequest(validation.join(', ') + ' is required')
  if (validationRegistered.length) throw new BadRequest(validationRegistered.join(', ') + ' is not registered')
  if (data.totalCntrQty && isNaN(data.totalCntrQty)) throw new BadRequest('totalCntrQty must be filled by number')
  if (data.tareWeight && isNaN(data.tareWeight)) throw new BadRequest('tareWeight must be filled by number')
  if (data.containerRemark && data.containerRemark.length > 210) throw new BadRequest('containerRemark is too long')
  if (['import', 'export', 'local'].indexOf(data.jobType.toLowerCase()) === -1) throw new BadRequest('jobType must fill by Import/Export/Local')
  if (['full', 'empty'].indexOf(data.laden.toLowerCase()) === -1) throw new BadRequest('laden must fill by Full/Empty')

  return ctx
}

module.exports = {
  before: {
    all: [apiKeyAuth()],
    find: [disallow()],
    get: [disallow()],
    create: [logRequest(), validator(),
      async ctx => {
        try {
          const { total } = await ctx.app.service('jobs').find({
            query: {
              movementId: ctx.data.movementId
            }
          })

          if (total) throw new Error(`Movement ${ctx.data.movementId} already exist`)

          const selectedHaulier = await ctx.app.service('cdi-participants').find({
            paginate: false,
            query: {
              code: ctx.data.haulierOrgCode
            }
          })

          let company = []

          if (selectedHaulier.length > 0) {
            company = await ctx.app.service('groups').find({
              paginate: false,
              query: {
                'groupInfo.uen': selectedHaulier[0].uen
              }
            })
          }

          const data = {
            truckNumber: ctx.data.vehicleRegN,
            trailerNumber: ctx.data.trailerRegnN,
            containerNumber: ctx.data.cntrN,
            sealNumber: ctx.data.sealN,
            jobStatus: 'new',
            tripId: ctx.data.tripId,
            movementId: ctx.data.movementId,
            pickupLocation: ctx.data.fromLocationM,
            deliveryLocation: ctx.data.toLocationM,
            haulierC: ctx.data.haulierOrgCode,
            driverName: ctx.data.driverM,
            source: 'OET',
            workOrder: ctx.data.workOrder,
            weight: ctx.data.tareWeight,
            ucrN: ctx.data.bookingNo,
            vesselM: ctx.data.vesselM,
            voyageN: ctx.data.voyageN,
            typeSize: ctx.data.isoC,
            totalCntrQty: ctx.data.totalCntrQty,
            containerRemark: ctx.data.containerRemark,
            pickupAddress: ctx.data.fromAddress,
            deliveryAddress: ctx.data.toAddress,
            jobType: ctx.data.jobType,
            laden: ctx.data.laden,
            multiMountTripId: ctx.data.multiMountTripId,
            bookingN: ctx.data.bookingID
          }

          if (company.length > 0) data.company = company[0]._id

          await ctx.app.service('jobs').create(data)
        } catch (err) {
          throw new Error(err.message)
        }
      }
    ],
    update: [disallow()],
    patch: [disallow()],
    remove: [disallow()]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [async ctx => {
      ctx.statusCode = 200

      return ctx
    }],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [async ctx => {
      logger.error(`Something wrong with OET /dispatchMovement ${ctx.error.message}`)
      ctx.result = new OptETruckResult('Fail', ctx.error.message)
      ctx.statusCode = ctx.error.code

      return ctx
    }],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
