// Initializes the `cancelDispatchedMovement` service on path `/cancel-dispatched-movement`
const { CancelDispatchedMovement } = require('./cancel-dispatched-movement.class')
const hooks = require('./cancel-dispatched-movement.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/cancelDispatchedMovement', new CancelDispatchedMovement(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('cancelDispatchedMovement')

  service.hooks(hooks)
}
