const logRequest = require('@hooks/log-external-request')
const apiKeyAuth = require('@hooks/api-key-authentication')
const { disallow } = require('feathers-hooks-common')
const { BadRequest } = require('@feathersjs/errors')
const OptETruckResult = require('../../beans/optETruckResult')
const logger = require('@hooks/logger')

const validator = () => ctx => {
  const { data } = ctx
  const validation = []

  logger.info('Incoming OET /updateDestination', data)
  if (!data.movementId) validation.push('movementId')
  if (!data.fromLocationM) validation.push('fromLocationM')
  if (!data.toLocationM) validation.push('toLocationM')
  if (!data.timestamp) validation.push('timestamp')
  if (!data.haulierOrgCode) validation.push('haulierOrgCode')
  if (!data.workOrder) validation.push('workOrder')
  if (!data.vesselM) validation.push('vesselM')
  if (!data.voyageN) validation.push('voyageN')
  if (!data.isoC) validation.push('isoC')
  if (!data.fromAddress) validation.push('fromAddress')
  if (!data.toAddress) validation.push('toAddress')

  if (validation.length) throw new BadRequest(validation.join(', ') + ' is required')
  if (data.tareWeight && isNaN(data.tareWeight)) throw new BadRequest('tareWeight must be filled by number')
  if (data.totalCntrQty && isNaN(data.totalCntrQty)) throw new BadRequest('totalCntrQty must be filled by number')

  return ctx
}

module.exports = {
  before: {
    all: [apiKeyAuth()],
    find: [disallow()],
    get: [disallow()],
    create: [logRequest(), validator(),
      async ctx => {
        try {
          const job = await ctx.app.service('jobs').find({
            query: {
              movementId: ctx.data.movementId
            },
            paginate: false
          })

          const data = {
            pickupLocation: ctx.data.fromLocationM,
            deliveryLocation: ctx.data.toLocationM,
            haulierC: ctx.data.haulierOrgCode,
            workOrder: ctx.data.workOrder,
            vesselM: ctx.data.vesselM,
            voyageN: ctx.data.voyageN,
            typeSize: ctx.data.isoC,
            pickupAddress: ctx.data.fromAddress,
            deliveryAddress: ctx.data.toAddress
          }

          if (ctx.data.vehicleRegN) data.truckNumber = ctx.data.vehicleRegN
          if (ctx.data.driverM) data.driverName = ctx.data.driverM
          if (ctx.data.trailerRegnN) data.trailerNumber = ctx.data.trailerRegnN
          if (ctx.data.cntrN) data.containerNumber = ctx.data.cntrN
          if (ctx.data.sealN) data.sealNumber = ctx.data.sealN
          if (ctx.data.tareWeight) data.weight = ctx.data.tareWeight
          if (ctx.data.bookingNo) data.ucrN = ctx.data.bookingNo
          if (ctx.data.containerRemark) data.containerRemark = ctx.data.containerRemark
          if (ctx.data.multiMountTripId) data.multiMountTripId = ctx.data.multiMountTripId
          if (ctx.data.totalCntrQty) data.totalCntrQty = ctx.data.totalCntrQty

          if (ctx.data.tripId && job[0].jobStatus === 'new') {
            data.tripId = ctx.data.tripId
          }

          await ctx.app.service('jobs').patch(job[0]._id, data)
        } catch (e) {
          throw new Error('Movement doesn’t exist.')
        }
      }
    ],
    update: [disallow()],
    patch: [disallow()],
    remove: [disallow()]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [async ctx => {
      ctx.statusCode = 200

      return ctx
    }],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [async ctx => {
      logger.error(`Something wrong with OET /dispatchMovement ${ctx.error.message}`)
      ctx.result = new OptETruckResult('Fail', ctx.error.message)
      ctx.statusCode = ctx.error.code

      return ctx
    }],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
