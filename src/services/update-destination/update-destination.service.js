// Initializes the `updateDestination` service on path `/update-destination`
const { UpdateDestination } = require('./update-destination.class')
const hooks = require('./update-destination.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/updateDestination', new UpdateDestination(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('updateDestination')

  service.hooks(hooks)
}
