const { SMS, Push } = require('./sender.class')
const hooks = require('./sender.hooks')
const mailer = require('feathers-mailer')
const smtpTransport = require('nodemailer-smtp-transport')

module.exports = async function (app) {
  // Register email Service & Hook
  app.use('/mailer', mailer(smtpTransport(app.get('smtp'))))
  app.service('mailer').hooks(hooks)

  // Register SMS Service & Hook
  app.use('/sms', new SMS({}, app))
  app.service('sms').hooks(hooks)

  // Register Push Notification Service & Hook
  app.use('/pushNotif', new Push({}, app))
  app.service('pushNotif').hooks(hooks)
}
