const { isArray } = require('lodash')
const axios = require('axios')
const logger = require('@hooks/logger')

exports.SMS = class SMS {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async create (data) {
    if (process.env.NODE_ENV === 'uat') {
      try {
        await axios.post('http://localhost:5001/', {
          mobile: data.mobile,
          otp: data.message
        })
      } catch (error) {
        throw new Error(error.response?.statusText || error.message)
      }
    } else if (process.env.NODE_ENV === 'production') {
      const config = this.app.get('sms')

      if (config) {
        logger.info('Start sending SMS', {
          to: data.mobile,
          from: config.from,
          message: data.message.replace(/\b\d{6}\b/g, 'xxxxxx')
        })

        const response = await axios.get(config.host, {
          params: {
            username: config.account.username,
            password: config.account.password,
            to: data.mobile,
            from: config.from,
            message: data.message
          }
        })

        logger.debug('SMSGateway Response', response.data)
      } else {
        logger.error('SMS config not found')
      }
    }

    return logger.info(data.message)
  }
}

exports.Push = class Push {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async create (data, params) {
    try {
      if (!isArray(data)) {
        data.android = {
          priority: 'high',
          notification: {
            clickAction: ''
          }
        }
        data.apns = {
          headers: {
            'apns-priority': '10',
            'content-available': '1'
          },
          payload: {
            aps: {
              badge: 1,
              sound: 'default'
            }
          }
        }

        // redis for checker
        let sendMessage = true

        if (data.message) {
          const currentMessageId = await this.app.redis.HGET('latestmessage', String(data.message.to))

          if (currentMessageId) {
            if (String(currentMessageId) !== String(data.message.id)) {
              await this.app.redis.HSET('latestmessage', `${String(data.message.to)}`, `${String(data.message.id)}`)
            } else {
              sendMessage = false
            }
          } else {
            await this.app.redis.HSET('latestmessage', `${String(data.message.to)}`, `${String(data.message.id)}`)
          }
        }

        if (sendMessage) {
          if (data.message) delete data.message

          const response = await this.app.firebase.messaging().send(data)

          logger.info(`Success sending fcm with detail ${JSON.stringify(response)}`)
        }
      }
    } catch (error) {
      logger.error('Oops! something wrong ', error)
    }

    return data
  }
}
