// Initializes the `connectionHistories` service on path `/connection-histories`
const { ConnectionHistories } = require('./connection-histories.class')
const createModel = require('../../models/connection-histories.model')
const hooks = require('./connection-histories.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/connection-histories', new ConnectionHistories(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('connection-histories')

  service.hooks(hooks)
}
