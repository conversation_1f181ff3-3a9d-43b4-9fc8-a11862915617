// Initializes the `cancel-dispatched-movement-tms` service on path `/cancelDispatchedMovemntTms`
const { CancelDispatchedMovementTms } = require('./cancel-dispatched-movement-tms.class')
const hooks = require('./cancel-dispatched-movement-tms.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/cancelDispatchedMovementTms', new CancelDispatchedMovementTms(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('cancelDispatchedMovementTms')

  service.hooks(hooks)
}
