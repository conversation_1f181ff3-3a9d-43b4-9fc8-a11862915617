const logRequest = require('../../hooks/log-external-request')
const apiKeyAuth = require('../../hooks/api-key-authentication')
const { disallow } = require('feathers-hooks-common')
const { BadRequest } = require('@feathersjs/errors')
const OptETruckResult = require('../../beans/optETruckResult')
const logger = require('../../hooks/logger')

const validator = () => ctx => {
  const { data } = ctx
  const validation = []

  logger.info('Incoming OET /cancelDispatchMovementTms', data)
  if (!data.movementId) validation.push('movementId')
  if (!data.timestamp) validation.push('timestamp')
  if (!data.thirdParty) validation.push('thirdParty')

  if (validation.length) throw new BadRequest(validation.join(', ') + ' is required')

  return ctx
}

module.exports = {
  before: {
    all: [apiKeyAuth()],
    find: [disallow()],
    get: [disallow()],
    create: [logRequest(), validator(),
      async ctx => {
        try {
          const thirdPartyExists = await ctx.app.service('api-keys').find({
            query: {
              apiKeyName: ctx.data.thirdParty,
              status: 'active'
            },
            paginate: false
          })

          if (!thirdPartyExists.length) {
            throw new Error(`Third Party ${ctx.data.thirdParty} does not exist`)
          }

          const job = await ctx.app.service('jobs').find({
            query: {
              movementId: ctx.data.movementId,
              jobStatus: { $in: ['new', 'in progress', 'start pickup', 'completed pickup', 'start delivery', 'completed delivery'] },
              source: 'OET',
              area_id: { $exists: false }
            },
            paginate: false
          })

          if (job.length === 0) {
            throw new Error('Movement doesn’t exist.')
          }

          await ctx.app.service('job-deleted').create(job[0])

          await ctx.app.service('jobs').remove(job[0]._id)
        } catch (err) {
          logger.error('Something wrong when /cancelDispatchMovementTms OET job ', err)
          throw new Error(err)
        }
      }
    ],
    update: [disallow()],
    patch: [disallow()],
    remove: [disallow()]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [async ctx => {
      ctx.statusCode = 200

      return ctx
    }],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [async ctx => {
      logger.error(`Something wrong with OET /cancelDispatchMovementTms ${ctx.error.message}`)
      ctx.result = new OptETruckResult('Fail', ctx.error.message)
      ctx.statusCode = ctx.error.code

      return ctx
    }],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
