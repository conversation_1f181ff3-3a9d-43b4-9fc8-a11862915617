const logger = require('@hooks/logger')
const ExcelJS = require('exceljs')
const fs = require('fs')
const ejs = require('ejs')
const { promisify } = require('util')
const renderFile = promisify(ejs.renderFile)
const ObjectId = require('mongoose').Types.ObjectId
/* eslint-disable no-unused-vars */
exports.ImportTrailerMaster = class ImportTrailerMaster {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async get (id, params) {
    return {
      id, text: `A new message with ID: ${id}!`
    }
  }

  async create (data, params) {
    return data
  }

  async generateTemplate (groupId, preValue = []) {
    try {
      // get the datas and prepare data
      const group = await this.app.service('groups').get(groupId)

      // prepare excel
      const filename = `${group.name.split(' ').join('-')} - Trailer-Failed.xlsx`
      const workbook = new ExcelJS.Workbook()
      workbook.creator = 'Rejing'
      workbook.created = new Date()
      const ws = workbook.addWorksheet('Trailer Master')
      let row

      // Style sheet
      const formatAlignCenter = { vertical: 'middle', horizontal: 'center', wrapText: true }
      const formatTextBold = { size: 12, bold: true }
      ws.getColumn(1).width = 5
      ws.getColumn(1).width = 5
      ws.getColumn(2).width = 20
      ws.getColumn(3).width = 30
      ws.getColumn(4).width = 30
      ws.getColumn(5).width = 15
      ws.getColumn(6).width = 10
      ws.getColumn(7).width = 25
      ws.getColumn(8).width = 20
      ws.getColumn(9).width = 20
      ws.getColumn(10).width = 20
      ws.getColumn(11).width = 20
      ws.getColumn(12).width = 20
      ws.getColumn(13).width = 20
      ws.getColumn(14).width = 10
      ws.getColumn(15).width = 10
      ws.getColumn(16).width = 25
      ws.getColumn(17).width = 25
      ws.getColumn(18).width = 25
      ws.getColumn(19).width = 25
      ws.getColumn(20).width = 25
      ws.getColumn(21).width = 25
      ws.getColumn(22).width = 10
      ws.getColumn(23).width = 25
      ws.getColumn(24).width = 25
      ws.getColumn(25).width = 25
      ws.getColumn(26).width = 25
      ws.getColumn(27).width = 25
      ws.getColumn(28).width = 15

      // START SET HEADER
      row = ws.addRow([
        'S/N',
        'Trailer Number*',
        'Company*',
        'Leasee',
        'Licence Status*',
        'On Lease*',
        'Trailer Description',
        'Trailer Make',
        'Trailer Model',
        'Trailer Status*',
        'Trailer Size*',
        'Trailer Type*',
        'Chassis Number',
        'mlwKg*',
        'ulwKg*',
        'Manufacture Date',
        'Registration Date*',
        'Road Tax Expiry Date*',
        'Insurance Expiry Date*',
        'Next Inpection Date*',
        'Registration Expiry Date',
        'Color*',
        'Remarks',
        'Is Pairing Required*',
        'Truck Number*',
        'Pairing Date*',
        'Paired by',
        'Billable*'
      ])

      const curRow = row._number
      ws.getCell(`B${curRow}`).value = {
        richText: [
          { font: { size: 12, bold: true }, text: 'Trailer Number*' }
        ]
      }
      // _______ sixe auto
      let indxRow = 'A'
      ws.columns.forEach((column, index) => {
        const wert = ws.getCell(`${indxRow}${curRow}`)

        if (typeof wert.value === 'object') {
          if (wert.value && typeof wert.value.richText !== 'undefined') {
            let sizeWidth = 12
            wert.value.richText.forEach((x, index) => {
              if (typeof x.text !== 'undefined' && x.text) {
                if (sizeWidth < x.text.length) {
                  sizeWidth = x.text.length
                }
              }
            })
            if (column.width < sizeWidth) {
              column.width = sizeWidth
            }
          }
        } else if (typeof wert.value === 'string') {
          if (column.width < wert.value.length) {
            column.width = wert.value.length
          }
        }

        if (indxRow !== 'AA' && indxRow !== 'AB') indxRow = String.fromCharCode(indxRow.charCodeAt(0) + 1)
        if (indxRow === '[') indxRow = 'AA'
        if (indxRow === 'AA') indxRow = 'AB'
      })
      // _______ ./sixe auto
      row.getCell(1).alignment = formatAlignCenter
      row.getCell(2).alignment = formatAlignCenter
      row.getCell(3).alignment = formatAlignCenter
      row.getCell(4).alignment = formatAlignCenter
      row.getCell(5).alignment = formatAlignCenter
      row.getCell(6).alignment = formatAlignCenter
      row.getCell(7).alignment = formatAlignCenter
      row.getCell(8).alignment = formatAlignCenter
      row.getCell(9).alignment = formatAlignCenter
      row.getCell(10).alignment = formatAlignCenter
      row.getCell(11).alignment = formatAlignCenter
      row.getCell(12).alignment = formatAlignCenter
      row.getCell(13).alignment = formatAlignCenter
      row.getCell(14).alignment = formatAlignCenter
      row.getCell(15).alignment = formatAlignCenter
      row.getCell(16).alignment = formatAlignCenter
      row.getCell(17).alignment = formatAlignCenter
      row.getCell(18).alignment = formatAlignCenter
      row.getCell(19).alignment = formatAlignCenter
      row.getCell(20).alignment = formatAlignCenter
      row.getCell(21).alignment = formatAlignCenter
      row.getCell(22).alignment = formatAlignCenter
      row.getCell(23).alignment = formatAlignCenter
      row.getCell(24).alignment = formatAlignCenter
      row.getCell(25).alignment = formatAlignCenter
      row.getCell(26).alignment = formatAlignCenter
      row.getCell(27).alignment = formatAlignCenter
      row.getCell(28).alignment = formatAlignCenter
      row.getCell(29).alignment = formatAlignCenter
      row.getCell(32).alignment = formatAlignCenter

      for (let i = 1; i <= 29; i++) {
        row.getCell(i).font = formatTextBold
      }
      // END SET HEADER

      // set value FAILED import
      preValue.forEach(el => {
        row = ws.addRow(el)
        row.getCell('B').numFmt = '@'
        row.getCell('C').numFmt = '@'
        row.getCell('D').numFmt = '@'
        row.getCell('F').numFmt = '@'
        row.getCell('G').numFmt = '@'
        row.getCell('H').numFmt = '@'
        row.getCell('I').numFmt = '@'
        row.getCell('J').numFmt = '@'
        row.getCell('K').numFmt = '@'
        row.getCell('L').numFmt = '@'
        row.getCell('M').numFmt = '@'
        row.getCell('N').numFmt = '@'
        row.getCell('O').numFmt = '@'
        row.getCell('P').numFmt = '@'
        row.getCell('Q').numFmt = '@'
        row.getCell('R').numFmt = '@'
        row.getCell('S').numFmt = '@'
        row.getCell('T').numFmt = '@'
        row.getCell('U').numFmt = '@'
        row.getCell('V').numFmt = '@'
        row.getCell('W').numFmt = '@'
        row.getCell('X').numFmt = '@'
        row.getCell('Y').numFmt = '@'
        row.getCell('Z').numFmt = '@'
        row.getCell('AA').numFmt = '@'
        row.getCell('AB').numFmt = '@'
      })

      // repeat logic Body
      for (let i = 0; i < 500; i++) {
        row = ws.addRow([])
        row.getCell('B').numFmt = '@'
        row.getCell('C').numFmt = '@'
        row.getCell('D').numFmt = '@'
        row.getCell('F').numFmt = '@'
        row.getCell('G').numFmt = '@'
        row.getCell('H').numFmt = '@'
        row.getCell('I').numFmt = '@'
        row.getCell('J').numFmt = '@'
        row.getCell('K').numFmt = '@'
        row.getCell('L').numFmt = '@'
        row.getCell('M').numFmt = '@'
        row.getCell('N').numFmt = '@'
        row.getCell('O').numFmt = '@'
        row.getCell('P').numFmt = '@'
        row.getCell('Q').numFmt = '@'
        row.getCell('R').numFmt = '@'
        row.getCell('S').numFmt = '@'
        row.getCell('T').numFmt = '@'
        row.getCell('U').numFmt = '@'
        row.getCell('V').numFmt = '@'
        row.getCell('W').numFmt = '@'
        row.getCell('X').numFmt = '@'
        row.getCell('Y').numFmt = '@'
        row.getCell('Z').numFmt = '@'
        row.getCell('AA').numFmt = '@'
        row.getCell('AB').numFmt = '@'
      }
      // End set body

      // // write file non upload aws s3
      await workbook.xlsx.writeFile(`${this.app.get('public')}/${filename}`)

      // // write file upload to aws s3
      // const buffer = await workbook.xlsx.writeBuffer();
      // const readable = new Readable();
      // readable._read = () => {}; // _read is required but you can noop it
      // readable.push(buffer);
      // readable.push(null);

      // prosess upload to public
      // const directory = `template/${filename}`;

      return filename
    } catch (e) {
      // statements
      logger.error('Oops! something wrong', e)
    }
  }

  async readTemplate (filename, user) {
    try {
      // prepare before import data trailer
      const collectedTrailerMaster = []; const collectedTrailerNumber = []; const companiesMap = {}; const truckNumbersMap = {}

      const workbook = new ExcelJS.Workbook()
      // get data from non s3
      await workbook.xlsx.readFile(`${this.app.get('import')}/trailer-master/${filename}`)

      const ws = workbook.getWorksheet(1)

      const companies = await this.app.service('groups').find({ paginate: false, query: { level: 2 } })
      companies.forEach(val => {
        companiesMap[val._id] = val
      })

      const truckNumber = await this.app.service('vehicles').find({ paginate: false })
      truckNumber.forEach(val => {
        truckNumbersMap[val._id] = val
      })

      ws.eachRow(async (row, rowNumber) => {
        if (rowNumber !== 1) {
          const { values } = row
          collectedTrailerMaster.push(values)
          collectedTrailerNumber.push(values[2])
        }
      })

      const importResult = await this.startImport({
        rawData: collectedTrailerMaster,
        collectedTrailerNumber,
        companiesMap,
        truckNumbersMap,
        user
      })

      const group = await this.app.service('groups').get(importResult.groupId, { paginate: false })
      let failedFilename
      if (importResult.invalidTrailerMaster.length > 0) failedFilename = await this.generateTemplate(importResult.groupId, importResult.invalidTrailerMaster)

      return {
        group: group.name,
        valid: importResult.validTrailerMaster.length,
        invalid: importResult.invalidTrailerMaster.length,
        filename: failedFilename || null,
        successfullCreated: importResult.validTrailerMaster
      }
    } catch (e) {
      // statements
      logger.error('Oops! something wrong', e)
    }
  }

  async startImport ({ rawData, collectedTrailerNumber, companiesMap, truckNumbersMap, user }) {
    try {
      const existTrailerNumber = (await this.app.service('trailer-master').find({
        paginate: false,
        fromImport: true,
        fromServer: true,
        query: {
          $limit: collectedTrailerNumber.length,
          trailerNumber: {
            $in: collectedTrailerNumber
          }
        }
      })).map(u => u.trailerNumber)

      let validTrailerMaster = []; const invalidTrailerMaster = []
      let groupId

      rawData.forEach((values) => {
        let validation = []
        // start validate data
        // check null data
        if (!values[2]) validation.push('- Trailer Number is required')
        if (!values[3]) validation.push('- Company is required')
        if (values[5] === null || typeof values[5] === 'undefined') validation.push('- Licence Status is required')
        if (values[6] === null || typeof values[6] === 'undefined') validation.push('- on lease is required')
        if (!values[10]) validation.push('- Trailer Status is required')
        if (!values[11]) validation.push('- Trailer Size is required')
        if (!values[12]) validation.push('- Trailer Type is required')
        if (!values[14]) validation.push('- mlwKg is required')
        if (!values[15]) validation.push('- ulwKg is required')
        if (!values[17]) validation.push('- Registration Date is required')
        if (!values[18]) validation.push('- Road Tax Expiry Date is required')
        if (!values[19]) validation.push('- Insurance Expiry Date is required')
        if (!values[20]) validation.push('- Next Inpection Date is required')
        if (!values[22]) validation.push('- Color is required')
        if (values[24] === null || typeof values[24] === 'undefined') validation.push('- Is Pairing is required')
        // if (!values[25]) validation.push('- Truck Number is required')
        if (!values[26]) validation.push('- Pairing Date is required')
        if (values[28] === null || typeof values[28] === 'undefined') validation.push('- Bill able is required')
        if (values[13] && values[13].length >= 30) validation.push('- Chassis Number cannot be more than 30 character')

        if (values[3] && ObjectId.isValid(values[3]) && companiesMap[values[3]].level !== 2) validation.push('must using company level')
        // check active data
        if (values[3] && ObjectId.isValid(values[3]) && companiesMap[values[3]].status !== 'active') validation.push('Company is inactive')
        if (values[4] && ObjectId.isValid(values[4]) && companiesMap[values[4]].status !== 'active') validation.push('leasee is inactive')
        if (values[25] && ObjectId.isValid(values[25]) && !truckNumbersMap[values[25]].status) validation.push('truck Number is inactive')
        // check exsis data
        if (values[2] && existTrailerNumber.includes(values[2].toString())) validation.push('- Trailer Number already exists')

        // check objectId
        if (values[3] && !ObjectId.isValid(values[3])) validation.push('- Company ObjectId is invalid')
        if (values[4] && !ObjectId.isValid(values[4])) validation.push('- Leasee ObjectId is invalid')
        if (values[25] && !ObjectId.isValid(values[25])) validation.push('- Truck Number ObjectId is invalid')
        if (values[27] && !ObjectId.isValid(values[27])) validation.push('- Paired By ObjectId is invalid')

        // check data boolean
        if (values[5] && typeof values[5] !== 'boolean') validation.push('- Licence Status must using boolean (TRUE/FALSE)')
        if (values[6] && typeof values[6] !== 'boolean') validation.push('- on lease must using boolean (TRUE/FALSE)')
        if (values[24] && typeof values[24] !== 'boolean') validation.push('- Is Pairing must using boolean (TRUE/FALSE)')
        if (values[28] && typeof values[28] !== 'boolean') validation.push('- Bill able must using boolean (TRUE/FALSE)')
        // end validate data

        if (companiesMap[values[3]]) groupId = companiesMap[values[3]].parent

        // push to exists data variable
        if (values[2]) existTrailerNumber.push(values[2])
        // end push to exists data variable

        if (validation.length > 0) {
          if (!values[11]) values[11] = null
          validation = validation.join('\n')
          values.push(validation)
          invalidTrailerMaster.push(values)
        } else {
          const trailerMaster = {
            trailerNumber: values[2].toString(),
            company: values[3],
            leasee: values[4] !== 'null' || values[4] !== null ? values[4] : null,
            licenceStatus: values[5],
            onLease: values[6],
            trailerDescription: values[7] ? values[7] : '',
            trailerMake: values[8] ? values[8] : '',
            trailerModel: values[9] ? values[9] : '',
            trailerStatus: values[10],
            trailerSize: values[11],
            trailerType: values[12],
            chassisNumber: values[13] ? values[13] : '',
            mlwKg: values[14],
            ulwKg: values[15],
            manufactureDate: values[16] ? values[16] : null,
            registrationDate: values[17],
            roadTaxExpiryDate: values[18],
            insuranceExpiryDate: values[19],
            nextInspectionDate: values[20],
            registrationExpiryDate: values[21] ? values[21] : null,
            color: values[22],
            remarks: values[23] !== 'null' || values[23] !== null ? values[23] : '',
            isPairingRequired: values[24],
            truckNumber: values[25],
            pairingDate: values[26],
            pairedBy: values[27] !== 'null' || values[27] !== null ? values[27] : null,
            billable: values[28]
          }
          validTrailerMaster.push(trailerMaster)
        }
      })

      if (validTrailerMaster.length > 0) {
        try {
          const data = await this.app.service('trailer-master').create(validTrailerMaster, { user })
          validTrailerMaster = data.successfullCreated.map(v => {
            v = {
              _id: v._id,
              trailerNumber: v.trailerNumber
            }

            return v
          })
        } catch (e) {
          logger.error('Oops! something wrong', e)
        }
      }

      return {
        validTrailerMaster,
        invalidTrailerMaster,
        groupId
      }
    } catch (e) {
      logger.error('Oops! something wrong', e)
    }
  }

  async prepareImport (filenames, user, _res) {
    try {
      let allImport = await this.readTemplate(filenames, user)
      const validData = allImport.successfullCreated

      delete allImport.successfullCreated

      logger.debug(`allImport: ${JSON.stringify(allImport)}`)

      const attachments = []
      if (allImport.filename) {
        const stream = this.createReadStreamSync(`${this.app.get('public')}/${allImport.filename}`)
        attachments.push({ filename: allImport.filename, content: stream })
      }

      allImport = [allImport]

      // send email
      renderFile(`${this.app.get('template').email}/upload-trailer-master-success.ejs`, {
        subject: 'Import Trailer Master Result',
        name: user.fullname,
        result: allImport
      }).then((html) => {
        const payloadEmail = {
          from: this.app.get('smtp').sender,
          to: user.email,
          subject: 'Import Trailer Master Result',
          html
        }
        if (attachments.length > 0 && attachments[0].filename) payloadEmail.attachments = attachments

        this.app
          .service('mailer')
          .create(payloadEmail)

        if (filenames) {
          fs.unlinkSync(`${this.app.get('import')}/trailer-master/${filenames}`, function (err) {
            if (err) throw err
            // if no error, file has been deleted successfully
            logger.debug('File import deleted!')
          })
        }

        if (attachments.length > 0 && attachments[0] && attachments[0].filename) {
          fs.unlinkSync(`${this.app.get('public')}/${attachments[0].filename}`, function (err) {
            if (err) throw err
            // if no error, file has been deleted successfully
            logger.debug('File template deleted!')
          })
        }
      }).catch(err => logger.error('Oops! something wrong', err))

      const response = {
        status: validData.length > 0 ? 'success' : 'unsuccessful'
      }

      if (validData.length > 0) {
        response.message = 'Please check your email if any data fails'
        response.data = validData
      } else response.message = 'All data failed, please check your email'
      _res.json(response)
    } catch (error) {
      logger.error('Oops! something wrong', error)
    }
  }

  createReadStreamSync (file, options) {
    if (!options) { options = {} }
    if (!options.flags) { options.flags = 'r' }
    if (!options.fd) { options.fd = fs.openSync(file, options.flags) }
    return fs.createReadStream(file, options)
  }
}
