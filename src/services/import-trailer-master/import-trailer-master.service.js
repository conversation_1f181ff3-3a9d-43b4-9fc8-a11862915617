// Initializes the `import-trailer-master` service on path `/import-trailer-master`
const { ImportTrailerMaster } = require('./import-trailer-master.class')
const hooks = require('./import-trailer-master.hooks')
const multer = require('multer')
const multerSharp = require('@multigunagemilang/multer-sharp-addon')

module.exports = function (app) {
  const confStorage = {
    destination: (_req, _file, cb) => cb(null, `${app.get('import')}/trailer-master/`), // where the files are being stored
    filename: (_req, file, cb) => cb(null, `${Date.now()}-${file.originalname}`) // getting the file name
  }

  const storage = multerSharp(confStorage)
  const upload = multer({ storage })

  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/import-trailer-master',
    new ImportTrailerMaster(options, app),
    upload.array('files'), (req, _res, next) => {
      req.feathers.files = req.files

      if (req.files.length > 0) {
        app.service('import-trailer-master').prepareImport(req.files[0].filename, req.res.hook.params.user, _res)
      } else {
        _res.json({
          status: 'error',
          message: 'Please add file to upload the data.'
        })
      }
    }
  )

  // Get our initialized service so that we can register hooks
  const service = app.service('import-trailer-master')

  service.hooks(hooks)
}
