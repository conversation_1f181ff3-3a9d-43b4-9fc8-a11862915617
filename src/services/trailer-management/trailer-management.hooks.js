const { authenticate } = require('@feathersjs/authentication').hooks
const { NotAcceptable, NotFound } = require('@feathersjs/errors')
const logger = require('@hooks/logger')
const msg = require('@utils/commonMsg')
const { formatDateSG } = require('@utils/format')
const { iff } = require('feathers-hooks-common')
const ObjectId = require('mongoose').Types.ObjectId
const { getConfig } = require('@hooks/data-update')
const { disallow } = require('feathers-hooks-common')
const moment = require('moment')

const queryData = () => async ctx => {
  logger.debug('add query trailer management')
  const $match = {}
  const limit = ctx.params.query.$limit ? Number(ctx.params.query.$limit) : ctx.app.get('paginate').default
  const skip = Number(ctx.params.query.$skip) ? Number(ctx.params.query.$skip) : 0
  let sort = {}
  if (ctx.params.query && ctx.params.query.$sort) {
    Object.keys(ctx.params.query.$sort).forEach(v => {
      sort = { [v]: Number(ctx.params.query.$sort[v]) }
    })
  } else sort = { updatedAt: -1 }
  let query = [
    {
      $facet: {
        data: [
          {
            $sort: sort
          },
          {
            $limit: limit + skip
          },
          {
            $skip: skip
          }
        ],
        total: [
          {
            $count: 'count'
          }
        ]
      }
    },
    {
      $unwind: '$total'
    },
    {
      $project: {
        data: 1,
        total: '$total.count'
      }
    }
  ]

  const trailer = ['trailerStatus', 'trailerSize', 'trailerType']
  if (Object.keys(ctx.params.query).length > 0) {
    const fieldRegTrailer = ['trailerNo']
    const fieldReg = ['remarks1', 'remarks2', 'job', 'lastLocation']
    const fieldIdTrailer = ['trailer', 'company', 'vehicleNumber']
    const fieldId = ['_id']
    const field = ['indicator', '$or']
    const fieldDate = ['referenceDateTime1', 'referenceDateTime2', 'createdAt', 'updatedAt']

    Object.keys(ctx.params.query).forEach(val => {
      trailer.forEach(v => {
        if (`trailerNumber.${v}` === val) $match[`trailerNumber.${v}`] = ctx.params.query[val]
      })

      fieldRegTrailer.forEach((v) => {
        if (v === val && val === 'trailerNo') $match['trailerNumber.trailerNumber'] = { $regex: ctx.params.query[val], $options: 'i' }
        else if (v === val) $match[`trailerNumber.${v}`] = { $regex: ctx.params.query[val], $options: 'i' }
      })

      fieldReg.forEach((v) => {
        if (v === val) $match[v] = { $regex: ctx.params.query[val], $options: 'i' }
      })

      fieldIdTrailer.forEach((v) => {
        if (v === val && val === 'trailer') $match['trailerNumber._id'] = { $in: Array.isArray(ctx.params.query[val]) ? ctx.params.query[val].map(v => ObjectId(v)) : [ObjectId(ctx.params.query[val])] }
        else if (v === val && val === 'vehicleNumber') $match['trailerNumber.truckNumber'] = ObjectId(ctx.params.query[val])
        else if (v === val) $match[`trailerNumber.${v}`] = ObjectId(ctx.params.query[val])
      })
      fieldId.forEach((v) => {
        if (v === val && ObjectId.isValid(ctx.params.query[val])) $match[v] = ObjectId(ctx.params.query[val])
        else if (v === val) {
          if (Array.isArray(ctx.params.query[val])) throw new NotAcceptable('Query is not valid')
          $match[val] = {}
          if (Object.keys(ctx.params.query[val]).filter(key => ObjectId.isValid(ctx.params.query[val][key])).length > 0) {
            Object.keys(ctx.params.query[val]).forEach(key => {
              if (!$match[val][key]) $match[val][key] = [ObjectId(ctx.params.query[val][key])]
            })
          } else {
            Object.keys(ctx.params.query[val]).forEach(key => {
              if (!$match[val][key]) $match[val][key] = ctx.params.query[val][key].map(value => ObjectId(value))
            })
          }
        }
      })

      field.forEach((v) => {
        if (v === val && val === 'indicator') $match['trailerNumber.color'] = ctx.params.query[val]
        else if (v === val && val === '$or') $match[v] = ctx.params.query[val]
        else if (v === val) $match[`trailerNumber.${v}`] = ctx.params.query[val]
      })

      fieldDate.forEach(v => {
        if (v === val) {
          Object.keys(ctx.params.query[val]).forEach(k => {
            if (k === '$gte') $match[val] = { $gte: moment(ctx.params.query[val][k], 'DD/MM/YYYY').startOf('day').toDate() }
            else if (k === '$lte') $match[val] = { ...$match[val], $lte: moment(ctx.params.query[val][k], 'DD/MM/YYYY').endOf('day').toDate() }
            else $match[val] = { $gte: moment(ctx.params.query[val], 'DD/MM/YYYY').startOf('day').toDate(), $lte: moment(ctx.params.query[val], 'DD/MM/YYYY').endOf('day').toDate() }
          })
        }
      })
    })
  }

  $match['trailerNumber.deleted'] = false

  const lookup = []
  trailer.forEach(v => {
    lookup.push(
      { $lookup: { from: 'trailerconfigs', localField: `trailerNumber.${v}`, foreignField: '_id', as: `trailerNumber.${v}` } },
      { $unwind: { path: `$trailerNumber.${v}`, preserveNullAndEmptyArrays: true } },
      { $project: { [`trailerNumber.${v}.type`]: 0, [`trailerNumber.${v}.createdAt`]: 0, [`trailerNumber.${v}.updatedAt`]: 0, [`trailerNumber.${v}.__v`]: 0 } }
    )
  })

  query = [
    { $lookup: { from: 'trailermasters', localField: 'trailerNumber', foreignField: '_id', as: 'trailerNumber' } },
    { $unwind: { path: '$trailerNumber', preserveNullAndEmptyArrays: true } },
    { $match },
    { $project: { 'trailerNumber.deleted': 0 } },
    ...lookup,
    { $lookup: { from: 'vehicles', localField: 'trailerNumber.truckNumber', foreignField: '_id', as: 'trailerNumber.truckNumber' } },
    { $unwind: { path: '$trailerNumber.truckNumber', preserveNullAndEmptyArrays: true } },
    ...query
  ]

  const res = await ctx.app.service('trailer-management').Model.aggregate(query).collation({ locale: 'en', strength: 2 })

  if (res.length === 0) throw new NotFound(msg.TRAILER_GET_DELETED)

  ctx.result = {
    total: res[0].total,
    limit,
    skip,
    data: res[0].data
  }

  return ctx
}

const createData = () => async ctx => {
  logger.debug('create trailer management')
  if (!ctx.data.trailer) throw new NotAcceptable(msg.TRAILERNUMBER_EMPTY)
  if (!ctx.data.lastLocation) throw new NotAcceptable(msg.LASTLOCATION_EMPTY)
  ctx.data.trailerNumber = ObjectId(ctx.data.trailer)
  delete ctx.data.trailer
  let trailer

  if (typeof ctx.data.lastLocation === 'undefined' || ctx.data.lastLocation === '' || ctx.data.lastLocation === null) throw new NotAcceptable(msg.LASTLOCATION_EMPTY)
  if (ctx.data.lastLocation) {
    if (typeof ctx.data.lastLocation !== 'string') throw new NotAcceptable(msg.LASTLOCATION_STRING)

    const reg = /[0-9]{1,3}\.[0-9]{4}/g
    if (!reg.test(ctx.data.lastLocation)) throw new NotAcceptable(msg.LASTLOCATION_NOT_VALID)
  }

  if (ctx.data.trailerNumber) {
    try {
      const dataTrailer = await ctx.app.service('trailer-master').get(ctx.data.trailerNumber, { fromServer: true, query: {} })

      if (dataTrailer.deleted) throw new NotAcceptable('Trailer is deleted')
      trailer = dataTrailer._id
    } catch (e) {
      if (e.code === 406) throw new NotFound(msg.TRAILER_CREATE_DELETED)
      else throw new NotFound(msg.TRAILER_DATA)
    }
  }

  if (ctx.data.referenceDateTime1) {
    ctx.data.referenceDateTime1 = formatDateSG(ctx.data.referenceDateTime1)
  }

  if (ctx.data.referenceDateTime2) {
    ctx.data.referenceDateTime2 = formatDateSG(ctx.data.referenceDateTime2)
  }

  const dataManagement = await ctx.app.service('trailer-management').find({
    query: {
      trailerNumber: ctx.data.trailerNumber
    },
    paginate: false,
    server: true
  })

  if (dataManagement.length > 0) {
    const result = await ctx.app.service('trailer-management').patch(dataManagement[0]._id, ctx.data, { server: true })
    ctx.result = result
  }

  if (ctx.data.indicator) {
    if (typeof ctx.data.indicator !== 'string') throw new NotAcceptable(msg.INDICATOR_STRING)
    await ctx.app.service('trailer-master').patch(trailer, { color: ctx.data.indicator }, { user: ctx.params.user })
  }

  return ctx
}

const patchData = () => async ctx => {
  logger.debug('patch trailer management')
  if (Object.keys(ctx.data).length === 0) throw new NotAcceptable('please add data to patch trailer management')
  if (ctx.data.trailer || ctx.data.trailerNumber) {
    delete ctx.data.trailer
    delete ctx.data.trailerNumber
  }

  if (typeof ctx.data.lastLocation !== 'undefined' && (ctx.data.lastLocation === '' || ctx.data.lastLocation === null)) throw new NotAcceptable(msg.LASTLOCATION_EMPTY)
  if (ctx.data.lastLocation) {
    if (typeof ctx.data.lastLocation !== 'string') throw new NotAcceptable(msg.LASTLOCATION_STRING)

    const reg = /[0-9]{1,3}\.[0-9]{4}/g
    if (!reg.test(ctx.data.lastLocation)) throw new NotAcceptable(msg.LASTLOCATION_NOT_VALID)
  }

  if (ctx.data.indicator) {
    const data = await ctx.app.service('trailer-management').get(ctx.id, { query: {} })
    await ctx.app.service('trailer-master').patch(data.trailerNumber, { color: ctx.data.indicator }, { user: ctx.params.user })
  }
}

const populate = () => ctx => {
  ctx.params.query.$populate = [{
    path: 'trailerNumber',
    populate: [{ path: 'trailerStatus', select: 'name' }, { path: 'trailerSize', select: 'name' }, { path: 'trailerType', select: 'name' }, { path: 'truckNumber' }]
  }]

  return ctx
}

const handleTrailerDelete = () => ctx => {
  const user = ctx.params.user || null
  if (Array.isArray(ctx.result)) {
    if (ctx.result.length > 0) {
      const arrObj = ctx.result.filter(v => v.trailerNumber && !v.trailerNumber.deleted)
      if (arrObj.length === 0) throw new NotFound(msg.TRAILER_GET_DELETED)
    }
  } else {
    if (!ctx.result.trailerNumber || (ctx.result.trailerNumber && ctx.result.trailerNumber.deleted)) throw new NotFound(msg.TRAILER_GET_DELETED)
    if (user && user.group) delete ctx.result.trailerNumber.deleted
  }

  return ctx
}

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [iff(ctx => ctx.params.query.trailerStatus || ctx.params.query.trailerSize || ctx.params.query.trailerType, getConfig(false)),
      iff(ctx => ctx.params && !ctx.params.server, queryData())
    ],
    get: [populate()],
    create: [createData()],
    update: [disallow()],
    patch: [iff(ctx => !ctx.params.server, patchData())],
    remove: [async ctx => {
      const data = await ctx.app.service('trailer-management').find({ query: { _id: ctx.id } })
      if (ctx.params.user && ctx.params.user.group && data.data[0].trailerNumber.company && ctx.params.user.company.toString() !== data.data[0].trailerNumber.company.toString()) throw new NotAcceptable(msg.COMPANY_NOT_SAME)
    }]
  },

  after: {
    all: [],
    find: [],
    get: [iff(ctx => !ctx.params.deleteTrailer, handleTrailerDelete())],
    create: [async ctx => {
      const data = await ctx.app.service('trailer-master').get(ctx.result.trailerNumber)
      ctx.result.trailerNumber = data

      return ctx
    }],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [ctx => {
      if (ctx.error.code === 404) throw new NotFound(msg.TRAILER_GET_DELETED)
    }],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
