// Initializes the `trailer-management` service on path `/trailer-management`
const { TrailerManagement } = require('./trailer-management.class')
const createModel = require('../../models/trailer-management.model')
const hooks = require('./trailer-management.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch', '$populate']
  }

  // Initialize our service with any options it requires
  app.use('/trailer-management', new TrailerManagement(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-management')

  service.hooks(hooks)
}
