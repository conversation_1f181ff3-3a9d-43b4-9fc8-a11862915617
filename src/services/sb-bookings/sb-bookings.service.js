// Initializes the `sbBookings` service on path `/sb-bookings`
const { SbBookings } = require('./sb-bookings.class')
const hooks = require('./sb-bookings.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/sb-bookings', new SbBookings(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('sb-bookings')

  service.hooks(hooks)
}
