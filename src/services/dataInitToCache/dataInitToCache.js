const logger = require('@hooks/logger')

module.exports = function (app) {
  logger.info('data init trailer config')
  app.service('trailer-config').find({ paginate: false })
    .then(confData => {
      logger.debug('config data ', confData)
      if (confData.length > 0) {
        for (let i = 0; i < confData.length; i++) {
          app.redis.HSET(confData[i].type, `${confData[i]._id}`, confData[i].name)
        }
      }
    })
    .catch(err => logger.error('Error occur on retrieve trailer config', err))
  logger.info('finish set up data ini trailer config')
}
