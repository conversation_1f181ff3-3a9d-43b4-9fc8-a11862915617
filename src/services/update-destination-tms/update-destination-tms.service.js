// Initializes the `update-destination-tms` service on path `/updateDestinationTms`
const { UpdateDestinationTms } = require('./update-destination-tms.class')
const hooks = require('./update-destination-tms.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/updateDestinationTms', new UpdateDestinationTms(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('updateDestinationTms')

  service.hooks(hooks)
}
