// Initializes the `channel membership` service on path `/channel-membership`
const { ChannelMembership } = require('./channel-membership.class')
const hooks = require('./channel-membership.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/channel-membership', new ChannelMembership(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('channel-membership')

  service.hooks(hooks)
}
