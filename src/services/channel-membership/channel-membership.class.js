/* eslint-disable no-unused-vars */
exports.ChannelMembership = class ChannelMembership {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async create (data, params) {
    if (params.connection) {
      this.app.channel(data.channel).join(params.connection)
    }

    return data
  }

  async remove (id, params) {
    if (params.connection) {
      this.app.channel(id).leave(params.connection)
    }

    return { id }
  }
}
