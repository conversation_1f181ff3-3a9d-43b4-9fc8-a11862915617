const { authenticate } = require('@feathersjs/authentication').hooks
const { iff, isProvider } = require('feathers-hooks-common')
const { getConfig } = require('@hooks/data-update')
const { GeneralError, LengthRequired, NotAcceptable, NotFound } = require('@feathersjs/errors')
const ObjectId = require('mongoose').Types.ObjectId
const msg = require('@utils/commonMsg')
const { getPropertyValue } = require('@utils/commonFunc')
const logger = require('@hooks/logger')
const moment = require('moment')
const { trailerMasterValidation } = require('@schema/trailer-master')
let arrTrailerNumber = []

async function retrieveCompanyData (id, ctx, type) {
  let company = await ctx.app.service('groups').get(id).catch(err => logger.error(msg.GROUP_RETRIEVE, id, err))
  if (company.status !== 'active' && type === 'single') throw new NotAcceptable(msg.COMPANY_INACTIVE)
  if (company.status !== 'active' && type === 'multi') company = false
  return company
}

async function filterTrailerConfig (ctx, dataStatus, dataSize, dataType) {
  let status, size, type

  if (dataStatus) {
    const trailerStatus = await ctx.app.redis.HGETALL('trailerStatus')
    const resStatus = Object.keys(trailerStatus).filter(v => trailerStatus[v] === dataStatus)

    if (resStatus.length > 0) status = resStatus[0]
    else {
      const { _id } = await ctx.app.service('trailer-config').create({ name: dataStatus, type: 'trailerStatus' })
      status = _id
    }
  }

  if (dataSize) {
    const trailerSize = await ctx.app.redis.HGETALL('trailerSize')
    const resSize = Object.keys(trailerSize).filter(v => trailerSize[v] === dataSize)

    if (resSize.length > 0) size = resSize[0]
    else {
      const { _id } = await ctx.app.service('trailer-config').create({ name: dataSize, type: 'trailerSize' })
      size = _id
    }
  }

  if (dataType) {
    const trailerType = await ctx.app.redis.HGETALL('trailerType')
    const resType = Object.keys(trailerType).filter(v => trailerType[v] === dataType)

    if (resType.length > 0) type = resType[0]
    else {
      const { _id } = await ctx.app.service('trailer-config').create({ name: dataType, type: 'trailerType' })
      type = _id
    }
  }

  return { status, size, type }
}

async function softDeleteTrailer (ctx, user) {
  const id = ctx.id ? [ObjectId(ctx.id)] : ctx.params.query.trailerId.map(v => ObjectId(v))
  const getData = await ctx.app.service('trailer-master').find({
    query: {
      _id: { $in: id }
    },
    paginate: false
  })

  if (user.group) {
    getData.forEach(v => {
      if (v.company._id.toString() !== user.company.toString()) throw new NotAcceptable(msg.COMPANY_NOT_SAME)
    })
  }

  const data = ctx.id
    ? await ctx.app.service('trailer-master').patch(ctx.id, { deleted: true, updatedBy: user._id }, { fromServer: true })
    : await ctx.app.service('trailer-master').patch(null, { deleted: true, updatedBy: user._id }, {
      query: {
        _id: { $in: id }
      },
      fromServer: true
    })

  return data
}

async function checkDuplicateTrailerNumber (trailerNumber, company, ctx, data, type) {
  const failedTrailer = []
  const trailerNumberInCompany = await ctx.app.service('trailer-master').find({
    query: {
      trailerNumber,
      company
    },
    paginate: false,
    fromServer: true
  })

  if (trailerNumberInCompany.length > 0) {
    trailerNumberInCompany.forEach(v => {
      if (!v.deleted && type === 'single') throw new NotAcceptable(msg.TRAILER_NUMBER_EXIST)
      if (!v.deleted && type === 'multi') failedTrailer.push(msg.TRAILER_NUMBER_EXIST)
    })
  }

  const trailerNumberGlobal = await ctx.app.service('trailer-master').find({
    query: {
      trailerNumber,
      deleted: false
    },
    paginate: false,
    fromServer: true
  })

  if (trailerNumberGlobal.length > 0) {
    const licenceStatus = type === 'single' ? ctx.data.licenceStatus : data.licenceStatus

    trailerNumberGlobal.forEach(v => {
      if (v.licenceStatus && licenceStatus && type === 'single') throw new NotAcceptable(msg.TRAILER_NUMBER_GLOBAL_EXIST)
      if (v.licenceStatus && licenceStatus && type === 'multi') failedTrailer.push(msg.TRAILER_NUMBER_GLOBAL_EXIST)
    })
  }

  if (type === 'multi') {
    if (arrTrailerNumber.length > 0) {
      arrTrailerNumber.forEach(v => {
        if (v.trailerNumber === trailerNumber && v.licenceStatus && data.licenceStatus) failedTrailer.push(msg.TRAILER_NUMBER_GLOBAL_EXIST)
      })
    }

    arrTrailerNumber.push(data)
  }

  return failedTrailer
}

async function validationCreate (ctx, field, x, type) {
  const data = type === 'multi' ? ctx.data[x] : ctx.data
  let trailerNumber

  let failedTrailer = []

  if (type === 'multi') {
    if (data.deleted) failedTrailer.push(msg.INCLUDE_DELETE)

    if (data.trailerNumber === null || data.trailerNumber === '' || typeof data.trailerNumber === 'undefined') failedTrailer.push(msg.TRAILERNUMBER_EMPTY)
    else if (data.trailerNumber) trailerNumber = data.trailerNumber.replace(/\s/g, '').toUpperCase()

    if ((data.company === null || data.company === '' || typeof data.company === 'undefined')) failedTrailer.push(msg.COMPANY_EMPTY)

    for (let i = 0; i < field.length; i++) {
      if (data[field[i]] === null || data[field[i]] === '' || typeof data[field[i]] === 'undefined') failedTrailer.push(msg[`${field[i].toUpperCase()}_EMPTY`])
    }

    if (!ObjectId.isValid(data.company)) failedTrailer.push(msg.COMPANY_IS_OBJECT_ID)

    if (data.company && ObjectId.isValid(data.company)) {
      const fail = await checkDuplicateTrailerNumber(trailerNumber, data.company, ctx, data, 'multi')
      failedTrailer = [...failedTrailer, ...fail]

      const failCompany = await retrieveCompanyData(data.company, ctx, 'multi')
      if (!failCompany) failedTrailer.push(msg.COMPANY_INACTIVE)
    }

    if (data.chassisNumber && data.chassisNumber.length >= 30) failedTrailer.push(msg.CHASSIS_NUMBER_LENGTH)
    if (data.leasee) {
      if (!ObjectId.isValid(data.leasee)) failedTrailer.push(msg.LEASEE_IS_OBJECT_ID)
      if (ObjectId.isValid(data.leasee)) {
        const failCompany = await retrieveCompanyData(data.leasee, ctx, 'multi')
        if (!failCompany) failedTrailer.push(msg.COMPANY_INACTIVE)
      }

      if (data.company && data.leasee === data.company) failedTrailer.push(msg.LEASEE_SIMILIAR_WITH_COMPANY)
      if (data.truckNumber) {
        if (!ObjectId.isValid(data.truckNumber)) failedTrailer.push(msg.TRUCK_IS_OBJECT_ID)
        if (ObjectId.isValid(data.truckNumber)) {
          const truckNo = await ctx.app.service('vehicles')
            .get(data.truckNumber)
            .catch(err => logger.error('Error occur on retrieve the vehicles', data.truckNumber, err))
          if (!truckNo.status) failedTrailer.push(msg.TRUCK_INACTIVE)
        }
      }
    }

    return { trailerNumber, failedTrailer }
  } else {
    if (data.deleted) throw new NotAcceptable(msg.INCLUDE_DELETE)

    if ((data.trailerNumber === null || data.trailerNumber === '' || typeof data.trailerNumber === 'undefined')) throw new GeneralError(msg.TRAILERNUMBER_EMPTY)
    else if (data.trailerNumber) trailerNumber = data.trailerNumber.replace(/\s/g, '').toUpperCase()

    if ((data.company === null || data.company === '' || typeof data.company === 'undefined')) throw new GeneralError(msg.COMPANY_EMPTY)

    for (let i = 0; i < field.length; i++) {
      if ((data[field[i]] === null || data[field[i]] === '' || typeof data[field[i]] === 'undefined')) throw new GeneralError(msg[`${field[i].toUpperCase()}_EMPTY`])
    }
    if (!ObjectId.isValid(data.company)) throw new NotAcceptable(msg.COMPANY_IS_OBJECT_ID)
    if (data.company && ObjectId.isValid(data.company)) {
      await checkDuplicateTrailerNumber(trailerNumber, data.company, ctx, data, 'single')
      await retrieveCompanyData(data.company, ctx, 'single')
    }

    if (data.chassisNumber && data.chassisNumber.length >= 30) throw new LengthRequired(msg.CHASSIS_NUMBER_LENGTH)
    if (data.leasee) {
      if (!ObjectId.isValid(data.leasee)) throw new NotAcceptable(msg.LEASEE_IS_OBJECT_ID)
      if (ObjectId.isValid(data.leasee)) await retrieveCompanyData(data.leasee, ctx, 'single')

      if (data.company && data.leasee === data.company) throw new NotAcceptable(msg.LEASEE_SIMILIAR_WITH_COMPANY)
      if (data.truckNumber) {
        if (!ObjectId.isValid(data.truckNumber)) throw new NotAcceptable(msg.TRUCK_IS_OBJECT_ID)
        if (ObjectId.isValid(data.truckNumber)) {
          const truckNo = await ctx.app.service('vehicles')
            .get(data.truckNumber)
            .catch(err => logger.error('Error occur on retrieve the vehicles', data.truckNumber, err))
          if (!truckNo.status) throw new NotAcceptable(msg.TRUCK_INACTIVE)
        }
      }
    }

    return trailerNumber
  }
}

const validation = () => async ctx => {
  logger.debug('validation create trailer')
  const field = ['trailerStatus', 'trailerSize', 'trailerType', 'licenceStatus', 'onLease', 'billable', 'isPairingRequired',
    'mlwKg', 'ulwKg', 'registrationDate', 'roadTaxExpiryDate', 'insuranceExpiryDate', 'nextInspectionDate', 'color']

  if (Array.isArray(ctx.data)) {
    const hasFailedTrailer = []
    if (ctx.data.filter(v => !v.trailerNumber).length > 0) {
      throw new NotAcceptable('reject create the data, because one of data doesn\'t have trailer number')
    } else {
      for (let x = 0; x < ctx.data.length; x++) {
        const { trailerNumber, failedTrailer } = await validationCreate(ctx, field, x, 'multi')
        if (failedTrailer.length > 0) {
          hasFailedTrailer.push({ trailerNo: ctx.data[x].trailerNumber || '-', index: x, error: failedTrailer[0] })
          ctx.data[x].index = x
        } else ctx.data[x].trailerNumber = trailerNumber
      }
    }

    if (hasFailedTrailer.length > 0) {
      ctx.failedTrailer = hasFailedTrailer.map(v => ({ trailerNo: v.trailerNo, error: v.error }))
      hasFailedTrailer.forEach(v => ctx.data.splice(ctx.data.findIndex(x => x.index === v.index), 1))
    }

    if (ctx.data.length === 0) {
      ctx.result = {
        failedCreated: ctx.failedTrailer,
        successfullCreated: []
      }
    }

    arrTrailerNumber = []
  } else {
    ctx.data.trailerNumber = await validationCreate(ctx, field, 0, 'single')
  }

  return ctx
}

const createConfig = () => async ctx => {
  logger.debug('create trailer config')
  const arrField = ['manufactureDate', 'registrationDate', 'roadTaxExpiryDate', 'insuranceExpiryDate', 'nextInspectionDate', 'registrationExpiryDate', 'pairingDate']
  if (Array.isArray(ctx.data)) {
    for (let x = 0; x < ctx.data.length; x++) {
      const redisTrailerConf = await ctx.app.redis.keys('trailer*')

      if (redisTrailerConf.length > 0) {
        const { status, size, type } = await filterTrailerConfig(ctx, ctx.data[x].trailerStatus, ctx.data[x].trailerSize, ctx.data[x].trailerType)

        ctx.data[x].trailerStatus = status
        ctx.data[x].trailerSize = size
        ctx.data[x].trailerType = type
      } else {
        const config = []

        if (ctx.data[x].trailerStatus) config.push({ name: ctx.data[x].trailerStatus, type: 'trailerStatus' })
        if (ctx.data[x].trailerSize) config.push({ name: ctx.data[x].trailerSize, type: 'trailerSize' })
        if (ctx.data[x].trailerType) config.push({ name: ctx.data[x].trailerType, type: 'trailerType' })
        const arrConf = config.map(e => e.name).map((e, i, final) => final.indexOf(e) === i && i).filter((e) => config[e]).map(e => config[e])

        const confData = await ctx.app.service('trailer-config').create(arrConf)

        for (let i = 0; i < confData.length; i++) {
          ctx.data[x][confData[i].type] = confData[i]._id
        }
      }
      for (let i = 0; i < arrField.length; i++) {
        if (ctx.data[x][arrField[i]]) ctx.data[x][arrField[i]] = moment(ctx.data[x][arrField[i]], 'DD-MM-YYYY').format('MM/DD/YYYY')
      }
    }
  } else {
    const redisTrailerConf = await ctx.app.redis.keys('trailer*')

    if (redisTrailerConf.length > 0) {
      const { status, size, type } = await filterTrailerConfig(ctx, ctx.data.trailerStatus, ctx.data.trailerSize, ctx.data.trailerType)

      ctx.data.trailerStatus = status
      ctx.data.trailerSize = size
      ctx.data.trailerType = type
    } else {
      const config = []

      if (ctx.data.trailerStatus) config.push({ name: ctx.data.trailerStatus, type: 'trailerStatus' })
      if (ctx.data.trailerSize) config.push({ name: ctx.data.trailerSize, type: 'trailerSize' })
      if (ctx.data.trailerType) config.push({ name: ctx.data.trailerType, type: 'trailerType' })

      const confData = await ctx.app.service('trailer-config').create(config)

      for (let i = 0; i < confData.length; i++) {
        ctx.data[confData[i].type] = confData[i]._id
      }
    }

    for (let i = 0; i < arrField.length; i++) {
      if (ctx.data[arrField[i]]) ctx.data[arrField[i]] = moment(ctx.data[arrField[i]], 'DD-MM-YYYY').format('MM/DD/YYYY')
    }
  }

  if (Array.isArray(ctx.data)) {
    for (let i = 0; i < ctx.data.length; i++) {
      ctx.data[i].createdBy = ctx.params.user._id
    }
  } else ctx.data.createdBy = ctx.params.user._id

  return ctx
}

const patchConfig = () => async ctx => {
  logger.debug('patch trailer')
  if (!ctx.id) throw new NotAcceptable(msg.ID_EMPTY)
  const field = ['leasee', 'remarks', 'pairingDate', 'pairedBy']
  const arrField = ['manufactureDate', 'registrationDate', 'roadTaxExpiryDate', 'insuranceExpiryDate', 'nextInspectionDate', 'registrationExpiryDate', 'pairingDate']
  const mandatory = ['trailerNumber', 'company', 'truckNumber', 'trailerStatus', 'trailerSize', 'trailerType', 'licenceStatus', 'onLease', 'billable', 'isPairingRequired',
    'mlwKg', 'ulwKg', 'registrationDate', 'roadTaxExpiryDate', 'insuranceExpiryDate', 'nextInspectionDate', 'color']

  Object.keys(v => {
    if (!field.includes(v) === null) throw new GeneralError(msg[`${v.toUpperCase()}_EMPTY`])
  })

  for (let i = 0; i < arrField.length; i++) {
    if (ctx.data[arrField[i]]) ctx.data[arrField[i]] = moment(ctx.data[arrField[i]], 'DD-MM-YYYY').format('MM/DD/YYYY')
  }

  for (let i = 0; i < mandatory.length; i++) {
    if (typeof ctx.data[mandatory[i]] !== 'undefined' && (ctx.data[mandatory[i]] === null || ctx.data[mandatory[i]] === '')) delete ctx.data[mandatory[i]]
  }

  const data = await ctx.app.service('trailer-master').get(ctx.id, { query: {}, fromServer: true }).catch(err => logger.error('Oops! something when wrong ', err))
  if (data.deleted) throw new NotAcceptable(msg.TRAILER_HAS_DELETED)

  if (ctx.data.company) {
    if (!ObjectId.isValid(ctx.data.company)) throw new NotAcceptable(msg.COMPANY_IS_OBJECT_ID)
    await retrieveCompanyData(ctx.data.company, ctx, 'single')
  }
  if (ctx.data.leasee) {
    if (!ObjectId.isValid(ctx.data.leasee)) { throw new NotAcceptable(msg.LEASEE_IS_OBJECT_ID) }
    await retrieveCompanyData(ctx.data.leasee, ctx, 'single')

    if ((ctx.data.company && ctx.data.leasee === ctx.data.company) || (!ctx.data.company && ctx.data.leasee === data.company._id)) throw new NotAcceptable(msg.LEASEE_SIMILIAR_WITH_COMPANY)
    if (ctx.data.truckNumber) {
      if (!ObjectId.isValid(ctx.data.truckNumber)) throw new NotAcceptable(msg.TRUCK_IS_OBJECT_ID)
      const truckNo = await ctx.app.service('vehicles')
        .get(ctx.data.truckNumber)
        .catch(err => logger.error('Error occur on retrieve the vehicles', ctx.data.truckNumber, err))
      if (!truckNo.status) throw new NotAcceptable(msg.TRUCK_INACTIVE)
    }
  }

  if (!ctx.data.company) ctx.data.company = data.company._id
  if (ctx.data.trailerNumber) {
    ctx.data.trailerNumber = ctx.data.trailerNumber.replace(/\s/g, '').toUpperCase()
    if (ctx.data.licenceStatus === null || ctx.data.licenceStatus === '' || typeof ctx.data.licenceStatus === 'undefined') ctx.data.licenceStatus = data.licenceStatus
    if (ctx.data.trailerNumber !== data.trailerNumber) {
      ctx.data.licenceStatus = ctx.data.licenceStatus || data.licenceStatus
      await checkDuplicateTrailerNumber(ctx.data.trailerNumber, ctx.data.company, ctx, null, 'single')
    }
  }

  const { status, size, type } = await filterTrailerConfig(ctx, ctx.data.trailerStatus || null, ctx.data.trailerSize || null, ctx.data.trailerType || null)

  if (ctx.data.trailerStatus) ctx.data.trailerStatus = status
  if (ctx.data.trailerSize) ctx.data.trailerSize = size
  if (ctx.data.trailerType) ctx.data.trailerType = type

  if (ctx.params && ctx.params.user) ctx.data.updatedBy = ctx.params.user._id

  return ctx
}

const deleteConfig = () => async ctx => {
  const user = ctx.params.user
  ctx.result = await softDeleteTrailer(ctx, user)

  return ctx
}

const populate = (type) => ctx => {
  logger.debug('populate trailer master')
  if (type === 'before') {
    if (Object.keys(ctx.params).length === 0) ctx.params = { query: {} }
    ctx.params.query.$populate = [
      { path: 'company' }, { path: 'leasee' }, { path: 'trailerStatus' }, { path: 'trailerSize' }, { path: 'trailerType' }, { path: 'truckNumber' }
    ]
  } else {
    const attributeName = 'name'
    if (!ctx.params.fromServer && Array.isArray(ctx.result) && ctx.result.length === 0) throw new NotFound(msg.TRAILER_CREATE_DELETED)
    else {
      if (ctx.result.data && ctx.result.data.length > 0) {
        ctx.result.data = ctx.result.data.map(v => {
          v.trailerStatus = getPropertyValue(v.trailerStatus, attributeName)
          v.trailerSize = getPropertyValue(v.trailerSize, attributeName)
          v.trailerType = getPropertyValue(v.trailerType, attributeName)

          return v
        })
      } else if (!ctx.result.data && !Array.isArray(ctx.result) && Object.keys(ctx.result).length > 0 && !ctx.result.deleted) {
        ctx.result.trailerStatus = getPropertyValue(ctx.result.trailerStatus, attributeName)
        ctx.result.trailerSize = getPropertyValue(ctx.result.trailerSize, attributeName)
        ctx.result.trailerType = getPropertyValue(ctx.result.trailerType, attributeName)
      }
    }
  }

  return ctx
}

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [iff(ctx => ctx.params.query.trailerStatus || ctx.params.query.trailerSize || ctx.params.query.trailerType, getConfig()),
      ctx => {
        if (ctx.params.user && !ctx.params.user.group && ctx.params.query && typeof ctx.params.query.deleted !== 'undefined' && ctx.params.query.deleted.toLowerCase() === 'true') ctx.params.query.deleted = true
        else ctx.params.query.deleted = false

        const fieldReg = ['trailerNumber', 'remarks']

        if (ctx.params.query && !ctx.params.fromImport) {
          fieldReg.forEach(v => {
            Object.keys(ctx.params.query).forEach(val => {
              if (v === val) ctx.params.query[val] = { $regex: ctx.params.query[val], $options: 'i' }
            })
          })
        }

        return ctx
      }, populate('before')
    ],
    get: [populate('before')],
    create: [validation(), iff(ctx => !Array.isArray(ctx.data), trailerMasterValidation), createConfig()],
    update: [],
    patch: [ctx => {
      if (ctx.params && !ctx.params.fromServer && ctx.data.deleted) throw new NotAcceptable('patch trailer not acceptable, "deleted" status is true')
    }, iff(ctx => !ctx.data.deleted, patchConfig())],
    remove: [deleteConfig()]
  },

  after: {
    all: [iff(isProvider('external'), ctx => {
      if (ctx.result.data && ctx.result.data.length) {
        ctx.result.data.forEach(v => {
          delete v.deleted

          return v
        })
      }

      return ctx
    })],
    find: [populate('after')],
    get: [populate('after'), ctx => {
      if (!ctx.params.fromServer && typeof ctx.result.deleted !== 'undefined') delete ctx.result.deleted

      return ctx
    }],
    create: [ctx => {
      if (Array.isArray(ctx.result)) {
        const result = ctx.result

        ctx.result = {
          failedCreated: [],
          successfullCreated: result
        }
        if (ctx.failedTrailer && ctx.failedTrailer.length > 0) ctx.result.failedCreated = ctx.failedTrailer
      }

      return ctx
    }],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
