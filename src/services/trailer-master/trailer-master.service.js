// Initializes the `trailer-master` service on path `/trailer-master`
const { TrailerMaster } = require('./trailer-master.class')
const createModel = require('../../models/trailer-master.model')
const hooks = require('./trailer-master.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$elemMatch', '$populate'],
    multi: ['patch', 'create', 'remove']
  }

  // Initialize our service with any options it requires
  app.use('/trailer-master', new TrailerMaster(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('trailer-master')

  service.hooks(hooks)
}
