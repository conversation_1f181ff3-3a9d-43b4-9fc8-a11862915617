// Initializes the `message-histories` service on path `/message-histories`
const { MessageHistory } = require('./message-histories.class')
const createModel = require('../../models/message-histories.model')
const hooks = require('./message-histories.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    multi: ['create'],
    whitelist: ['$regex', '$options']
  }

  // Initialize our service with any options it requires
  app.use('/message-histories', new MessageHistory(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('message-histories')

  app.use('/message-histories/store-message-histories', {
    async create (data, params) {
      let resultStore = {}
      try {
        resultStore = await service.storeMsgHistory(params)
      } catch (e) {
        resultStore = { message: 'System Error Cannot Generate the Request' }
      }
      return resultStore
    }
  })

  service.hooks(hooks)
}
