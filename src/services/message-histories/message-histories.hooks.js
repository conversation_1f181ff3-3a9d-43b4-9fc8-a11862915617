const { authenticate } = require('@feathersjs/authentication').hooks
const logger = require('@hooks/logger')
const moment = require('moment')

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [ctx => {
      ctx.params.query.messagDateTime.$gte = moment(`${ctx.params.query.messagDateTime.$gte}T16:00:00Z`).subtract(1, 'days')
      ctx.params.query.messagDateTime.$lt = moment(`${ctx.params.query.messagDateTime.$lt}T15:59:59Z`)
      logger.debug('user extract data with date range', ctx.params.query.messagDateTime.$gte, ctx.params.query.messagDateTime.$lt)

      if (!ctx.params.query.company) {
        throw new Error('Please select')
      }
    }],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [async ctx => {
      ctx.result.data.forEach(item => {
        item.messagDateTime = moment(item.messagDateTime).tz(ctx.app.get('agenda').timezone).format('DD/MM/YYYY HH:mm:ss')
        item.dataMsg = item.dataMsg.replace(/<br\/>/gi, ' ')
      })
    }],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
