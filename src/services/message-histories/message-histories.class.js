const { Service } = require('feathers-mongoose')
const moment = require('moment')
const logger = require('@hooks/logger')
const { exec } = require('child_process')
const pLimit = require('p-limit')

const BATCH_SIZE = 1000
const CONCURRENCY_LIMIT = 5

exports.MessageHistory = class MessageHistory extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async storeMsgHistory (params) {
    logger.info('Start generate Message Histories data', params)

    const result = []
    let endDate
    let runarchive = true
    let startDate
    let tableName = 'messages'

    if (params.query && params.query.messagDateTime) {
      startDate = new Date(params.query.messagDateTime.$gte)
      endDate = new Date(params.query.messagDateTime.$lt)
      runarchive = false

      if (params.query.tableName) tableName = params.query.tableName
    } else {
      const yesterday = moment().subtract(1, 'days')
      const start = yesterday.clone().hours(21).minutes(0).seconds(0).milliseconds(0).utc(true)

      startDate = start.toDate()
      endDate = start.clone().add({ hours: 23, minutes: 59, seconds: 59, milliseconds: 999 }).toDate()
    }

    const driver = await this.app.service('vehicles').find({
      paginate: false,
      query: { $limit: 1, driver: { $exists: true, $ne: null } }
    }).then(res => res[0]?.driver).catch(() => null)

    const driverRoleObj = await this.app.service('users').find({
      paginate: false,
      query: { _id: driver }
    }).then(res => res[0]?.roleAcl).catch(() => null)

    const { data: companies } = await this.app.service('groups').find({ query: { level: 2 } })

    const limit = pLimit(CONCURRENCY_LIMIT)
    const tasks = companies.map(cmpObj => limit(async () => {
      try {
        logger.info('Start processing', cmpObj.name)

        const historymsg = await this.generateCompanyMessages(cmpObj, tableName, startDate, endDate, driverRoleObj)
        const historymsgMap = this.transformMessages(historymsg)

        for (let i = 0; i < historymsgMap.length; i += BATCH_SIZE) {
          const chunk = historymsgMap.slice(i, i + BATCH_SIZE)

          await this.Model.bulkWrite(
            chunk.map(doc => ({ insertOne: { document: doc } })),
            { ordered: false }
          )
        }

        logger.info(`Finished processing ${cmpObj.name}`)
        result.push({ message: `${cmpObj.name} Message History Finish Generated` })
      } catch (e) {
        logger.error(`Error processing ${cmpObj.name}`, e)
        result.push({ message: `${cmpObj.name} Message History Unable to generate` })
      }
    }))

    await Promise.all(tasks)

    if (runarchive) {
      this.messagearchive(this.app)
    }

    return result
  }

  async generateCompanyMessages (company, tableName, startDate, endDate, driverRoleObj) {
    const pipeline = [{
      $project: {
        messagDateTime: '$messageObj.createdAt',
        messageSendFrom: '$senderName.fullname',
        message: '$messageObj.content',
        vehicle: 1,
        vehicleNo: '$vehicleNo.vehicleNo',
        company: 1,
        companyName: '$companyName.name',
        userRole: {
          $toString: '$senderName.roleAcl'
        }
      }
    }, {
      $match: { company: { $in: [company._id] } }
    }, {
      $lookup: {
        from: `${tableName}`,
        let: {
          conversationId: '$_id'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$conversationId', '$$conversationId']
            },
            createdAt: {
              $gte: startDate,
              $lt: endDate
            }
          }
        }, {
          $project: {
            _id: 0,
            content: 1,
            createdAt: 1,
            sender: 1
          }
        }],
        as: 'messageObj'
      }
    }, {
      $unwind: {
        path: '$messageObj',
        preserveNullAndEmptyArrays: true
      }
    }, {
      $lookup: {
        from: 'vehicles',
        let: {
          vehicleId: '$vehicle'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$_id', '$$vehicleId']
            }
          }
        }, {
          $project: {
            _id: 0,
            vehicleNo: 1
          }
        }],
        as: 'vehicleNo'
      }
    }, {
      $unwind: {
        path: '$vehicleNo',
        preserveNullAndEmptyArrays: true
      }
    }, {
      $unwind: '$company'
    }, {
      $lookup: {
        from: 'groups',
        let: {
          company: '$company'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$_id', '$$company']
            }
          }
        }, {
          $project: {
            _id: 0,
            name: 1
          }
        }],
        as: 'companyName'
      }
    }, {
      $unwind: {
        path: '$companyName',
        preserveNullAndEmptyArrays: true
      }
    }, {
      $lookup: {
        from: 'users',
        let: {
          sender: '$messageObj.sender'
        },
        pipeline: [{
          $match: {
            $expr: {
              $eq: ['$_id', '$$sender']
            }
          }
        }, {
          $project: {
            _id: 0,
            fullname: 1,
            roleAcl: 1
          }
        }],
        as: 'senderName'
      }
    }, {
      $unwind: {
        path: '$senderName',
        preserveNullAndEmptyArrays: true
      }
    }, {
      $addFields: {
        messagDateTime: '$messageObj.createdAt',
        message: '$messageObj.content',
        vehicleNo: '$vehicleNo.vehicleNo',
        companyName: '$companyName.name',
        messageSendFrom: '$senderName.fullname',
        userRole: {
          $toString: '$senderName.roleAcl'
        }
      }
    }, {
      $sort: { 'messageObj.createdAt': 1 }
    }]

    return this.app.service('conversations').Model.aggregate(pipeline).option({ allowDiskUse: true })
  }

  messagearchive (app) {
    try {
      const shellcmd = `sh script/messagesarchive.sh '${app.get('mongodb')}'`

      logger.debug('Excecute cmd', shellcmd)

      exec(shellcmd, (error, stdout, stderr) => {
        logger.info('call back on run script', stdout, stderr)

        if (error) {
          throw error
        }
      })
    } catch (e) {
      logger.error('Error occur on messagearchive', e)
    }
  }

  transformMessages (historymsg) {
    let lastMessage = ''
    let lastVehicleNo = ''

    return historymsg.filter(result => {
      if (!result.message || !result.vehicleNo) return false

      const message = result.message.toLowerCase()
      const vehicleNo = result.vehicleNo

      if (message === lastMessage && vehicleNo === lastVehicleNo) return false

      lastMessage = message
      lastVehicleNo = vehicleNo

      if (['logged in', 'started trip', 'completed trip', 'logged out'].some(k => message.includes(k))) return false

      return true
    }).map(result => {
      const messageStr = result.message.replace(/(\r\n|\n|\r)/gm, '')
      const base = {
        company: result.company,
        vehicle: result.vehicle,
        messagDateTime: result.messagDateTime,
        userSend: result.vehicleNo || 'UNKNOWN',
        userRec: 'PSA',
        dataMsg: messageStr
      }

      if (result.messageSendFrom) {
        if (result.userRole === String(result.driverRole)) {
          base.userRec = result.message.toLowerCase().includes('esurvey') || result.message.toLowerCase().includes('in position') ? 'PSA' : result.companyName
        } else {
          base.userSend = result.messageSendFrom
          base.userRec = result.vehicleNo
        }
      } else {
        base.userSend = 'PORTNET USER'
      }

      return base
    })
  }
}
