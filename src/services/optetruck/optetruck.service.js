// Initializes the `optetruck` service on path `/optetruck`
const { Optetruck } = require('./optetruck.class')
const hooks = require('./optetruck.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/optetruck', new Optetruck(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('optetruck')

  service.hooks(hooks)
}
