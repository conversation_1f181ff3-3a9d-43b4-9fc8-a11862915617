/* eslint-disable no-unused-vars */
const axios = require('axios')
const logger = require('@hooks/logger')
const { GeneralError } = require('@feathersjs/errors')
const moment = require('moment-timezone')
const ejs = require('ejs')
const { promisify } = require('util')
const renderFile = promisify(ejs.renderFile)

exports.Optetruck = class Optetruck {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async create (data, params) {
    let service = ''
    const status = ['Started', 'Completed', 'Rejected', 'start pickup', 'completed pickup', 'start delivery', 'completed delivery']
    let optetruckUrl = this.app.get('optetruckUrl')
    const job = await this.app.service('jobs').get(data.jobId)

    logger.info(`data patch job send to ${job.thirdParty ? job.thirdParty : 'OptETruck'}`, data)

    let tmsApiKey
    if (job.thirdParty) {
      const apiKey = await this.app.service('api-keys').find({
        query: {
          apiKeyName: job.thirdParty
        },
        paginate: false
      })
      if (apiKey.length > 0) {
        optetruckUrl = apiKey[0].responseWebhook
        tmsApiKey = apiKey[0].tmsAccessToken
      }
    }

    const setting = await this.app.service('system-settings').find({ paginate: false })

    let dataDispatch = {
      movementId: job.movementId,
      timestamp: moment().tz('Asia/Singapore').format('yyyy-MM-DDTHH:mm:ssZ')
    }

    dataDispatch.haulierOrgCode = (job && job.haulierC) ? job.haulierC : await this.getHaulierCode(job.company)

    if (job && job.area_id) dataDispatch.tripId = job.tripId

    if (data.jobStatus && status.includes(data.jobStatus)) {
      service = 'updateStatus'
      dataDispatch = {
        ...dataDispatch,
        stC: data.jobStatus.toUpperCase(),
        vehicleRegnN: job.truckNumber,
        trailerRegnN: job.trailerNumber || ''
      }

      if (data.driverName) {
        dataDispatch.driverM = job.driverName
      }
    } else {
      service = 'updateContainerMovementDetail'
      dataDispatch = {
        ...dataDispatch
      }

      if (data.cntrN) {
        dataDispatch.cntrN = data.cntrN
      }

      if (data.sealN) {
        dataDispatch.sealN = data.sealN
      }

      if (data.trailerRegN) {
        dataDispatch.trailerRegnN = data.trailerRegN
      }

      if (data.tareWeight) {
        dataDispatch.tareWeight = data.tareWeight
      }
    }

    // send data dispact to server ops E truck
    let callback = ''
    let retryCount = 0
    const retryDelay = 2 * 60 * 1000 // 2 minutes in millisecond
    let firstTryTimestamp = ''
    let secondTryTimestamp = ''

    while (retryCount < 3) {
      try {
        if (retryCount > 0) {
          logger.info(`Retrying axios.post, attempt ${retryCount}`, dataDispatch)

          await new Promise(resolve => setTimeout(resolve, retryDelay))

          if (retryCount === 1) {
            firstTryTimestamp = moment().tz('Asia/Singapore').format('yyyy-MM-DDTHH:mm:ssZ')
          } else if (retryCount === 2) {
            secondTryTimestamp = moment().tz('Asia/Singapore').format('yyyy-MM-DDTHH:mm:ssZ')
          }
        }

        if (optetruckUrl) {
          logger.info(`data send to ${job.thirdParty ? job.thirdParty : 'OptETruck'}`, dataDispatch, service)
          callback = await axios.post(`${optetruckUrl}/${service}`, dataDispatch, {
            headers: {
              apikey: tmsApiKey || setting[0].optetruckApiKey
            }
          })
        } else {
          callback = { data: { status: 'success', dataDispatch } }
        }
        callback = callback.data
        logger.info(`successfully sent to ${job.thirdParty ? job.thirdParty : 'OptETruck'}`)
        logger.info(`callback from ${job.thirdParty ? job.thirdParty : 'OptETruck'}`, callback)
        return callback
      } catch (error) {
        logger.error('Oops! something went wrong ', error)
        retryCount++
        if (retryCount >= 3) {
          // send email
          const email = this.app.get('cdasAdminEmail')

          renderFile(`${this.app.get('template').email}/oet-timeout.ejs`, {
            subject: 'CTR Notification - Update OET Unsuccessful',
            data: {
              ...dataDispatch,
              firstTryTimestamp,
              secondTryTimestamp
            },
            error: error.message
          }).then((html) => {
            const payloadEmail = {
              from: this.app.get('smtp').sender,
              to: email,
              subject: 'CTR Notification - Update OET Unsuccessful',
              html
            }

            this.app
              .service('mailer')
              .create(payloadEmail)
          }).catch(err => {
            logger.error('Oops! something wrong', err)
            const newError = new GeneralError('fail')
            throw newError
          })
        }
      }
    }
  }

  async getHaulierCode (companyId) {
    const company = await this.app.service('groups').get(companyId, {
      query: { $select: ['groupInfo'] }
    })

    const [findUen] = await this.app.service('cdi-participants').find({
      paginate: false,
      query: {
        uen: company.groupInfo.uen
      }
    })

    return findUen.code
  }
}
