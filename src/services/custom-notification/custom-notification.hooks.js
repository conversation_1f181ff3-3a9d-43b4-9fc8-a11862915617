const { authenticate } = require('@feathersjs/authentication').hooks
const logger = require('@hooks/logger')
const { disallow } = require('feathers-hooks-common')
const fs = require('fs')
const { NotAcceptable, BadRequest } = require('@feathersjs/errors')
const msg = require('@utils/commonMsg')
const fileType = require('file-type')
const ObjectId = require('mongoose').Types.ObjectId
const { iff } = require('feathers-hooks-common')

function createReadStreamSync (file, options) {
  if (!options) { options = {} }
  if (!options.flags) { options.flags = 'r' }
  if (!options.fd) { options.fd = fs.openSync(file, options.flags) }
  return fs.createReadStream(file, options)
}

const sendEmail = () => async ctx => {
  if (!ctx.data.from) throw new NotAcceptable(msg.FROM_EMPTY)

  let attachments = ''

  if (ctx.data.attachments && ctx.data.filename) {
    const padding = ctx.data.attachments.endsWith('==') ? 2 : 1
    const base64 = ctx.data.attachments.replace(/data:.*base64,/i, '')
    const fileSize = (base64.length * (3 / 4)) - padding

    if (fileSize > (10 * 1024 * 1024)) {
      throw new BadRequest('Attachment size must less or equal than 10 MB')
    }

    const buffer = Buffer.from(base64, 'base64')
    const format = await fileType.fromBuffer(buffer)
    const fileFormat = ctx.data.filename.split('.')

    if (fileFormat.length === 1) throw new NotAcceptable('please add format file on filename')
    if (format && format.ext !== fileFormat[1]) throw new NotAcceptable('format file not equal with filename')

    fs.writeFileSync(`${ctx.app.get('public')}/${ctx.data.filename}`, buffer)
    const stream = createReadStreamSync(`${ctx.app.get('public')}/${ctx.data.filename}`)
    logger.info('stream data', stream)
    attachments = stream
  }

  const payloadEmail = {
    from: ctx.data.from,
    to: ctx.data.receiver,
    subject: ctx.data.subject,
    html: ctx.data.html || '',
    attachments: ctx.data.attachments ? { filename: ctx.data.filename, content: attachments } : ''
  }

  logger.debug('payload email ', payloadEmail)

  ctx.app
    .service('mailer')
    .create(payloadEmail)

  if (ctx.data.attachments) {
    fs.unlinkSync(`${ctx.app.get('public')}/${ctx.data.filename}`, function (err) {
      if (err) throw err
      // if no error, file has been deleted successfully
      logger.debug('File successfully deleted!')
    })
  }

  return ctx
}

const sendPush = () => async ctx => {
  if (!ctx.data.description) throw new NotAcceptable(msg.DESCRIPTION_EMPTY)
  if (!ObjectId.isValid(ctx.data.receiver)) throw new NotAcceptable(msg.RECEIVER_OBJECTID)
  const receiver = await getUser(ctx, ctx.data.receiver)
  ctx.data.user = receiver.fullname

  if (receiver.device) {
    await ctx.app.service('notifications').create({
      channel: 'push',
      title: ctx.data.subject,
      type: ctx.data.subject,
      to: ctx.data.receiver,
      description: ctx.data.description
    }).catch(() => null)
  }

  return ctx
}

async function getUser (ctx, userId) {
  return await ctx.app.service('users').get(userId)
}

module.exports = {
  before: {
    all: [authenticate('jwt')],
    find: [disallow('external')],
    get: [disallow('external')],
    create: [ctx => {
      const arrField = ['receiver', 'subject']
      Object.keys(ctx.data).forEach(v => {
        if (arrField.includes(v) && !ctx.data[v] && v !== 'attachments') throw new NotAcceptable(msg[`${v.toUpperCase()}_EMPTY`])
      })
    }, iff(ctx => ctx.data.type.toLowerCase() === 'email', sendEmail()), iff(ctx => ctx.data.type.toLowerCase() === 'push', sendPush())],
    update: [disallow('external')],
    patch: [disallow('external')],
    remove: [disallow('external')]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [async ctx => {
      ctx.result = `${ctx.data.type.toLowerCase() !== 'email' ? 'Push Notification' : 'Email'} Successfully Sent.`

      const activityLogs = {
        action: 'create',
        module: 'custom-notification',
        user: ctx.params.user._id,
        data: {
          from: ctx.data.from || '',
          to: ctx.data.user,
          subject: ctx.data.subject,
          type: ctx.data.type,
          html: ctx.data.html || ''
        }
      }
      ctx.app.service('activity-logs').create(activityLogs)
    }],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
}
