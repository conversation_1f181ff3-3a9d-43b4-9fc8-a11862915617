// Initializes the `custom-notification` service on path `/custom-notification`
const { CustomNotification } = require('./custom-notification.class')
const hooks = require('./custom-notification.hooks')

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/custom-notification', new CustomNotification(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('custom-notification')

  service.hooks(hooks)
}
