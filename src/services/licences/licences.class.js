const { Service } = require('feathers-mongoose')
const { GeneralError } = require('@feathersjs/errors')
const logger = require('@hooks/logger')

exports.Licences = class Licences extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async remove (id) {
    const license = await super.get(id)
    if (license.tag) {
      const newError = new GeneralError('This licence is the default and cannot be deleted')
      throw newError
    } else {
      const subscription = await this.app.service('subscriptions').find({
        query: {
          license: license._id
        },
        paginate: false
      })
      if (subscription.length > 0) {
        const companyId = []
        for (let i = 0; i < subscription.length; i++) {
          companyId.push(subscription[i].company)
        }
        logger.debug('company Id ', companyId)
        const checkActive = await this.app.service('groups').find({
          query: {
            _id: {
              $in: companyId
            }
            // status: 'active'
          },
          paginate: false
        })
        if (checkActive.length > 0) {
          const newError = new GeneralError('This licence using by some company and cannot be deleted')
          throw newError
        } else {
          await super.remove(id)
        }
      } else {
        await super.remove(id)
      }
    }
    return license
  }
}
