// Initializes the `licences` service on path `/licences`
const { Licences } = require('./licences.class')
const createModel = require('../../models/licences.model')
const hooks = require('./licences.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$options', '$regex']
  }

  // Initialize our service with any options it requires
  app.use('/licences', new Licences(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('licences')

  service.hooks(hooks)
}
