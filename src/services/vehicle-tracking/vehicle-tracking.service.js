// Initializes the `vehicle-tracking` service on path `/vehicle-tracking`
const { VehicleTracking } = require('./vehicle-tracking.class')
const createModel = require('../../models/vehicle-tracking.model')
const hooks = require('./vehicle-tracking.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/vehicle-tracking', new VehicleTracking(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('vehicle-tracking')

  // route for survey
  app.use('/vehicle-tracking/current-locations', {
    async get (id, params) {
      const location = await service.currentLocation(id)

      return location
    }
  })

  service.hooks(hooks)
}
