const { Service } = require('feathers-mongoose')
const moment = require('moment')

exports.VehicleTracking = class VehicleTracking extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async create (data, params) {
    // get tracking data
    const today = new Date()
    const tracking = await super.find({
      query: {
        $limit: 1,
        createdAt: {
          $gte: moment(today).startOf('day').toISOString(),
          $lte: moment(today).endOf('day').toISOString()
        }
      }
    })

    const vehicleId = data.vehicle
    const long = data.routes[0].location.coordinates[0]
    const lat = data.routes[0].location.coordinates[1]

    // inset new record if document not found
    if (tracking.total < 1) {
      // store data to redis
      this.app.redis.HSET('vehicle', `${vehicleId}`, `${long},${lat}`)

      return super.create(data, params)
    }

    // store data to redis
    this.app.redis.HSET('vehicle', `${vehicleId}`, `${long},${lat}`)

    // push new routes into current document
    const newRoutes = data.routes[0]
    return super.patch(
      { _id: tracking.data[0]._id },
      {
        $push: {
          routes: newRoutes
        }
      }
    )
  }

  async currentLocation (id) {
    // get current location to redis
    const currentLocation = await this.app.redis.HGET('vehicle', id)
    const result = {
      vehicle: id,
      locations: currentLocation
    }

    return result
  }
}
