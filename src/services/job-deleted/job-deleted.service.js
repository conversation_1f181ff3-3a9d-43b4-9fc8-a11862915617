// Initializes the `job-deleted` service on path `/job-deleted`
const { JobDeleted } = require('./job-deleted.class')
const createModel = require('../../models/job-deleted.model')
const hooks = require('./job-deleted.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/job-deleted', new JobDeleted(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('job-deleted')

  service.hooks(hooks)
}
