const logger = require('../hooks/logger')

const initData = async (app) => {
  let role = null
  let rolesAcl = null

  try {
    const [setting] = await app.service('system-settings').find({
      paginate: false
    })

    const data = {
      trackingDistance: 100,
      jobReminder: {
        days: 0,
        hours: 1,
        minutes: 0
      },
      jobCancel: {
        days: 4,
        hours: 0,
        minutes: 0
      },
      graceSb: {
        days: 0,
        hours: 1,
        minutes: 0
      },
      multiReminderInterval: {
        days: 0,
        hours: 1,
        minutes: 0
      },
      trackingInterval: 10000,
      allowMultipleReminder: true,
      mapAutoRefreshInterval: 10,
      radiusDistance: 500,
      dataArchive: [
        {
          tableName: 'jobs',
          dataRetention: 380,
          fileRetention: 3
        },
        {
          tableName: 'trips',
          dataRetention: 380,
          fileRetention: 3
        },
        {
          tableName: 'connectionhistories',
          dataRetention: 380,
          fileRetention: 3
        },
        {
          tableName: 'conversations',
          dataRetention: 380,
          fileRetention: 3
        },
        {
          tableName: 'deliveryrecords',
          dataRetention: 380,
          fileRetention: 3
        },
        {
          tableName: 'activitylogs',
          dataRetention: 40,
          fileRetention: 3
        },
        {
          tableName: 'cdilogs',
          dataRetention: 40,
          fileRetention: 3
        },
        {
          tableName: 'eventlogs',
          dataRetention: 40,
          fileRetention: 3
        },
        {
          tableName: 'messagehistories',
          dataRetention: 60,
          fileRetention: 3
        },
        {
          tableName: 'messages_archive_data',
          dataRetention: 60,
          fileRetention: 3
        }
      ]
    }

    if (setting) {
      const requiredKeys = ['trackingDistance', 'jobReminder', 'jobCancel', 'multiReminderInterval', 'trackingInterval', 'dataArchive']
      const obj = {}

      requiredKeys.forEach(v => {
        if (typeof setting[v] === 'undefined') obj[v] = data[v]
      })

      if (Object.keys(obj).length) {
        await app.service('system-settings').patch(setting._id, obj)
      }
    } else {
      await app.service('system-settings').create(data)
    }

    // init modules data
    if ((await app.service('master-modules').Model.countDocuments()) === 0) {
      await app
        .service('master-modules')
        .create([
          { name: 'Messaging' },
          { name: 'Vehicle Tracking' },
          { name: 'Jobs' },
          { name: 'Message Broadcasts' },
          { name: 'Broadcasts Template' },
          { name: 'License Types' },
          { name: 'Company Subscriptions' },
          { name: 'Vehicle Subscriptions' },
          { name: 'Vehicle Management' },
          { name: 'Vehicle Groups' },
          { name: 'Messaging Journey' },
          { name: 'Vehicle Journey' },
          { name: 'User Activities' },
          { name: 'Groups' },
          { name: 'Companies' },
          { name: 'CDAS Users' },
          { name: 'Organization Users' },
          { name: 'Role Modules' },
          { name: 'Organization Types' },
          { name: 'System Settings' },
          { name: 'Articles' }
        ])
    }

    if ((await app.service('roles-acl').Model.countDocuments()) === 0) {
      rolesAcl = await app.service('roles-acl').create([
        {
          forCdas: true,
          name: 'Administrator',
          modules: [
            {
              allowCreate: false,
              allowRead: true,
              allowUpdate: null,
              allowDelete: null,
              name: 'Messaging'
            },
            {
              allowCreate: null,
              allowRead: true,
              allowUpdate: null,
              allowDelete: null,
              name: 'Vehicle Tracking'
            },
            {
              allowCreate: null,
              allowRead: true,
              allowUpdate: null,
              allowDelete: null,
              name: 'Jobs'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Broadcasts Template'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Message Broadcasts'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Company Subscriptions'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'License Types'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Vehicle Groups'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Vehicle Subscriptions'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Vehicle Management'
            },
            {
              allowCreate: null,
              allowRead: true,
              allowUpdate: null,
              allowDelete: null,
              name: 'User Activities'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Companies'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'CDAS Users'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Groups'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Messaging Journey'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Vehicle Journey'
            },
            {
              allowCreate: null,
              allowRead: true,
              allowUpdate: true,
              allowDelete: null,
              name: 'System Settings'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Organization Types'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Role Modules'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Organization Users'
            },
            {
              allowCreate: true,
              allowRead: true,
              allowUpdate: true,
              allowDelete: true,
              name: 'Articles'
            }
          ]
        },
        {
          name: 'Transporter - Controller',
          forCdas: false,
          modules: []
        },
        {
          name: 'Transporter - Driver',
          forCdas: false,
          modules: []
        }
      ])

      // init role data
      if (await app.service('roles').Model.countDocuments() === 0) {
        const administrators = await rolesAcl.filter(v => v.forCdas).map(v => v._id)
        const transporters = await rolesAcl.filter(v => !v.forCdas).map(v => v._id)

        role = await app.service('roles').create([
          {
            name: 'Administrator',
            rolesAcl: administrators
          },
          {
            name: 'Transporter',
            rolesAcl: transporters
          }
        ])
      }

      // init users data
      if ((await app.service('users').Model.countDocuments()) === 0) {
        role = role.filter((v) => v.name === 'Administrator')[0]
        rolesAcl = rolesAcl.filter((v) => v.name === 'Administrator')[0]

        await app.service('users').create({
          status: true,
          preferredComm: 'email',
          username: 'admin',
          email: '<EMAIL>',
          mobile: '+6581234555666',
          fullname: 'CDAS Aministrator',
          password: 'admin123',
          role: role._id,
          roleAcl: rolesAcl._id
        })
      }
    }

    // checking vehicle-key
    const vehicleKeys = await app.redis.HGETALL(`${app.get('vehicleKeyName')}`)

    if (!vehicleKeys || Object.keys(vehicleKeys).length === 0) {
      await app.service('api-keys').create({
        apiKeyName: app.get('vehicleKeyName'),
        headerKey: 'apikey',
        status: 'active'
      })
    }

    // checking sb-key
    const sbKeys = await app.redis.HGETALL(`${app.get('sb').apiKeyName}`)

    if (!sbKeys || Object.keys(sbKeys).length === 0) {
      await app.service('api-keys').create({
        apiKeyName: app.get('sb').apiKeyName,
        headerKey: 'apikey',
        status: 'active'
      })
    }

    // init data archive
    const dataArchive = await app.redis.keys('dataArchive')

    if (!dataArchive || Object.keys(dataArchive).length === 0) {
      await app.redis.HSET('dataArchive', 'status', 'false')
    }

    if ((await app.service('licences').Model.countDocuments()) === 0) {
      await app
        .service('licences')
        .create({
          status: true,
          name: 'PSA PortNet Licence',
          tag: true
        })
    }

    if ((await app.service('system-settings').Model.countDocuments()) === 0) {
      await app
        .service('system-settings')
        .create({

        })
    }

    app.initRoleAcl = await app.service('roles-acl').find({ query: { name: 'Transporter - Driver', $select: '_id' }, $limit: 1 }).then(res => res.data[0]._id)
  } catch (err) {
    logger.error('Oops! something went wrong ', err)
  }
}

const populateAdminEmail = async (app) => {
  let cdasAdmin = await app.service('users').find({ paginate: false, query: { group: { $exists: false }, company: { $exists: false }, $select: ['email'] } })

  cdasAdmin = cdasAdmin.map(e => e.email).join(',')

  app.set('cdasAdminEmail', cdasAdmin)
}

const initGateLocsData = async (app) => {
  try {
    const gateLocs = await app.service('gate-locs').find({ paginate: false })

    const gateLocsData = [
      {
        gate: 'TTATT',
        linkgates: [],
        lat: 1.2664817,
        long: 103.6155435,
        zoneradius: 3,
        insidezonetime: 10,
        outsidezonetime: 40,
        remark: ''
      },
      {
        gate: 'TTABT',
        linkgates: ['TTAPPT'],
        lat: 1.266271,
        long: 103.826969,
        zoneradius: 3,
        insidezonetime: 10,
        outsidezonetime: 40,
        remark: ''
      },
      {
        gate: 'TTAPPT',
        linkgates: ['TTABT'],
        lat: 1.283354,
        long: 103.777274,
        zoneradius: 3,
        insidezonetime: 10,
        outsidezonetime: 40,
        remark: ''
      }
    ]

    for (const gateLoc of gateLocsData) {
      const existingGate = gateLocs.find(gate => gate.gate === gateLoc.gate)
      if (!existingGate) {
        await app.service('gate-locs').create(gateLoc)
      }
    }
  } catch (error) {
    logger.error(`Error when initializing gate locs data: ${error}`)
  }
}

const users = require('./users/users.service.js')
const channelMembership = require('./channel-membership/channel-membership.service.js')
const sender = require('./sender/sender.service.js')
const otp = require('./otp/otp.service.js')
const activityLogs = require('./activity-logs/activity-logs.service.js')
const groups = require('./groups/groups.service.js')
const vehicles = require('./vehicles/vehicles.service.js')
const vehicleTracking = require('./vehicle-tracking/vehicle-tracking.service.js')
const roles = require('./roles/roles.service.js')
const jobs = require('./jobs/jobs.service.js')
const messages = require('./messages/messages.service.js')
const conversations = require('./conversations/conversations.service.js')
const vehicleDrivers = require('./vehicle-drivers/vehicle-drivers.service.js')
const licences = require('./licences/licences.service.js')
const vehicleGroups = require('./vehicle-groups/vehicle-groups.service.js')
const subscriptions = require('./subscriptions/subscriptions.service.js')
const psaMessaging = require('./psa-messaging/psa-messaging.service.js')
const trips = require('./trips/trips.service.js')
const articles = require('./articles/articles.service.js')
const eventLogs = require('./event-logs/event-logs.service.js')
const rolesAcl = require('./roles-acl/roles-acl.service.js')
const masterModules = require('./master-modules/master-modules.service.js')
const systemSettings = require('./system-settings/system-settings.service.js')
const notifications = require('./notifications/notifications.service.js')
const depots = require('./depots/depots.service.js')
const cdiParticipants = require('./cdi-participants/cdi-participants.service.js')
const importUsers = require('./import-users/import-users.service.js')
const getFiles = require('./get-files/get-files.service.js')
const importMultipleVehicles = require('./import-multiple-vehicles/import-multiple-vehicles.service.js')
const adminBroadcasts = require('./admin-broadcasts/admin-broadcasts.service.js')
const tokens = require('./tokens/tokens.service.js')
const apiKeys = require('./api-keys/api-keys.service.js')

const sbBookings = require('./sb-bookings/sb-bookings.service.js')
const smartBookings = require('./smartbookings/smartbookings.service.js')
const ping = require('./ping/ping.service.js')
const connectionHistories = require('./connection-histories/connection-histories.service.js')
const pong = require('./pong/pong.service.js')
const aggregate = require('./aggregate/aggregate.service.js')
const trailerManagement = require('./trailer-management/trailer-management.service.js')
const trailerMaster = require('./trailer-master/trailer-master.service.js')
const trailerConfig = require('./trailer-config/trailer-config.service.js')
const dataInit = require('./dataInitToCache/dataInitToCache.js')
const sbEntryexit = require('./sb-entryexit/sb-entryexit.service.js')
const deliveryRecord = require('./delivery-record/delivery-record.service.js')
const usersOnline = require('./users-online/users-online.service.js')
const messageHistories = require('./message-histories/message-histories.service.js')
const company = require('./company/company.service.js')
const validateToken = require('./validate-token/validate-token.service.js')
const importTrailerMaster = require('./import-trailer-master/import-trailer-master.service.js')
const emailNotificationSettings = require('./email-notification-settings/email-notification-settings.service.js')
const customNotification = require('./custom-notification/custom-notification.service.js')
const usersRestricted = require('./users-restricted/users-restricted.service.js')
const dispatchMovement = require('./dispatch-movement/dispatch-movement.service.js')
const logExternalRequest = require('./log-external-request/log-external-request.service.js')
const cancelDispatchedMovement = require('./cancel-dispatched-movement/cancel-dispatched-movement.service.js')
const updateDestination = require('./update-destination/update-destination.service.js')
const optetruck = require('./optetruck/optetruck.service.js')
const jobDeleted = require('./job-deleted/job-deleted.service.js')
const jobCombine = require('./job-combine/job-combine.service.js')
const jobCounter = require('./job-counter/job-counter.service.js')
const ttaDocs = require('./tta-docs/tta-docs.service.js')
const gateLocs = require('./gate-locs/gate-locs.service.js')
const ttaMsgQueues = require('./tta-msg-queues/tta-msg-queues.service.js')
const dispatchMovementTms = require('./dispatch-movement-tms/dispatch-movement-tms.service.js')
const cancelDispatchedMovementTms = require('./cancel-dispatched-movement-tms/cancel-dispatched-movement-tms.service.js')
const updateDestinationTms = require('./update-destination-tms/update-destination-tms.service.js')

// eslint-disable-next-line no-unused-vars
module.exports = function (app) {
  app.configure(roles)
  app.configure(users)
  app.configure(channelMembership)
  app.configure(sender)
  app.configure(otp)
  app.configure(activityLogs)
  app.configure(groups)
  app.configure(vehicles)
  app.configure(vehicleTracking)
  app.configure(jobs)
  app.configure(messages)
  app.configure(conversations)
  app.configure(vehicleDrivers)
  app.configure(licences)
  app.configure(vehicleGroups)
  app.configure(subscriptions)
  app.configure(psaMessaging)
  app.configure(trips)
  app.configure(articles)
  app.configure(eventLogs)
  app.configure(rolesAcl)
  app.configure(masterModules)
  app.configure(systemSettings)
  app.configure(notifications)
  app.configure(initData)
  app.configure(depots)
  app.configure(cdiParticipants)
  app.configure(importUsers)
  app.configure(getFiles)
  app.configure(importMultipleVehicles)
  app.configure(adminBroadcasts)
  app.configure(tokens)
  app.configure(sbBookings)
  app.configure(smartBookings)
  app.configure(ping)
  app.configure(connectionHistories)
  app.configure(pong)
  app.configure(apiKeys)
  app.configure(sbEntryexit)
  app.configure(aggregate)
  app.configure(populateAdminEmail)
  app.configure(deliveryRecord)
  app.configure(trailerMaster)
  app.configure(trailerConfig)
  app.configure(dataInit)
  app.configure(usersOnline)
  app.configure(trailerManagement)
  app.configure(messageHistories)
  app.configure(company)
  app.configure(validateToken)
  app.configure(importTrailerMaster)
  app.configure(emailNotificationSettings)
  app.configure(customNotification)
  app.configure(usersRestricted)
  app.configure(dispatchMovement)
  app.configure(logExternalRequest)
  app.configure(cancelDispatchedMovement)
  app.configure(updateDestination)
  app.configure(optetruck)
  app.configure(jobDeleted)
  app.configure(jobCombine)
  app.configure(jobCounter)
  app.configure(ttaDocs)
  app.configure(gateLocs)
  app.configure(ttaMsgQueues)
  app.configure(initGateLocsData)
  app.configure(dispatchMovementTms)
  app.configure(cancelDispatchedMovementTms)
  app.configure(updateDestinationTms)
}
