// Initializes the `master-modules` service on path `/master-modules`
const { MasterModules } = require('./master-modules.class')
const createModel = require('../../models/master-modules.model')
const hooks = require('./master-modules.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$options', '$regex', '$exists'],
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/master-modules', new MasterModules(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('master-modules')

  service.hooks(hooks)
}
