// Initializes the `tokens` service on path `/tokens`
const { Tokens } = require('./tokens.class')
const createModel = require('../../models/tokens.model')
const hooks = require('./tokens.hooks')

const jwt = require('jsonwebtoken')

module.exports = function (app) {
  const validateRefreshToken = (req, res, next) => {
    const tokens = app.service('tokens')
    const users = app.service('users')

    const auth = req.headers.authorization || ''
    const query = {
      refreshToken: auth.includes('Bearer') ? auth.split(' ')[1] : auth
    }

    tokens
      .find({ query })
      .then(result => {
        if (result.total <= 0) {
          next(new Error('Refresh Token not found'))
        } else {
          req.tokenId = result.data[0]._id
          users
            .get(result.data[0].user)
            .then(result => {
              if (result.length <= 0) {
                next(new Error('User not found'))
              } else {
                req.userId = result._id
                next()
              }
            }).catch(app.debug)
        }
      }).catch(app.debug)
  }

  const generateAccessToken = (req, res, next) => {
    jwt
      .sign({ sub: req.userId }, app.get('authentication').secret, app.get('authentication').jwtOptions, (err, token) => {
        if (err) next(err)
        req.token = token
        next()
      })
  }

  const revokeRefreshToken = async (req, res, next) => {
    const tokens = app.service('tokens')

    const auth = req.headers.authorization || ''
    const query = {
      refreshToken: auth.includes('Bearer') ? auth.split(' ')[1] : auth
    }

    if (req.headers.fcm) {
      const user = await app.service('users').get(req.userId)

      const devices = user.device.filter(val => {
        return val.fcmToken !== req.headers.fcm
      })

      app.service('users').patch(req.userId, {
        device: devices
      })
    }

    tokens
      .remove(null, { query })
      .then(() => {
        next()
      })
      .catch(err => {
        next(err)
      })
  }

  app.post('/token/refresh', validateRefreshToken, generateAccessToken, (req, res) => {
    res.status(200).json({ accessToken: req.token })
  })

  app.post('/token/revoke', validateRefreshToken, revokeRefreshToken, (req, res) => {
    res.status(200).json({ message: 'Token successfully revoked' })
  })

  const Model = createModel(app)
  const paginate = app.get('paginate')

  const options = {
    Model,
    paginate,
    multi: ['remove']
  }

  // Initialize our service with any options it requires
  app.use('/tokens', new Tokens(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('tokens')

  service.hooks(hooks)
}
