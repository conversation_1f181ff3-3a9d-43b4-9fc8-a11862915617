const { BadRequest } = require('@feathersjs/errors')
const ExcelJS = require('exceljs')
const ejs = require('ejs')
const { Readable } = require('stream')
const fs = require('fs')
const AdmZip = require('adm-zip')
const { promisify } = require('util')
const renderFile = promisify(ejs.renderFile)
const { S3Client, GetObjectCommand, DeleteObjectCommand, PutObjectCommand } = require('@aws-sdk/client-s3')

const zip = new AdmZip()

const s3 = new S3Client({
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  },
  region: 'ap-southeast-1'
})
const logger = require('@hooks/logger')

exports.ImportVehicles = class ImportVehicles {
  constructor (options, app) {
    this.options = options || {}
    this.app = app
  }

  async get (id, params) {
    const { group } = params.query
    if (id === 'getTemplate') {
      if (!group) throw new BadRequest('Group NotFound')

      if (group instanceof Array) {
        const totalgroup = await this.app.service('groups').find({ query: { level: 1 } }).then(res => res.total)
        const orgTypeTrans = await this.app.service('roles').find({ query: { name: 'Transporter' } }).then(res => res.data)
        let children = await this.app.service('groups').find({ query: { level: 2, parent: { $in: group }, organizationType: orgTypeTrans[0]._id } }).then(res => res.data)

        children = children.map(e => String(e.parent)).map((e, i, final) => final.indexOf(e) === i && i).filter((e) => children[e]).map(e => children[e].parent)

        logger.debug('total group ', children.length)
        // if (total !== group.length) throw new Error('Some of group not exists!')

        const preValue = []
        const type = (totalgroup === group.length) ? 'all' : ''
        let filenames = []

        filenames = await Promise.all(children.map(g => {
          return this.generateTemplate(g, preValue, type, orgTypeTrans[0]._id)
        }))

        if (type === 'all') {
          filenames = await this.zipAllFiles()
        }

        return filenames
      } else {
        const res = await this.app.service('groups').get(group).catch(e => {
          throw new Error('Group not exists!')
        })

        return res
      }
    } else throw new BadRequest('Id NotFound')
  }

  async create (data, params) {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)))
    }

    return data
  }

  async generateTemplate (groupId, preValue = [], type, orgType = null) {
    try {
      // get the datas and prepare data
      const group = await this.app.service('groups').get(groupId)
      let companies = await this.app.service('groups').find({ query: { level: 2, parent: groupId, organizationType: orgType ? { $in: orgType } : { $exists: true } } })

      companies = companies.data

      const companiesName = companies.map(v => { return v.name })

      // prepare excel
      const filename = `${group.name.split(' ').join('-')} - Vehicles-Import.xlsx`
      // const filename = 'template-vehicles.xlsx';

      const workbook = new ExcelJS.Workbook()

      workbook.creator = 'Rejing'
      workbook.created = new Date()

      const ws = workbook.addWorksheet('Import Vehicles')
      let row

      // Style sheet
      const formatAlignCenter = { vertical: 'middle', horizontal: 'center', wrapText: true }
      const formatTextBold = { size: 12, bold: true }

      ws.getColumn(1).width = 5
      ws.getColumn(2).width = 25
      ws.getColumn(3).width = 15
      ws.getColumn(4).width = 15

      ws.addRow([groupId])
      ws.getRow(1).hidden = true

      // START SET HEADER
      row = ws.addRow([
        'S/N',
        'Vehicles Number*',
        'Company*',
        'Vehicle Status*'
      ])

      const curRow = row._number

      ws.getCell(`B${curRow}`).value = {
        richText: [
          { font: { size: 12, bold: true }, text: 'Vehicles Number*' },
          { font: { size: 9 }, text: '\rExample: XD1234A' }
        ]
      }
      // _______ size auto

      let indxRow = 'A'

      ws.columns.forEach((column, index) => {
        const wert = ws.getCell(`${indxRow}${curRow}`)
        if (typeof wert.value === 'object') {
          if (wert.value && typeof wert.value.richText !== 'undefined') {
            let sizeWidth = 12
            wert.value.richText.forEach((x, index) => {
              if (typeof x.text !== 'undefined' && x.text) {
                if (sizeWidth < x.text.length) {
                  sizeWidth = x.text.length
                }
              }
            })
            if (column.width < sizeWidth) {
              column.width = sizeWidth
            }
          }
        } else if (typeof wert.value === 'string') {
          if (column.width < wert.value.length) {
            column.width = wert.value.length
          }
        }

        indxRow = String.fromCharCode(indxRow.charCodeAt(0) + 1)
      })
      // _______ ./size auto

      row.getCell(1).alignment = formatAlignCenter
      row.getCell(2).alignment = formatAlignCenter
      row.getCell(3).alignment = formatAlignCenter
      row.getCell(4).alignment = formatAlignCenter

      for (let i = 1; i <= 13; i++) {
        row.getCell(i).font = formatTextBold
      }
      // END SET HEADER

      // SET BODY TABLE
      if (preValue.length === 0) {
        row = ws.addRow([])
        row.getCell('B').numFmt = '@'
        row.getCell('C').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${companiesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('D').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"Active, Inactive"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
      }

      preValue.forEach(el => {
        row = ws.addRow(el)
        row.getCell('B').numFmt = '@'
        row.getCell('C').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${companiesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('D').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"Active, Inactive"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
      })

      // repeat logic Body
      for (let i = 0; i < 500; i++) {
        row = ws.addRow([])
        row.getCell('B').numFmt = '@'
        row.getCell('C').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${companiesName.join()}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
        row.getCell('D').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"Active, Inactive"'],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Input Error',
          error: 'Invalid value for this input'
        }
      }
      // End set body

      if (type === 'all') {
        await workbook.xlsx.writeFile(`${this.app.get('public')}/import-vehicles/${filename}`)
      } else {
        // write file and upload to local public
        // await workbook.xlsx.writeFile(`${this.app.get('public')}/import-vehicles/${filename}`);

        // write file and upload to aws s3
        const buffer = await workbook.xlsx.writeBuffer()
        const readable = new Readable()

        readable._read = () => {} // _read is required but you can noop it
        readable.push(buffer)
        readable.push(null)

        // prosess upload to s3
        const uploadParams = { Bucket: this.app.get('storageBucket'), Key: '', Body: readable }
        uploadParams.Key = `template/${filename}`

        await new Promise((resolve, reject) => {
          return s3.send(new PutObjectCommand(uploadParams), function (err, data) {
            if (err) {
              logger.error('Oops! something wrong with s3 ', err)
              reject(err)
            }

            if (data) {
              logger.debug('success upload to S3 ')
              resolve(data)
            }
          })
        })
      }

      return filename
    } catch (e) {
      // statements
      logger.error('Oops! something wrong', e)
    }
  }

  async readTemplate (filename) {
    try {
      // prepare before import data Vehicles
      const collectedVehicles = []; const collectedVehicleNumber = []
      const companiesMap = {}

      const workbook = new ExcelJS.Workbook()

      // get data from s3
      const params = { Bucket: this.app.get('storageBucket'), Key: `import/${filename}` }
      const s3Response = await s3.send(new GetObjectCommand(params))
      await workbook.xlsx.read(s3Response.Body)

      const ws = workbook.getWorksheet(1)
      const companies = await this.app.service('groups').find({ paginate: false, query: { level: 2 } })

      companies.forEach(val => {
        companiesMap[val.name] = val
      })

      let groupId

      ws.eachRow(async (row, rowNumber) => {
        const { values } = row

        if (rowNumber > 2) {
          collectedVehicles.push(values)

          if (values[2]) collectedVehicleNumber.push(values[2].toUpperCase())
        } if (rowNumber === 1) {
          groupId = values[1].replace(/[^a-zA-Z0-9]/g, '')
        }
      })

      const importResult = await this.startImport({
        rawData: collectedVehicles,
        companiesMap,
        collectedVehicleNumber
      })

      const group = await this.app.service('groups').get(groupId, { paginate: false })
      let failedFilename

      if (importResult.invalidVehicle.length > 0) failedFilename = await this.generateTemplate(groupId, importResult.invalidVehicle)

      return {
        group: group.name,
        valid: importResult.validVehicle.length,
        invalid: importResult.invalidVehicle.length,
        filename: failedFilename || null
      }
    } catch (e) {
      // statements
      logger.error('Oops! something wrong', e)
    }
  }

  async startImport ({ rawData, companiesMap, collectedVehicleNumber }) {
    try {
      const existVehicle = (await this.app.service('vehicles').find({
        paginate: false,
        query: {
          $limit: collectedVehicleNumber.length,
          vehicleNo: {
            $in: collectedVehicleNumber
          },
          $populate: {
            path: 'company', $select: ['name']
          }
        }
      })).map(u => {
        u = { vehicleNo: u.vehicleNo, status: u.status ? 'Active' : 'Inactive', company: u.company.name }

        return u
      })

      const validVehicle = []
      const invalidVehicle = []
      rawData.forEach((values) => {
        let validation = []
        let isValid = true

        // start validate data
        const vehicleNumber = (values[2]) ? values[2].toUpperCase() : ''
        const vehicleNumberLength = vehicleNumber.length

        if (vehicleNumberLength > 7) {
          validation.push('- Vehicle Number too long')
        } else if (vehicleNumberLength < 4) {
          validation.push('- Vehicle Number minimum 4 character')
        } else if (!isNaN(vehicleNumber.substring(0, 1)) || !isNaN(vehicleNumber.substring(1, 2)) || !isNaN(vehicleNumber.substr(vehicleNumber.length - 1))) {
          validation.push('- Format is cc<number>c, where (c) is an alphabet character and <number> is a number up to 4 digits max.')
        } else if (isNaN(vehicleNumber.substring(2, vehicleNumberLength - 1)) === true) {
          validation.push('- Format is cc<number>c, where (c) is an alphabet character and <number> is a number up to 4 digits max.')
        }

        // check null data
        if (!vehicleNumber) validation.push('- Vehicle number is required')

        if (!values[3]) validation.push('- Company is required')

        if (!values[4]) validation.push('- Vehicle status is required')
        // end validate data

        // check exsis data
        if (existVehicle.length > 0) {
          existVehicle.forEach(v => {
            if (this.checkUndefined(v.vehicleNo) && this.checkUndefined(vehicleNumber) && this.checkUndefined(v.company) && this.checkUndefined(values[3]) && (this.checkUndefined(v.status) && (v.status !== null || v.status !== 'null')) && (this.checkUndefined(values[4]) && (values[4] !== null || values[4] !== 'null')) && v.vehicleNo.toUpperCase() === vehicleNumber) {
              if (String(v.company) === String(values[3])) {
                validation.push('- Vehicle number already exists in this company')
                isValid = false
              }

              if (String(v.company) !== String(values[3]) && values[4] === 'Active' && v.status === 'Active') {
                validation.push('- Vehicle number already exists in another company')
                isValid = false
              }
            }
          })
        }

        // push to exists data variable
        if (isValid && vehicleNumber && values[3] && (this.checkUndefined(values[4]) && (values[4] !== null || values[4] !== 'null'))) existVehicle.push({ vehicleNo: vehicleNumber, company: values[3], status: values[4] })
        // end push to exists data variable

        if (validation.length > 0) {
          if (!values[4]) values[4] = null

          validation = validation.join('\n')

          values.push(validation)
          invalidVehicle.push(values)
        } else {
          const vehicle = {
            status: (values[4] === 'Active'),
            vehicleNo: vehicleNumber,
            group: companiesMap[values[3]].parent,
            company: companiesMap[values[3]]._id
            // driver: ,
            // createdBy: ,
            // updatedBy:
          }

          validVehicle.push(vehicle)

          try {
            this.app.service('vehicles').create(vehicle)
          } catch (e) {
            logger.error('Oops! something wrong', e)
          }
        }
      })
      return {
        validVehicle,
        invalidVehicle
      }
    } catch (e) {
      logger.error('Oops! something wrong', e)
    }
  }

  async prepareImport (filenames, user) {
    const fileUpload = []
    const promises = filenames.map(f => {
      fileUpload.push(f)
      return this.readTemplate(f)
    })

    try {
      const allImport = await Promise.all(promises)

      logger.debug(`allImport: ${JSON.stringify(allImport)}`)

      const attachments = []

      allImport.forEach(async val => {
        if (val.filename) {
          const params = { Bucket: this.app.get('storageBucket'), Key: `template/${val.filename}` }
          try {
            const s3Response = await s3.send(new GetObjectCommand(params))
            attachments.push({ filename: val.filename, content: s3Response.Body })
          } catch (e) {
            logger.error('Oops! something wrong ', e)
          }
        }
      })

      renderFile(`${this.app.get('template').email}/upload-vehicles-success.ejs`, {
        subject: 'Import Vehicles Result',
        name: user.fullname,
        result: allImport
      }).then((html) => {
        const payloadEmail = {
          from: this.app.get('smtp').sender,
          to: user.email,
          subject: 'Import Vehicles Result',
          html
        }

        if (attachments.length > 0 && attachments[0].filename) payloadEmail.attachments = attachments

        logger.debug('sender: ', this.app.get('smtp').sender)
        this.app
          .service('mailer')
          .create(payloadEmail)

        setTimeout(() => {
          allImport.forEach(val => {
            if (val.filename) {
              this.deleteFileAWS(`template/${val.filename}`)
            }
          })
          fileUpload.forEach(el => {
            if (el) {
              this.deleteFileAWS(`import/${el}`)
            }
          })
        }, 5000)
      }).catch(e => logger.error('Oops! something wrong ', e))
    } catch (e) {
      logger.error('Oops! something wrong ', e)
    }
  }

  async deleteFileAWS (s) {
    const deleteParam = {
      Bucket: this.app.get('storageBucket'),
      Key: s
    }

    try {
      await s3.send(new DeleteObjectCommand(deleteParam))
      logger.debug('success delete file s3')
    } catch (e) {
      logger.error('Oops! something wrong ', e)
    }
  }

  async zipAllFiles () {
    try {
      const pathTemplate = './public/import-vehicles/'
      const pathZip = './public/'

      if (fs.existsSync(pathTemplate)) {
        // Robust directory creation with error handling
        try {
          if (!fs.existsSync(pathZip)) fs.mkdirSync(pathZip, { recursive: true })
        } catch (err) {
          logger.error('Oops! something wrong ', err)
        }

        const zipFileName = 'template-vehicles.zip'
        const files = fs.readdirSync(pathTemplate)

        for (let j = 0; j < files.length; j++) {
          const file = files[j]

          if (/^.*.xls.*$/.test(file)) {
            const fileName = pathTemplate + file

            await zip.addLocalFile(fileName)

            try {
              fs.unlinkSync(fileName)
              logger.info(`File ${fileName} deleted successfully`)
            } catch (e) {
              logger.error(`Error occur on remove ${fileName}`, e)
            }
          }
        }

        zip.toBuffer()
        zip.writeZip(pathZip + zipFileName, async () => {
          logger.info('Zip file succesfully created')

          const files = fs.readdirSync(pathZip)

          if (files.length) {
            const uploadPromises = []
            for (let i = 0; i < files.length; i++) {
              const file = files[i]

              if (file.includes('.zip')) {
                const buffer = fs.readFileSync(pathZip + file, null)
                const uploadParams = {
                  Bucket: this.app.get('storageBucket'),
                  Key: `template/${zipFileName}`,
                  Body: buffer,
                  ContentType: 'application/zip'
                }
                uploadPromises.push((async () => {
                  try {
                    await s3.send(new PutObjectCommand(uploadParams))
                    logger.info('File uploaded successfully at s3')
                    try {
                      fs.unlinkSync(pathZip + file)
                      logger.info(`File ${pathZip + file} deleted successfully`)
                    } catch (e) {
                      logger.error(`Error occur on remove ${pathZip + file}`, e)
                    }
                  } catch (e) {
                    logger.error('Oops! something wrong ', e)
                  }
                })())
              }
            }
            await Promise.all(uploadPromises)
          }
        })

        return [zipFileName]
      }
    } catch (e) {
      logger.error('Error occur on zipAllFiles', e)
    }
  }

  checkUndefined (val) {
    return typeof val !== 'undefined'
  }
}
