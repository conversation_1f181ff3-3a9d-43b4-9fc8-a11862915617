// Initializes the `import-multiple-vehicles` service on path `/import-multiple-vehicles`
const { ImportVehicles } = require('./import-multiple-vehicles.class.js')
const hooks = require('./import-multiple-vehicles.hooks')
const multer = require('multer')
const multerSharp = require('@multigunagemilang/multer-sharp-addon')
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3')
const fs = require('fs')

const s3 = new S3Client({
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  },
  region: 'ap-southeast-1'
})

module.exports = function (app) {
  const confStorage = {
    destination: (_req, _file, cb) => cb(null, `${app.get('import')}/vehicles/`), // where the files are being stored
    filename: (_req, file, cb) => cb(null, `${Date.now()}-${file.originalname}`) // getting the file name
  }

  const storage = multerSharp(confStorage)
  const upload = multer({ storage })

  const options = {
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/import-multiple-vehicles',
    new ImportVehicles(options, app),
    upload.array('files'), async (req, _res, next) => {
      const { method } = req

      if (method === 'POST' || method === 'PATCH') {
        req.feathers.files = req.files // transfer the received files to feathers

        const files = req.files.map(f => {
          return f.filename
        })

        // Use Promise.all to upload all files in parallel and collect results
        const uploadResults = await Promise.all(req.files.map(async (v) => {
          const filename = v.filename
          const path = `${app.get('import')}/vehicles/${filename}`
          const params = {
            Bucket: app.get('storageBucket'),
            Key: `import/${filename}`,
            Body: fs.createReadStream(path)
          }

          try {
            await s3.send(new PutObjectCommand(params))

            return { filename, success: true }
          } catch (err) {
            app.get('logger').error('S3 upload failed:', err)

            return { filename, success: false, error: err.message }
          }
        }))

        const failedUploads = uploadResults.filter(r => !r.success)

        if (failedUploads.length > 0) {
          _res.json({
            status: 'partial',
            message: 'Some files failed to upload.',
            failed: failedUploads.map(f => ({ filename: f.filename, error: f.error }))
          })
        } else {
          app.service('import-multiple-vehicles').prepareImport(files, req.res.hook.params.user)
          _res.json({
            status: 'success',
            message: 'All files uploaded. We\'re processing your file(s) now. We will notify you the result after process have done.'
          })
        }
      } else {
        next()
      }
    })

  // Get our initialized service so that we can register hooks
  const service = app.service('import-multiple-vehicles')

  service.hooks(hooks)
}
