// Initializes the `tta-docs` service on path `/tta-docs`
const { TtaDocs } = require('./tta-docs.class')
const createModel = require('../../models/tta-docs.model')
const hooks = require('./tta-docs.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
    whitelist: ['$text', '$search', '$regex', '$exists', '$options', '$populate'],
    multi: true
  }

  // Initialize our service with any options it requires
  app.use('/tta-docs', new TtaDocs(options, app))

  // Add new endpoint to call processBatch
  app.post('/process-tta', async (req, res) => {
    const ttaDocsService = app.service('tta-docs')
    await ttaDocsService.processBatch()
    res.send('Batch processing completed')
  })

  // Get our initialized service so that we can register hooks
  const service = app.service('tta-docs')

  service.hooks(hooks)
}
