// Initializes the `depots` service on path `/depots`
const { Depots } = require('./depots.class')
const createModel = require('../../models/depots.model')
const hooks = require('./depots.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/depots', new Depots(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('depots')

  service.hooks(hooks)
}
