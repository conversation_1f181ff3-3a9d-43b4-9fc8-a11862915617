/* eslint-disable no-unused-vars */
const { Service } = require('feathers-mongoose')
const _ = require('lodash')
const moment = require('moment')
const logger = require('@hooks/logger')

exports.PsaMessaging = class PsaMessaging extends Service {
  constructor (options, app) {
    super(options)
    this.app = app
  }

  async create (data) {
    const messageType = data.type
    const messageDirection = data.direction
    const messageText = data.text
    const outHdr = data.out_hdr
    const inHdr = data.in_hdr
    const outTrip = data.out_trip
    const inTrip = data.in_trip
    const inYard = data.in_yard
    const jobTrip = data.job
    const jobServiceAvb = data.job_service_available
    let companyId = null
    // let flagBatch = false; // for flag batch project or

    // get vehicle data
    let getVehicleData = await this.app.service('vehicles').find({
      query: {
        vehicleNo: outHdr.destination
      },
      paginate: false
    })
    if (getVehicleData.length > 1) {
      getVehicleData = await this.app.service('vehicles').find({
        query: {
          $limit: 1,
          vehicleNo: outHdr.destination,
          status: true
        },
        paginate: false
      })
    }

    // action for trip
    if (['MDT*', 'CDAS*', 'CTR*'].includes(outHdr.destination)) {
      this.sendUrgentBroadcast(data)
    } else if (messageType === 'trip') {
      // action for out trip
      if (messageDirection === 'out' && outHdr !== null && outTrip !== null) {
        const tripData = {
          outHdr,
          tripId: outTrip.Trip_Id,
          pmNo: outTrip.PM_No,
          status: outTrip.TripSt,
          c1: outTrip.C1,
          s1: outTrip.S1,
          c2: outTrip.C2,
          s2: outTrip.S2
        }

        // temporary for test batch number Job
        // if (outTrip.C1 === outTrip.C2) {
        //   flagBatch = true;
        // }

        // trip status = S or W or A
        if (tripData.status === 'S' || tripData.status === 'W' || tripData.status === 'A') {
          // block for send data to message service
          // get vehicle data
          if (getVehicleData.length > 0) {
            companyId = getVehicleData[0].company
            const messageData = {
              type: 'psa-message',
              vehicle: getVehicleData[0]._id,
              company: companyId,
              messages: {
                type: 'text',
                content: messageText
              }
            }

            // get conversation data
            const getConversation = await this.app.service('conversations').find({
              query: {
                $limit: 1,
                type: 'psa-message',
                vehicle: getVehicleData[0]._id,
                company: companyId
              },
              paginate: false
            })

            if (getConversation.length > 0) {
              messageData.conversationId = getConversation[0]._id
            }

            // send message via messages service
            await this.app.service('messages').create(messageData)
          }
          // end block for send data to message service

          if (jobServiceAvb === 'true' && tripData.tripId) {
            // get trip data
            try {
              const params = {
                query: { tripId: tripData.tripId },
                mongoose: { upsert: true }
              }

              await this.app.service('trips').patch(null, tripData, params)
            } catch (err) {
              logger.error('Oopss! Something went wrong.', err)
            }

            // handle jobs data
            await new Promise(resolve => setTimeout(resolve, 2000))
            await this.processJob(tripData, jobTrip, companyId)
          }
        } else if (tripData.status === 'PZ' || tripData.status === 'CS' || tripData.status === 'NS' || tripData.status === 'ND' || tripData.status === 'DZ') {
          // get vehicle data
          const vehicle = getVehicleData[0]
          if (!_.isUndefined(vehicle)) {
            const messageData = {
              type: 'psa-message',
              vehicle: vehicle._id,
              company: vehicle.company,
              messages: {
                type: 'text',
                content: messageText
              }
            }

            // get conversation data
            const getConversationData = await this.app.service('conversations').find({
              query: {
                $limit: 1,
                type: 'psa-message',
                vehicle: vehicle._id,
                company: vehicle.company
              },
              paginate: false
            })

            if (getConversationData.length > 0) {
              messageData.conversationId = getConversationData[0]._id
            }

            // send message via messages service
            await this.app.service('messages').create(messageData)
          }
        } else if (tripData.status === 'CZ') {
          // block for send data to message service
          // get vehicle data
          const vehicle = getVehicleData[0]
          if (!_.isUndefined(vehicle)) {
            companyId = vehicle.company
            const messageData = {
              type: 'psa-message',
              vehicle: vehicle._id,
              company: companyId,
              messages: {
                type: 'text',
                content: messageText
              }
            }

            // get conversation data
            const getConversationData = await this.app.service('conversations').find({
              query: {
                $limit: 1,
                type: 'psa-message',
                vehicle: vehicle._id,
                company: companyId
              },
              paginate: false
            })

            if (getConversationData.length > 0) {
              messageData.conversationId = getConversationData[0]._id
            }

            // send message via messages service
            await this.app.service('messages').create(messageData)
          }

          if (jobServiceAvb === 'true') {
            // get trip data
            const getTripData = await this.app.service('trips').find({
              query: {
                $limit: 1,
                tripId: tripData.tripId
              },
              paginate: false
            })

            if (getTripData.length > 0) {
              // update trip status
              this.app.service('trips').patch(getTripData[0]._id, { status: tripData.status })
            }

            if (jobTrip.length > 0) {
              // update survey status for each job
              for (let i = 0; i < jobTrip.length; i++) {
                let getJob = null
                if (!jobTrip[i].Container_Number) {
                  const queryGetJob = {
                    tripId: tripData.tripId,
                    truckNumber: outHdr.destination,
                    batchNumber: jobTrip[i].Batch_Number,
                    company: companyId,
                    source: { $ne: 'OET' }
                  }
                  if (jobTrip[i].Batch_Number) {
                    queryGetJob.tag = 'C' + (i + 1)
                  }
                  getJob = await this.app.service('jobs').find({
                    $limit: 1,
                    query: queryGetJob,
                    paginate: false
                  })
                } else {
                  getJob = await this.app.service('jobs').find({
                    $limit: 1,
                    query: {
                      tripId: tripData.tripId,
                      truckNumber: outHdr.destination,
                      containerNumber: jobTrip[i].Container_Number,
                      company: companyId,
                      source: { $ne: 'OET' }
                    },
                    paginate: false
                  })
                }

                // validate survey result
                if (getJob.length > 0) {
                  let surveyResult = 'NO'
                  const surveyText = jobTrip[i].text.toLowerCase()
                  if (surveyText.includes('successful') || jobTrip[i].Survey.toLowerCase() === 'yes') {
                    surveyResult = 'YES'
                  }

                  // update survey result
                  await this.app.service('jobs').patch(getJob[0]._id, {
                    survey: surveyResult,
                    ...(messageText ? { text: messageText } : {})
                  })
                }
              }
            }
          }
        } else if (tripData.status === 'C') {
          // block for send data to message service
          // get vehicle data
          const vehicle = getVehicleData[0]
          if (!_.isUndefined(vehicle)) {
            companyId = vehicle.company
            const messageData = {
              type: 'psa-message',
              vehicle: vehicle._id,
              company: companyId,
              messages: {
                type: 'text',
                content: messageText
              }
            }

            // get conversation data
            const getConversationData = await this.app.service('conversations').find({
              query: {
                $limit: 1,
                type: 'psa-message',
                vehicle: vehicle._id,
                company: companyId
              },
              paginate: false
            })

            if (getConversationData.length > 0) {
              messageData.conversationId = getConversationData[0]._id
            }
          }

          if (jobServiceAvb === 'true') {
            // get trip data
            const getTripData = await this.app.service('trips').find({
              query: {
                $limit: 1,
                tripId: tripData.tripId
              },
              paginate: false
            })

            if (getTripData.length > 0) {
              // update trip status
              this.app.service('trips').patch(getTripData[0]._id, { status: tripData.status })
            }

            const withdrawnStatus = 'withdrawn'
            for (let i = 0; i < jobTrip.length; i++) {
              let job = null
              if (!jobTrip[i].Container_Number) {
                const queryJob = {
                  $limit: 1,
                  tripId: tripData.tripId,
                  truckNumber: jobTrip[i].Truck_No,
                  batchNumber: jobTrip[i].Batch_Number,
                  company: companyId,
                  source: { $ne: 'OET' }
                }
                if (jobTrip[i].Batch_Number) {
                  queryJob.tag = 'C' + (i + 1)
                }
                job = await this.app.service('jobs').find({
                  query: queryJob,
                  paginate: false
                })
              } else {
                job = await this.app.service('jobs').find({
                  query: {
                    $limit: 1,
                    tripId: tripData.tripId,
                    truckNumber: jobTrip[i].Truck_No,
                    containerNumber: jobTrip[i].Container_Number,
                    company: companyId,
                    source: { $ne: 'OET' }
                  },
                  paginate: false
                })
              }

              if (job.length > 0) {
                const jobPayload = {
                  ...(messageText ? { text: messageText } : {})
                }

                if (job[0].jobStatus !== 'completed') {
                  // update trip status
                  jobPayload.jobStatus = withdrawnStatus
                }

                if (Object.keys(jobPayload).length) await this.app.service('jobs').patch(job[0]._id, jobPayload)
              }
            }
          }
        } else { // if status ''
          // block for send data to message service
          // get vehicle data
          const vehicle = getVehicleData[0]
          if (!_.isUndefined(vehicle)) {
            companyId = vehicle.company
            const messageData = {
              type: 'psa-message',
              vehicle: vehicle._id,
              company: companyId,
              messages: {
                type: 'text',
                content: messageText
              }
            }

            // get conversation data
            const getConversationData = await this.app.service('conversations').find({
              query: {
                $limit: 1,
                type: 'psa-message',
                vehicle: vehicle._id,
                company: companyId
              },
              paginate: false
            })

            if (getConversationData.length > 0) {
              messageData.conversationId = getConversationData[0]._id
            }

            // send message via messages service
            await this.app.service('messages').create(messageData)
          }
        }
      }
    } else if (messageType === 'operation' || messageType === 'yard') {
      if (messageDirection === 'out') {
        // start send message
        // get vehicle data
        const vehicle = getVehicleData[0]
        if (!_.isUndefined(vehicle)) {
          companyId = vehicle.company
          const messageData = {
            type: 'psa-message',
            vehicle: vehicle._id,
            company: companyId,
            messages: {
              type: 'text',
              content: messageText
            }
          }

          // get conversation data
          const getConversationData = await this.app.service('conversations').find({
            query: {
              $limit: 1,
              type: 'psa-message',
              vehicle: vehicle._id,
              company: companyId
            },
            paginate: false
          })

          if (getConversationData.length > 0) {
            messageData.conversationId = getConversationData[0]._id
          }

          // send message via messages service
          await this.app.service('messages').create(messageData)
        }
        // end send message
        if (jobServiceAvb === 'true') {
          if (jobTrip !== null && jobTrip.length > 0) {
            let jobBatchMix = false
            if (jobTrip.length > 1) {
              if (jobTrip[0].Batch_Number && jobTrip[1].Batch_Number) {
                jobBatchMix = true
              }
            }
            for (let i = 0; i < jobTrip.length; i++) {
              const queryJobDataTag = {
                $limit: 1,
                jobStatus: {
                  $nin: ['cancel', 'rejected']
                },
                batchNumber: jobTrip[i].Batch_Number,
                truckNumber: outHdr.destination,
                company: companyId,
                source: { $ne: 'OET' }
              }

              const getJobDataTag = await this.app.service('jobs').find({
                query: queryJobDataTag,
                paginate: false
              })

              let job = null
              if (!jobTrip[i].Container_Number) {
                const queryJob = {
                  $limit: 1,
                  // jobStatus: 'in progress',
                  jobStatus: {
                    $nin: ['cancel', 'completed', 'rejected']
                  },
                  batchNumber: jobTrip[i].Batch_Number,
                  truckNumber: outHdr.destination,
                  company: companyId,
                  source: { $ne: 'OET' }
                }

                if (jobTrip[i].Batch_Number) {
                  queryJob.tag = 'C' + (i + 1)
                  if (jobTrip.length > 1) {
                    if (jobBatchMix) {
                      queryJob.tag = 'C' + (i + 1)
                    } else if (getJobDataTag[0]) {
                      queryJob.tag = getJobDataTag[0].tag
                    }
                  }
                }

                job = await this.app.service('jobs').find({
                  query: queryJob,
                  paginate: false
                })
              } else {
                job = await this.app.service('jobs').find({
                  query: {
                    $limit: 1,
                    // jobStatus: 'in progress',
                    jobStatus: {
                      $nin: ['cancel', 'completed', 'rejected']
                    },
                    containerNumber: jobTrip[i].Container_Number,
                    truckNumber: outHdr.destination,
                    company: companyId,
                    source: { $ne: 'OET' }
                  },
                  paginate: false
                })
              }

              if (job.length > 0) {
                const updateJobData = messageText ? { text: messageText } : {}
                if (jobTrip[i].PSA_Location) {
                  updateJobData.psaLocation = jobTrip[i].PSA_Location
                }

                if (jobTrip[i].PSA_Instruction) {
                  updateJobData.psaInstruction = jobTrip[i].PSA_Instruction
                }

                if (jobTrip[i].Job_Type) {
                  updateJobData.jobType = jobTrip[i].Job_Type
                }

                if (!_.isEmpty(updateJobData)) {
                  await this.app.service('jobs').patch(job[0]._id, updateJobData)
                }
              }
            }
          }
        }
      }
    }

    // store data to event logs table
    const eventLogs = {
      action: 'psa-message-received',
      data
    }
    await this.app.service('event-logs').create(eventLogs)

    return data
  }

  async processJob (tripData, jobTrip, companyId) {
    // handle jobs data
    if (jobTrip.length > 0) {
      const arrayJobType = []
      let payloadJobType = 'normal' // for flagging post payload
      let jobBatchMix = false
      // check the job is mix or not
      if (jobTrip.length > 1) {
        for (let j = 0; j < jobTrip.length; j++) {
          arrayJobType.push(jobTrip[j].Job_Type)
        }
        const allEqual = arr => arr.every(v => v === arr[0])
        const checkMix = allEqual(arrayJobType)
        if (!checkMix) {
          payloadJobType = 'mix'
        }
        if (jobTrip[0].Batch_Number && jobTrip[1].Batch_Number) {
          jobBatchMix = true
        }
      }
      for (let i = 0; i < jobTrip.length; i++) {
        let getJobData = null
        let getJobDataTag = null
        let replaceBlankJob = false
        let payloadType = 'normal' // for flagging and inserted to database
        let tripIdFromAdaptor = ''
        let jobTripId = jobTrip[i].Trip_ID

        // check job with blank trip Id with same container Number (for case mix container type)
        // if (tripData.status === 'S') {
        const getJob = {
          $limit: 1,
          tripId: '<blank>',
          truckNumber: jobTrip[i].Truck_No,
          jobStatus: {
            $nin: ['cancel', 'rejected']
          },
          company: companyId,
          source: { $ne: 'OET' }
        }

        if (jobTrip[i].Container_Number) {
          getJob.containerNumber = jobTrip[i].Container_Number
        } else {
          getJob.batchNumber = jobTrip[i].Batch_Number
        }

        getJobData = await this.app.service('jobs').find({
          query: getJob,
          paginate: false
        })

        if (getJobData.length > 0) {
          replaceBlankJob = true
          if (tripData.status !== 'S') {
            jobTripId = '<blank>'
          }
        }

        // check existing job type
        const getJobTag = {
          $limit: 1,
          truckNumber: jobTrip[i].Truck_No,
          jobStatus: {
            $nin: ['cancel', 'rejected']
          },
          company: companyId,
          source: { $ne: 'OET' }
        }

        if (jobTrip[i].Container_Number) {
          getJobTag.containerNumber = jobTrip[i].Container_Number
        } else {
          getJobTag.batchNumber = jobTrip[i].Batch_Number
        }

        getJobDataTag = await this.app.service('jobs').find({
          query: getJobTag,
          paginate: false
        })

        if (!replaceBlankJob) {
          if (!jobTrip[i].Container_Number) {
            const queryJobData = {
              $limit: 1,
              tripId: tripData.tripId,
              truckNumber: jobTrip[i].Truck_No,
              batchNumber: jobTrip[i].Batch_Number,
              jobStatus: {
                $nin: ['cancel', 'rejected']
              },
              company: companyId,
              source: { $ne: 'OET' }
            }
            // special for batch number job using tag
            if (jobTrip[i].Batch_Number) {
              queryJobData.tag = 'C' + (i + 1)
              if (jobTrip.length > 1) {
                if (jobBatchMix) {
                  queryJobData.tag = 'C' + (i + 1)
                } else if (getJobDataTag[0]) {
                  queryJobData.tag = getJobDataTag[0].tag
                }
              }
            }
            getJobData = await this.app.service('jobs').find({
              query: queryJobData,
              paginate: false
            })
          } else {
            getJobData = await this.app.service('jobs').find({
              query: {
                $limit: 1,
                tripId: tripData.tripId,
                truckNumber: jobTrip[i].Truck_No,
                containerNumber: jobTrip[i].Container_Number,
                jobStatus: {
                  $nin: ['cancel', 'rejected']
                },
                company: companyId,
                source: { $ne: 'OET' }
              },
              paginate: false
            })
          }
        }

        let job = getJobData[0]

        // function for set Trip Id when message A have new container and different type (mix)
        if (!replaceBlankJob) {
          if (tripData.status === 'W' || tripData.status === 'A') {
            if (_.isUndefined(job)) {
              if (payloadJobType === 'mix') {
                jobTripId = '<blank>'
                tripIdFromAdaptor = jobTrip[i].Trip_ID
                payloadType = 'mix'
              } else {
                const getLastJob = await this.app.service('jobs').find({
                  query: {
                    $limit: 1,
                    tripId: tripData.tripId,
                    truckNumber: jobTrip[i].Truck_No,
                    jobStatus: {
                      $nin: ['cancel', 'rejected']
                    },
                    company: companyId,
                    source: { $ne: 'OET' }
                  },
                  paginate: false
                })

                if (!_.isUndefined(getLastJob[0])) {
                  if (getLastJob[0].jobType && (getLastJob.jobType !== jobTrip[i].Job_Type)) {
                    jobTripId = '<blank>'
                    tripIdFromAdaptor = jobTrip[i].Trip_ID
                    payloadType = 'mix'
                  }
                }
              }
            }
          }
        }

        if (jobTripId === '<blank>') {
          const queryJobRepalce = {
            $limit: 1,
            tripId: {
              $ne: '<blank>'
            },
            truckNumber: jobTrip[i].Truck_No,
            jobStatus: {
              $in: ['new', 'in progress']
            },
            company: companyId,
            source: { $ne: 'OET' }
          }

          if (jobTrip[i].Container_Number) {
            queryJobRepalce.containerNumber = jobTrip[i].Container_Number
          } else {
            queryJobRepalce.batchNumber = jobTrip[i].Batch_Number
          }

          if (tripData.status !== 'S') {
            queryJobRepalce.jobType = jobTrip[i].Job_Type
          }
          getJobData = await this.app.service('jobs').find({
            query: queryJobRepalce,
            paginate: false
          })

          if (getJobData.length < 1) {
            const queryJobRepalceCek = {
              $limit: 1,
              tripId: '<blank>',
              truckNumber: jobTrip[i].Truck_No,
              jobStatus: {
                $in: ['new', 'in progress']
              },
              company: companyId,
              source: { $ne: 'OET' }
            }

            if (jobTrip[i].Container_Number) {
              queryJobRepalceCek.containerNumber = jobTrip[i].Container_Number
            } else {
              queryJobRepalceCek.batchNumber = jobTrip[i].Batch_Number
            }

            if (tripData.status !== 'S') {
              queryJobRepalceCek.jobType = jobTrip[i].Job_Type
            }
            getJobData = await this.app.service('jobs').find({
              query: queryJobRepalceCek,
              paginate: false
            })
          }
          if (getJobData[0] && getJobData[0].tripId !== '<blank>') {
            jobTripId = getJobData[0].tripId
          } else {
            const lessDate = moment().subtract(2, 'days').seconds(0).utc(true).toDate()
            const queryNewTripJob = {
              $limit: 1,
              truckNumber: jobTrip[i].Truck_No,
              jobStatus: {
                $in: ['in progress', 'new']
              },
              company: companyId,
              createdAt: { $gte: lessDate },
              source: { $ne: 'OET' }
            }

            if (jobTrip[i].Container_Number) {
              queryNewTripJob.containerNumber = jobTrip[i].Container_Number
            } else {
              queryNewTripJob.batchNumber = jobTrip[i].Batch_Number
            }

            getJobData = await this.app.service('jobs').find({
              query: queryNewTripJob,
              paginate: false
            })

            if (getJobData[0] && getJobData[0].tripId !== '<blank>') {
              jobTripId = getJobData[0].tripId
            } else {
              if (payloadJobType === 'mix' && jobTrip[i].Job_Type === 'EXP') {
                jobTripId = jobTrip[i].Trip_ID
              } else if (payloadJobType === 'normal') {
                jobTripId = jobTrip[i].Trip_ID
              }
            }
          }
        }

        job = getJobData[0]

        const jobData = {
          jobType: jobTrip[i].Job_Type,
          tripId: jobTripId,
          text: jobTrip[i].text,
          truckNumber: jobTrip[i].Truck_No,
          sealNumber: jobTrip[i].Seal_No,
          containerNumber: jobTrip[i].Container_Number,
          batchNumber: jobTrip[i].Batch_Number,
          typeSize: jobTrip[i].Type_Size,
          psaInstruction: jobTrip[i].PSA_Instruction,
          pickupLocation: jobTrip[i].Pickup_Location,
          deliveryLocation: jobTrip[i].Delivery_Location,
          psaLocation: jobTrip[i].PSA_Location,
          survey: jobTrip[i].Survey,
          weight: jobTrip[i].Weight,
          tt: jobTrip[i].tt,
          jobStatus: jobTrip[i].Job_Status,
          payloadType,
          tripIdFromAdaptor,
          area_id: tripData.outHdr.area_id,
          source: tripData.outHdr.sender_name
        }
        if (jobTrip[i].Batch_Number) {
          jobData.tag = 'C' + (i + 1)
          if (jobTrip.length > 1) {
            if (jobBatchMix) {
              jobData.tag = 'C' + (i + 1)
            } else if (job) {
              jobData.tag = job.tag
            }
          }
        }

        // for delete attribute job data if value is null or key undefined
        const keys = Object.keys(jobData)
        keys.forEach((key, index) => {
          if (jobData[key] === '' || jobData[key] === undefined) {
            delete jobData[key]
          }
        })

        // set company
        jobData.company = companyId

        // TO DO : handle jobs with batch number only(without container number)
        if (_.isUndefined(job)) {
          jobData.jobStatus = 'new'
          if (tripData.status !== 'S') {
            if (payloadType !== 'mix') {
              jobData.jobStatus = 'new'
            } else {
              if (jobData.jobType === 'IMP') { // jika mix dan job type IMP auto update to new
                jobData.jobStatus = 'new'
              }
            }
          }
          job = await this.app.service('jobs').create(jobData)

          /** send notification **/
          await this.sendNotifNewJob(jobData)
        } else {
          if (tripData.status !== 'S') {
            if (job.jobTypeManual && !job.jobStatus) { // job type set manualy by driver
              jobData.jobStatus = 'new'
            } else {
              if (!_.isUndefined(jobData.jobStatus)) {
                jobData.jobStatus = 'in progress'
              } else {
                delete jobData.jobStatus
              }
            }
          }
          job = await this.app.service('jobs').patch(job._id, jobData)
        }
      }
    }
  }

  async sendUrgentBroadcast (data) {
    const { conversationId } = await this.app.service('conversations').find({ query: { type: 'broadcast' } })
      .then(({ data, total }) => {
        if (total) return { conversationId: data[0]._id }

        return { conversationId: null }
      })
      .catch(() => {
        return { conversationId: null }
      })

    this.app.service('messages').create({
      type: 'broadcast',
      conversationId,
      messages: {
        type: 'text',
        content: data.text
      }
    })
      .catch(err => {
        throw new Error(err.stack)
      })
  }

  async sendNotifNewJob (data) {
    const vehicleNumber = data.truckNumber
    // get driver
    // get data old vehicle
    const vehicle = await this.app.service('vehicles').find({
      query: {
        $limit: 1,
        company: data.company,
        vehicleNo: data.truckNumber
      },
      paginate: false
    })
    if (vehicle[0].driver !== null) {
      const msgData = {
        type: 'private',
        company: vehicle[0].company,
        members: [
          vehicle[0].driver
        ],
        messages: {
          type: 'job-created',
          sender: vehicle[0].driver,
          content: (data.containerNumber) ? data.containerNumber : data.batchNumber
        }
      }
      await this.app.service('messages').create(msgData)
    }
  }
}
