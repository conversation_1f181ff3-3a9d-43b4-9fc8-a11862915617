// Initializes the `psa-messaging` service on path `/psa-messaging`
const { PsaMessaging } = require('./psa-messaging.class')
const createModel = require('../../models/psa-messaging.model')
const hooks = require('./psa-messaging.hooks')

module.exports = function (app) {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate')
  }

  // Initialize our service with any options it requires
  app.use('/psa-messaging', new PsaMessaging(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('psa-messaging')

  service.hooks(hooks)
}
