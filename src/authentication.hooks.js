/* eslint-disable indent */
const { BadRequest, NotAuthenticated, NotAcceptable } = require('@feathersjs/errors')

const logAuthActivity = require('@hooks/log-auth-activity')
const generateRefreshToken = require('@hooks/generate-refresh-token')
const updateLastOnline = require('@hooks/update-last-online')
const logger = require('@hooks/logger')

module.exports = {
  before: {
    create: [
      async ctx => {
        if (ctx.data.strategy === 'local') {
          try {
            const [{ username, email, status, group, company }] = await ctx.app.service('users').find({
              paginate: false,
              query: {
                $or: [
                  { username: ctx.data.email },
                  { email: ctx.data.email },
                  { mobile: ctx.data.email }
                ],
                $limit: 1
              }
            })

            ctx.data.username = username || ctx.data.email
            ctx.data.email = email || ctx.data.email

            if (!status) {
              throw new BadRequest()
            }

            if (group || company) {
              const orgStatus = await ctx.app.service('groups').find({ query: { _id: { $in: [group, company] }, status: 'active' }, paginate: false })
              if (orgStatus.length !== 2) throw new NotAcceptable('Your company/group is inactive')
            }

            return ctx
          } catch (error) {
            if (error.message.includes('Cannot read property \'username\'')) throw new NotAuthenticated('Incorrect email or password.')
            throw error
          }
        }
      }
    ]
  },

  after: {
    all: [logAuthActivity()],

    create: [
      async ctx => {
        if (ctx.data.strategy === 'local' && ctx.result.user) {
          const { user } = ctx.result
          logger.info(`${user._id} ${user.fullname} login success`)
        }

        let user

        if (ctx.data.device) {
          user = await ctx.app.service('users').updateFcm(ctx.result.user._id, ctx.data.device)
        } else {
          const userId = ctx.result.user._id
          user = await ctx.app.service('users').get(userId)
        }

        delete user.password
        delete user.passwordHistories

        ctx.result.user = user

        if (ctx.data.strategy === 'local' && (user && (ctx.data.device.FCMId !== 'portal' || typeof user.firstLogin === 'undefined' || user.firstLogin))) {
          const device = (ctx.data.device.FCMId === 'portal') ? 'portal' : 'mobile'
          const data = {
            type: 'login',
            channel: ctx.result.user.preferredComm,
            secret: ctx.result.user._id + '-first-login-' + device,
            to: ctx.result.user._id,
            device
          }
          if (ctx.result.user.preferredComm === 'email') data.templateUrl = await `${ctx.app.get('template').email}/otp-notifications.ejs`

          if (device !== 'mobile') { // for sent otp mobile direct code on mobile
            ctx.app.service('notifications').create(data)
              .then(({ type, channel, token }) => {
                logger.debug(`OTP ${channel} ${type} with token ${token} is sent`)
              })
              .catch(err => logger.error('Oops! something when wrong ', err))
          }
        }

        if (ctx.data.strategy === 'local' && ctx.data.device && ctx.result.user.deviceHistory) {
          if (ctx.data.device.uuid === ctx.result.user.deviceHistory.uuid) {
            ctx.result.user.statusDevice = 'S'
          } else {
            ctx.result.user.statusDevice = 'N'
          }
        } else {
          ctx.result.user.statusDevice = 'N'
        }
        return ctx
      },
      generateRefreshToken(),
      updateLastOnline()
    ],
    remove: [
      async ctx => {
        if (ctx.result.user) {
          const { user } = ctx.result
          logger.info(`${user._id} ${user.fullname} logout success`)
          let isDriver = await ctx.app.service('vehicles').find({ paginate: false, query: { driver: ctx.result.user._id } })

          isDriver = isDriver[0]
          if (isDriver) {
            const conversation = await ctx.app.service('conversations').find({ paginate: false, query: { type: 'group', vehicle: isDriver._id } })
            const msgData = {
              type: 'group',
              flagFrom: 'ctr-message',
              company: ctx.result.user.company,
              vehicle: ctx.result.user._id,
              messages: {
                type: 'text',
                sender: ctx.result.user._id,
                content: `Driver ${ctx.result.user.fullname} has logged out`
              }
            }

            if (conversation.length > 0) msgData.conversationId = conversation[0]._id
            await ctx.app.service('messages').create(msgData)
          }
        }
      },
      updateLastOnline()
    ]
  },

  error: {
    create: [ctx => { if ([401, 400].includes(ctx.error.code)) { ctx.error.message = 'Incorrect user or password.' } }]
  }
}
