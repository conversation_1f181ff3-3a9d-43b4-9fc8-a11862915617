module.exports = function (app) {
  if (typeof app.channel !== 'function') {
    return
  }

  app.on('connection', connection => {
    app.channel('anonymous').join(connection)
  })

  app.on('logout', (authResult, { connection }) => {
    if (connection) {
      app.channel('anonymous').join(connection)
      app.channel('authenticated').leave(connection)
      app.channel('urgentBroadcastMembers').leave(connection)
      app.channel(`userIds/${authResult.user._id}`).leave(connection)
      app.channel('admins').leave(connection)
    }
  })

  app.on('login', async (authResult, { connection }) => {
    if (connection) {
      app.channel('anonymous').leave(connection)
      app.channel('authenticated').join(connection)
      app.channel(`userIds/${authResult.user._id}`).join(connection)

      // Join broadcast channel
      let isConversationMember = false

      if (authResult.user.company) {
        await app.service('groups').find({ query: { _id: { $in: authResult.user.company } } })
          .then(async ({ data: [{ _id }] }) => {
            const { total } = await app.service('vehicles').find({ query: { company: { $in: _id } } })

            if (total) isConversationMember = true
          })
      }

      const isUrgentBroadcastMember = isConversationMember || (authResult.user && !authResult.user.company)

      if (isUrgentBroadcastMember) await app.channel('urgentBroadcastMembers').join(connection)

      // Join Admin
      const isAdmin = await app.service('users').get(authResult.user._id, {
        query: {
          group: { $exists: false },
          company: { $exists: false }
        }
      }).then(() => true).catch(() => false)

      if (isAdmin) app.channel('admins').join(connection)
    }
  })
}
