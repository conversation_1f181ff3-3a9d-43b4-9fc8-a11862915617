{"identity": {"id": "06a70341-bbd2-4cab-8b47-47bd468f3583", "name": "CTR", "channel_url": "Int-CDI-ctr.fifo", "bucket_name": "int-cdi-ctr", "organization": {"id": "c48e51c5-6418-441f-bc07-6160cbaf9ba0", "name": "CDAS Logistics Aliance (Ltd.)"}}, "produces": [{"id": "haulier_gps", "name": "<PERSON><PERSON>er G<PERSON>", "schema": {"type": "object", "additionalProperties": false, "properties": {"haulier_nm": {"type": "string", "description": "Abbreviated name of haulier (PSA definition) - Example: ACS, CWT, COGENT"}, "haulier_uen": {"type": "string", "description": "Unique Entity Number for haulier"}, "vehicle_no": {"type": "string", "description": "Registration number of prime mover / towhead"}, "position_latitude": {"type": "number", "format": "double", "description": "Latitude coordinates of position"}, "position_longitude": {"type": "number", "format": "double", "description": "Longitude coordinates of position"}, "position_altitude": {"type": "number", "format": "double", "description": "Elevation coordinates of position"}, "heading": {"type": "number", "format": "double", "description": "Direction in which vehicle is pointing"}, "snapshot_dt": {"type": "string", "format": "date-time", "description": "Timestamp of this record being captured (ISO8601)"}}, "required": ["haulier_nm", "vehicle_no", "position_latitude", "position_longitude", "heading", "snapshot_dt"]}, "querySchema": {"additionalProperties": false, "type": "object", "properties": {"position_latitude": {"type": "number", "format": "double", "description": "Latitude coordinates of position"}, "position_longitude": {"type": "number", "format": "double", "description": "Longitude coordinates of position"}, "position_radius_metres": {"type": "string", "format": "int64", "description": "Range of geofence"}, "snapshot_dt": {"type": "string", "format": "date-time", "description": "Timestamp of this record being captured (ISO8601)"}}, "required": ["position_latitude", "position_longitude", "position_radius_metres", "snapshot_dt"]}, "to": [{"id": "0fd2b2f7-4dfd-48d7-8256-38237bab3846", "name": "Cogent Holdings Pte. Ltd", "system": {"id": "b0d74e48-b08a-426e-bb22-5f82e34c612c", "name": "Cogent Holdings Pte. Ltd"}}, {"id": "d4002d09-f0d7-4258-8428-0032fd8d2f52", "name": "PSA International Pte. Ltd", "system": {"id": "f989dc80-531f-44b0-af4e-31481891200d", "name": "SmartBooking"}}], "onBehalfOf": [{"id": "1d342034-0eb0-4fe1-9d1d-987facd6d02b", "lookupid": null, "name": "Allied Container Services Pte. Ltd"}, {"id": "0fd2b2f7-4dfd-48d7-8256-38237bab3846", "lookupid": null, "name": "Cogent Holdings Pte. Ltd"}, {"id": "cb8c69e0-58a2-4ebc-b201-4ff6ae2fe3a6", "lookupid": null, "name": "CWT Pte. Ltd"}]}], "consumes": []}