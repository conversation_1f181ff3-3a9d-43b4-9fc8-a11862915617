{"version": 3, "file": "workbook.js", "names": ["Worksheet", "require", "DefinedNames", "XLSX", "CSV", "Workbook", "constructor", "category", "company", "created", "Date", "description", "keywords", "manager", "modified", "properties", "calcProperties", "_worksheets", "subject", "title", "views", "media", "_definedNames", "xlsx", "_xlsx", "csv", "_csv", "nextId", "i", "length", "addWorksheet", "name", "options", "id", "console", "trace", "tabColor", "argb", "theme", "indexed", "lastOrderNo", "reduce", "acc", "ws", "orderNo", "worksheetOptions", "Object", "assign", "workbook", "worksheet", "removeWorksheetEx", "removeWorksheet", "getWorksheet", "destroy", "undefined", "find", "Boolean", "worksheets", "slice", "sort", "a", "b", "filter", "eachSheet", "iteratee", "for<PERSON>ach", "sheet", "definedNames", "clearThemes", "_themes", "addImage", "image", "push", "type", "getImage", "model", "creator", "lastModifiedBy", "lastPrinted", "map", "sheets", "language", "revision", "contentStatus", "themes", "value", "worksheetModel", "state", "findIndex", "module", "exports"], "sources": ["../../../lib/doc/workbook.js"], "sourcesContent": ["'use strict';\n\nconst Worksheet = require('./worksheet');\nconst DefinedNames = require('./defined-names');\nconst XLSX = require('../xlsx/xlsx');\nconst CSV = require('../csv/csv');\n\n// Workbook requirements\n//  Load and Save from file and stream\n//  Access/Add/Delete individual worksheets\n//  Manage String table, Hyperlink table, etc.\n//  Manage scaffolding for contained objects to write to/read from\n\nclass Workbook {\n  constructor() {\n    this.category = '';\n    this.company = '';\n    this.created = new Date();\n    this.description = '';\n    this.keywords = '';\n    this.manager = '';\n    this.modified = this.created;\n    this.properties = {};\n    this.calcProperties = {};\n    this._worksheets = [];\n    this.subject = '';\n    this.title = '';\n    this.views = [];\n    this.media = [];\n    this._definedNames = new DefinedNames();\n  }\n\n  get xlsx() {\n    if (!this._xlsx) this._xlsx = new XLSX(this);\n    return this._xlsx;\n  }\n\n  get csv() {\n    if (!this._csv) this._csv = new CSV(this);\n    return this._csv;\n  }\n\n  get nextId() {\n    // find the next unique spot to add worksheet\n    for (let i = 1; i < this._worksheets.length; i++) {\n      if (!this._worksheets[i]) {\n        return i;\n      }\n    }\n    return this._worksheets.length || 1;\n  }\n\n  addWorksheet(name, options) {\n    const id = this.nextId;\n\n    // if options is a color, call it tabColor (and signal deprecated message)\n    if (options) {\n      if (typeof options === 'string') {\n        // eslint-disable-next-line no-console\n        console.trace(\n          'tabColor argument is now deprecated. Please use workbook.addWorksheet(name, {properties: { tabColor: { argb: \"rbg value\" } }'\n        );\n        options = {\n          properties: {\n            tabColor: {argb: options},\n          },\n        };\n      } else if (options.argb || options.theme || options.indexed) {\n        // eslint-disable-next-line no-console\n        console.trace(\n          'tabColor argument is now deprecated. Please use workbook.addWorksheet(name, {properties: { tabColor: { ... } }'\n        );\n        options = {\n          properties: {\n            tabColor: options,\n          },\n        };\n      }\n    }\n\n    const lastOrderNo = this._worksheets.reduce((acc, ws) => ((ws && ws.orderNo) > acc ? ws.orderNo : acc), 0);\n    const worksheetOptions = Object.assign({}, options, {\n      id,\n      name,\n      orderNo: lastOrderNo + 1,\n      workbook: this,\n    });\n\n    const worksheet = new Worksheet(worksheetOptions);\n\n    this._worksheets[id] = worksheet;\n    return worksheet;\n  }\n\n  removeWorksheetEx(worksheet) {\n    delete this._worksheets[worksheet.id];\n  }\n\n  removeWorksheet(id) {\n    const worksheet = this.getWorksheet(id);\n    if (worksheet) {\n      worksheet.destroy();\n    }\n  }\n\n  getWorksheet(id) {\n    if (id === undefined) {\n      return this._worksheets.find(Boolean);\n    }\n    if (typeof id === 'number') {\n      return this._worksheets[id];\n    }\n    if (typeof id === 'string') {\n      return this._worksheets.find(worksheet => worksheet && worksheet.name === id);\n    }\n    return undefined;\n  }\n\n  get worksheets() {\n    // return a clone of _worksheets\n    return this._worksheets\n      .slice(1)\n      .sort((a, b) => a.orderNo - b.orderNo)\n      .filter(Boolean);\n  }\n\n  eachSheet(iteratee) {\n    this.worksheets.forEach(sheet => {\n      iteratee(sheet, sheet.id);\n    });\n  }\n\n  get definedNames() {\n    return this._definedNames;\n  }\n\n  clearThemes() {\n    // Note: themes are not an exposed feature, meddle at your peril!\n    this._themes = undefined;\n  }\n\n  addImage(image) {\n    // TODO:  validation?\n    const id = this.media.length;\n    this.media.push(Object.assign({}, image, {type: 'image'}));\n    return id;\n  }\n\n  getImage(id) {\n    return this.media[id];\n  }\n\n  get model() {\n    return {\n      creator: this.creator || 'Unknown',\n      lastModifiedBy: this.lastModifiedBy || 'Unknown',\n      lastPrinted: this.lastPrinted,\n      created: this.created,\n      modified: this.modified,\n      properties: this.properties,\n      worksheets: this.worksheets.map(worksheet => worksheet.model),\n      sheets: this.worksheets.map(ws => ws.model).filter(Boolean),\n      definedNames: this._definedNames.model,\n      views: this.views,\n      company: this.company,\n      manager: this.manager,\n      title: this.title,\n      subject: this.subject,\n      keywords: this.keywords,\n      category: this.category,\n      description: this.description,\n      language: this.language,\n      revision: this.revision,\n      contentStatus: this.contentStatus,\n      themes: this._themes,\n      media: this.media,\n      calcProperties: this.calcProperties,\n    };\n  }\n\n  set model(value) {\n    this.creator = value.creator;\n    this.lastModifiedBy = value.lastModifiedBy;\n    this.lastPrinted = value.lastPrinted;\n    this.created = value.created;\n    this.modified = value.modified;\n    this.company = value.company;\n    this.manager = value.manager;\n    this.title = value.title;\n    this.subject = value.subject;\n    this.keywords = value.keywords;\n    this.category = value.category;\n    this.description = value.description;\n    this.language = value.language;\n    this.revision = value.revision;\n    this.contentStatus = value.contentStatus;\n\n    this.properties = value.properties;\n    this.calcProperties = value.calcProperties;\n    this._worksheets = [];\n    value.worksheets.forEach(worksheetModel => {\n      const {id, name, state} = worksheetModel;\n      const orderNo = value.sheets && value.sheets.findIndex(ws => ws.id === id);\n      const worksheet = (this._worksheets[id] = new Worksheet({\n        id,\n        name,\n        orderNo,\n        state,\n        workbook: this,\n      }));\n      worksheet.model = worksheetModel;\n    });\n\n    this._definedNames.model = value.definedNames;\n    this.views = value.views;\n    this._themes = value.themes;\n    this.media = value.media || [];\n  }\n}\n\nmodule.exports = Workbook;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;AACxC,MAAMC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC/C,MAAME,IAAI,GAAGF,OAAO,CAAC,cAAc,CAAC;AACpC,MAAMG,GAAG,GAAGH,OAAO,CAAC,YAAY,CAAC;;AAEjC;AACA;AACA;AACA;AACA;;AAEA,MAAMI,QAAQ,CAAC;EACbC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC;IACzB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACL,OAAO;IAC5B,IAAI,CAACM,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,aAAa,GAAG,IAAIpB,YAAY,CAAC,CAAC;EACzC;EAEA,IAAIqB,IAAIA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACA,KAAK,GAAG,IAAIrB,IAAI,CAAC,IAAI,CAAC;IAC5C,OAAO,IAAI,CAACqB,KAAK;EACnB;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACA,IAAI,GAAG,IAAItB,GAAG,CAAC,IAAI,CAAC;IACzC,OAAO,IAAI,CAACsB,IAAI;EAClB;EAEA,IAAIC,MAAMA,CAAA,EAAG;IACX;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACX,WAAW,CAACY,MAAM,EAAED,CAAC,EAAE,EAAE;MAChD,IAAI,CAAC,IAAI,CAACX,WAAW,CAACW,CAAC,CAAC,EAAE;QACxB,OAAOA,CAAC;MACV;IACF;IACA,OAAO,IAAI,CAACX,WAAW,CAACY,MAAM,IAAI,CAAC;EACrC;EAEAC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAC1B,MAAMC,EAAE,GAAG,IAAI,CAACN,MAAM;;IAEtB;IACA,IAAIK,OAAO,EAAE;MACX,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/B;QACAE,OAAO,CAACC,KAAK,CACX,8HACF,CAAC;QACDH,OAAO,GAAG;UACRjB,UAAU,EAAE;YACVqB,QAAQ,EAAE;cAACC,IAAI,EAAEL;YAAO;UAC1B;QACF,CAAC;MACH,CAAC,MAAM,IAAIA,OAAO,CAACK,IAAI,IAAIL,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACO,OAAO,EAAE;QAC3D;QACAL,OAAO,CAACC,KAAK,CACX,gHACF,CAAC;QACDH,OAAO,GAAG;UACRjB,UAAU,EAAE;YACVqB,QAAQ,EAAEJ;UACZ;QACF,CAAC;MACH;IACF;IAEA,MAAMQ,WAAW,GAAG,IAAI,CAACvB,WAAW,CAACwB,MAAM,CAAC,CAACC,GAAG,EAAEC,EAAE,KAAM,CAACA,EAAE,IAAIA,EAAE,CAACC,OAAO,IAAIF,GAAG,GAAGC,EAAE,CAACC,OAAO,GAAGF,GAAI,EAAE,CAAC,CAAC;IAC1G,MAAMG,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,OAAO,EAAE;MAClDC,EAAE;MACFF,IAAI;MACJa,OAAO,EAAEJ,WAAW,GAAG,CAAC;MACxBQ,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,MAAMC,SAAS,GAAG,IAAIjD,SAAS,CAAC6C,gBAAgB,CAAC;IAEjD,IAAI,CAAC5B,WAAW,CAACgB,EAAE,CAAC,GAAGgB,SAAS;IAChC,OAAOA,SAAS;EAClB;EAEAC,iBAAiBA,CAACD,SAAS,EAAE;IAC3B,OAAO,IAAI,CAAChC,WAAW,CAACgC,SAAS,CAAChB,EAAE,CAAC;EACvC;EAEAkB,eAAeA,CAAClB,EAAE,EAAE;IAClB,MAAMgB,SAAS,GAAG,IAAI,CAACG,YAAY,CAACnB,EAAE,CAAC;IACvC,IAAIgB,SAAS,EAAE;MACbA,SAAS,CAACI,OAAO,CAAC,CAAC;IACrB;EACF;EAEAD,YAAYA,CAACnB,EAAE,EAAE;IACf,IAAIA,EAAE,KAAKqB,SAAS,EAAE;MACpB,OAAO,IAAI,CAACrC,WAAW,CAACsC,IAAI,CAACC,OAAO,CAAC;IACvC;IACA,IAAI,OAAOvB,EAAE,KAAK,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAAChB,WAAW,CAACgB,EAAE,CAAC;IAC7B;IACA,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAAChB,WAAW,CAACsC,IAAI,CAACN,SAAS,IAAIA,SAAS,IAAIA,SAAS,CAAClB,IAAI,KAAKE,EAAE,CAAC;IAC/E;IACA,OAAOqB,SAAS;EAClB;EAEA,IAAIG,UAAUA,CAAA,EAAG;IACf;IACA,OAAO,IAAI,CAACxC,WAAW,CACpByC,KAAK,CAAC,CAAC,CAAC,CACRC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChB,OAAO,GAAGiB,CAAC,CAACjB,OAAO,CAAC,CACrCkB,MAAM,CAACN,OAAO,CAAC;EACpB;EAEAO,SAASA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACP,UAAU,CAACQ,OAAO,CAACC,KAAK,IAAI;MAC/BF,QAAQ,CAACE,KAAK,EAAEA,KAAK,CAACjC,EAAE,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEA,IAAIkC,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC7C,aAAa;EAC3B;EAEA8C,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,OAAO,GAAGf,SAAS;EAC1B;EAEAgB,QAAQA,CAACC,KAAK,EAAE;IACd;IACA,MAAMtC,EAAE,GAAG,IAAI,CAACZ,KAAK,CAACQ,MAAM;IAC5B,IAAI,CAACR,KAAK,CAACmD,IAAI,CAAC1B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;MAACE,IAAI,EAAE;IAAO,CAAC,CAAC,CAAC;IAC1D,OAAOxC,EAAE;EACX;EAEAyC,QAAQA,CAACzC,EAAE,EAAE;IACX,OAAO,IAAI,CAACZ,KAAK,CAACY,EAAE,CAAC;EACvB;EAEA,IAAI0C,KAAKA,CAAA,EAAG;IACV,OAAO;MACLC,OAAO,EAAE,IAAI,CAACA,OAAO,IAAI,SAAS;MAClCC,cAAc,EAAE,IAAI,CAACA,cAAc,IAAI,SAAS;MAChDC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BrE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBK,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B0C,UAAU,EAAE,IAAI,CAACA,UAAU,CAACsB,GAAG,CAAC9B,SAAS,IAAIA,SAAS,CAAC0B,KAAK,CAAC;MAC7DK,MAAM,EAAE,IAAI,CAACvB,UAAU,CAACsB,GAAG,CAACpC,EAAE,IAAIA,EAAE,CAACgC,KAAK,CAAC,CAACb,MAAM,CAACN,OAAO,CAAC;MAC3DW,YAAY,EAAE,IAAI,CAAC7C,aAAa,CAACqD,KAAK;MACtCvD,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBZ,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBK,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBM,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBD,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBN,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBL,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBI,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BsE,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,MAAM,EAAE,IAAI,CAACf,OAAO;MACpBhD,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBL,cAAc,EAAE,IAAI,CAACA;IACvB,CAAC;EACH;EAEA,IAAI2D,KAAKA,CAACU,KAAK,EAAE;IACf,IAAI,CAACT,OAAO,GAAGS,KAAK,CAACT,OAAO;IAC5B,IAAI,CAACC,cAAc,GAAGQ,KAAK,CAACR,cAAc;IAC1C,IAAI,CAACC,WAAW,GAAGO,KAAK,CAACP,WAAW;IACpC,IAAI,CAACrE,OAAO,GAAG4E,KAAK,CAAC5E,OAAO;IAC5B,IAAI,CAACK,QAAQ,GAAGuE,KAAK,CAACvE,QAAQ;IAC9B,IAAI,CAACN,OAAO,GAAG6E,KAAK,CAAC7E,OAAO;IAC5B,IAAI,CAACK,OAAO,GAAGwE,KAAK,CAACxE,OAAO;IAC5B,IAAI,CAACM,KAAK,GAAGkE,KAAK,CAAClE,KAAK;IACxB,IAAI,CAACD,OAAO,GAAGmE,KAAK,CAACnE,OAAO;IAC5B,IAAI,CAACN,QAAQ,GAAGyE,KAAK,CAACzE,QAAQ;IAC9B,IAAI,CAACL,QAAQ,GAAG8E,KAAK,CAAC9E,QAAQ;IAC9B,IAAI,CAACI,WAAW,GAAG0E,KAAK,CAAC1E,WAAW;IACpC,IAAI,CAACsE,QAAQ,GAAGI,KAAK,CAACJ,QAAQ;IAC9B,IAAI,CAACC,QAAQ,GAAGG,KAAK,CAACH,QAAQ;IAC9B,IAAI,CAACC,aAAa,GAAGE,KAAK,CAACF,aAAa;IAExC,IAAI,CAACpE,UAAU,GAAGsE,KAAK,CAACtE,UAAU;IAClC,IAAI,CAACC,cAAc,GAAGqE,KAAK,CAACrE,cAAc;IAC1C,IAAI,CAACC,WAAW,GAAG,EAAE;IACrBoE,KAAK,CAAC5B,UAAU,CAACQ,OAAO,CAACqB,cAAc,IAAI;MACzC,MAAM;QAACrD,EAAE;QAAEF,IAAI;QAAEwD;MAAK,CAAC,GAAGD,cAAc;MACxC,MAAM1C,OAAO,GAAGyC,KAAK,CAACL,MAAM,IAAIK,KAAK,CAACL,MAAM,CAACQ,SAAS,CAAC7C,EAAE,IAAIA,EAAE,CAACV,EAAE,KAAKA,EAAE,CAAC;MAC1E,MAAMgB,SAAS,GAAI,IAAI,CAAChC,WAAW,CAACgB,EAAE,CAAC,GAAG,IAAIjC,SAAS,CAAC;QACtDiC,EAAE;QACFF,IAAI;QACJa,OAAO;QACP2C,KAAK;QACLvC,QAAQ,EAAE;MACZ,CAAC,CAAE;MACHC,SAAS,CAAC0B,KAAK,GAAGW,cAAc;IAClC,CAAC,CAAC;IAEF,IAAI,CAAChE,aAAa,CAACqD,KAAK,GAAGU,KAAK,CAAClB,YAAY;IAC7C,IAAI,CAAC/C,KAAK,GAAGiE,KAAK,CAACjE,KAAK;IACxB,IAAI,CAACiD,OAAO,GAAGgB,KAAK,CAACD,MAAM;IAC3B,IAAI,CAAC/D,KAAK,GAAGgE,KAAK,CAAChE,KAAK,IAAI,EAAE;EAChC;AACF;AAEAoE,MAAM,CAACC,OAAO,GAAGrF,QAAQ"}