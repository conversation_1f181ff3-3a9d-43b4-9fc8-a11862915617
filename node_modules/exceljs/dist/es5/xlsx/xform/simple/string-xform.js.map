{"version": 3, "file": "string-xform.js", "names": ["BaseXform", "require", "StringXform", "constructor", "options", "tag", "attr", "attrs", "render", "xmlStream", "model", "undefined", "openNode", "addAttributes", "addAttribute", "writeText", "closeNode", "parseOpen", "node", "name", "attributes", "text", "parseText", "push", "parseClose", "join", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/simple/string-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass StringXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.tag = options.tag;\n    this.attr = options.attr;\n    this.attrs = options.attrs;\n  }\n\n  render(xmlStream, model) {\n    if (model !== undefined) {\n      xmlStream.openNode(this.tag);\n      if (this.attrs) {\n        xmlStream.addAttributes(this.attrs);\n      }\n      if (this.attr) {\n        xmlStream.addAttribute(this.attr, model);\n      } else {\n        xmlStream.writeText(model);\n      }\n      xmlStream.closeNode();\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      if (this.attr) {\n        this.model = node.attributes[this.attr];\n      } else {\n        this.text = [];\n      }\n    }\n  }\n\n  parseText(text) {\n    if (!this.attr) {\n      this.text.push(text);\n    }\n  }\n\n  parseClose() {\n    if (!this.attr) {\n      this.model = this.text.join('');\n    }\n    return false;\n  }\n}\n\nmodule.exports = StringXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,WAAW,SAASF,SAAS,CAAC;EAClCG,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACtB,IAAI,CAACC,IAAI,GAAGF,OAAO,CAACE,IAAI;IACxB,IAAI,CAACC,KAAK,GAAGH,OAAO,CAACG,KAAK;EAC5B;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACvBF,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACP,GAAG,CAAC;MAC5B,IAAI,IAAI,CAACE,KAAK,EAAE;QACdE,SAAS,CAACI,aAAa,CAAC,IAAI,CAACN,KAAK,CAAC;MACrC;MACA,IAAI,IAAI,CAACD,IAAI,EAAE;QACbG,SAAS,CAACK,YAAY,CAAC,IAAI,CAACR,IAAI,EAAEI,KAAK,CAAC;MAC1C,CAAC,MAAM;QACLD,SAAS,CAACM,SAAS,CAACL,KAAK,CAAC;MAC5B;MACAD,SAAS,CAACO,SAAS,CAAC,CAAC;IACvB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACd,GAAG,EAAE;MAC1B,IAAI,IAAI,CAACC,IAAI,EAAE;QACb,IAAI,CAACI,KAAK,GAAGQ,IAAI,CAACE,UAAU,CAAC,IAAI,CAACd,IAAI,CAAC;MACzC,CAAC,MAAM;QACL,IAAI,CAACe,IAAI,GAAG,EAAE;MAChB;IACF;EACF;EAEAC,SAASA,CAACD,IAAI,EAAE;IACd,IAAI,CAAC,IAAI,CAACf,IAAI,EAAE;MACd,IAAI,CAACe,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC;IACtB;EACF;EAEAG,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAClB,IAAI,EAAE;MACd,IAAI,CAACI,KAAK,GAAG,IAAI,CAACW,IAAI,CAACI,IAAI,CAAC,EAAE,CAAC;IACjC;IACA,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGzB,WAAW"}