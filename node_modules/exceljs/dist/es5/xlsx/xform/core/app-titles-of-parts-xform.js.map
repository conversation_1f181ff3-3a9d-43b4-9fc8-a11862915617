{"version": 3, "file": "app-titles-of-parts-xform.js", "names": ["BaseXform", "require", "AppTitlesOfPartsXform", "render", "xmlStream", "model", "openNode", "size", "length", "baseType", "for<PERSON>ach", "sheet", "leafNode", "undefined", "name", "closeNode", "parseOpen", "node", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/core/app-titles-of-parts-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass AppTitlesOfPartsXform extends BaseXform {\n  render(xmlStream, model) {\n    xmlStream.openNode('TitlesOfParts');\n    xmlStream.openNode('vt:vector', {size: model.length, baseType: 'lpstr'});\n\n    model.forEach(sheet => {\n      xmlStream.leafNode('vt:lpstr', undefined, sheet.name);\n    });\n\n    xmlStream.closeNode();\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    // no parsing\n    return node.name === 'TitlesOfParts';\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    return name !== 'TitlesOfParts';\n  }\n}\n\nmodule.exports = AppTitlesOfPartsXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,qBAAqB,SAASF,SAAS,CAAC;EAC5CG,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC;IACnCF,SAAS,CAACE,QAAQ,CAAC,WAAW,EAAE;MAACC,IAAI,EAAEF,KAAK,CAACG,MAAM;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;IAExEJ,KAAK,CAACK,OAAO,CAACC,KAAK,IAAI;MACrBP,SAAS,CAACQ,QAAQ,CAAC,UAAU,EAAEC,SAAS,EAAEF,KAAK,CAACG,IAAI,CAAC;IACvD,CAAC,CAAC;IAEFV,SAAS,CAACW,SAAS,CAAC,CAAC;IACrBX,SAAS,CAACW,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd;IACA,OAAOA,IAAI,CAACH,IAAI,KAAK,eAAe;EACtC;EAEAI,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACL,IAAI,EAAE;IACf,OAAOA,IAAI,KAAK,eAAe;EACjC;AACF;AAEAM,MAAM,CAACC,OAAO,GAAGnB,qBAAqB"}