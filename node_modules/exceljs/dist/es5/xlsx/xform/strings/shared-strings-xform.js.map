{"version": 3, "file": "shared-strings-xform.js", "names": ["XmlStream", "require", "BaseXform", "SharedStringXform", "SharedStringsXform", "constructor", "model", "values", "count", "hash", "Object", "create", "rich", "sharedStringXform", "_sharedStringXform", "uniqueCount", "length", "getString", "index", "add", "value", "richText", "addRichText", "addText", "undefined", "push", "xml", "toXml", "render", "xmlStream", "_values", "openXml", "StdDocAttributes", "openNode", "xmlns", "sx", "for<PERSON>ach", "sharedString", "closeNode", "parseOpen", "node", "parser", "name", "Error", "JSON", "stringify", "parseText", "text", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/strings/shared-strings-xform.js"], "sourcesContent": ["const XmlStream = require('../../../utils/xml-stream');\nconst BaseXform = require('../base-xform');\nconst SharedStringXform = require('./shared-string-xform');\n\nclass SharedStringsXform extends BaseXform {\n  constructor(model) {\n    super();\n\n    this.model = model || {\n      values: [],\n      count: 0,\n    };\n    this.hash = Object.create(null);\n    this.rich = Object.create(null);\n  }\n\n  get sharedStringXform() {\n    return this._sharedStringXform || (this._sharedStringXform = new SharedStringXform());\n  }\n\n  get values() {\n    return this.model.values;\n  }\n\n  get uniqueCount() {\n    return this.model.values.length;\n  }\n\n  get count() {\n    return this.model.count;\n  }\n\n  getString(index) {\n    return this.model.values[index];\n  }\n\n  add(value) {\n    return value.richText ? this.addRichText(value) : this.addText(value);\n  }\n\n  addText(value) {\n    let index = this.hash[value];\n    if (index === undefined) {\n      index = this.hash[value] = this.model.values.length;\n      this.model.values.push(value);\n    }\n    this.model.count++;\n    return index;\n  }\n\n  addRichText(value) {\n    // TODO: add WeakMap here\n    const xml = this.sharedStringXform.toXml(value);\n    let index = this.rich[xml];\n    if (index === undefined) {\n      index = this.rich[xml] = this.model.values.length;\n      this.model.values.push(value);\n    }\n    this.model.count++;\n    return index;\n  }\n\n  // <?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n  // <sst xmlns=\"http://schemas.openxmlformats.org/spreadsheetml/2006/main\" count=\"<%=totalRefs%>\" uniqueCount=\"<%=count%>\">\n  //   <si><t><%=text%></t></si>\n  //   <si><r><rPr></rPr><t></t></r></si>\n  // </sst>\n\n  render(xmlStream, model) {\n    model = model || this._values;\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n\n    xmlStream.openNode('sst', {\n      xmlns: 'http://schemas.openxmlformats.org/spreadsheetml/2006/main',\n      count: model.count,\n      uniqueCount: model.values.length,\n    });\n\n    const sx = this.sharedStringXform;\n    model.values.forEach(sharedString => {\n      sx.render(xmlStream, sharedString);\n    });\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'sst':\n        return true;\n      case 'si':\n        this.parser = this.sharedStringXform;\n        this.parser.parseOpen(node);\n        return true;\n      default:\n        throw new Error(`Unexpected xml node in parseOpen: ${JSON.stringify(node)}`);\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.values.push(this.parser.model);\n        this.model.count++;\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case 'sst':\n        return false;\n      default:\n        throw new Error(`Unexpected xml node in parseClose: ${name}`);\n    }\n  }\n}\n\nmodule.exports = SharedStringsXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACtD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAME,iBAAiB,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAE1D,MAAMG,kBAAkB,SAASF,SAAS,CAAC;EACzCG,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,KAAK,GAAGA,KAAK,IAAI;MACpBC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACC,IAAI,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACjC;EAEA,IAAIE,iBAAiBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACC,kBAAkB,KAAK,IAAI,CAACA,kBAAkB,GAAG,IAAIX,iBAAiB,CAAC,CAAC,CAAC;EACvF;EAEA,IAAII,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,KAAK,CAACC,MAAM;EAC1B;EAEA,IAAIQ,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACT,KAAK,CAACC,MAAM,CAACS,MAAM;EACjC;EAEA,IAAIR,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,KAAK,CAACE,KAAK;EACzB;EAEAS,SAASA,CAACC,KAAK,EAAE;IACf,OAAO,IAAI,CAACZ,KAAK,CAACC,MAAM,CAACW,KAAK,CAAC;EACjC;EAEAC,GAAGA,CAACC,KAAK,EAAE;IACT,OAAOA,KAAK,CAACC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACF,KAAK,CAAC,GAAG,IAAI,CAACG,OAAO,CAACH,KAAK,CAAC;EACvE;EAEAG,OAAOA,CAACH,KAAK,EAAE;IACb,IAAIF,KAAK,GAAG,IAAI,CAACT,IAAI,CAACW,KAAK,CAAC;IAC5B,IAAIF,KAAK,KAAKM,SAAS,EAAE;MACvBN,KAAK,GAAG,IAAI,CAACT,IAAI,CAACW,KAAK,CAAC,GAAG,IAAI,CAACd,KAAK,CAACC,MAAM,CAACS,MAAM;MACnD,IAAI,CAACV,KAAK,CAACC,MAAM,CAACkB,IAAI,CAACL,KAAK,CAAC;IAC/B;IACA,IAAI,CAACd,KAAK,CAACE,KAAK,EAAE;IAClB,OAAOU,KAAK;EACd;EAEAI,WAAWA,CAACF,KAAK,EAAE;IACjB;IACA,MAAMM,GAAG,GAAG,IAAI,CAACb,iBAAiB,CAACc,KAAK,CAACP,KAAK,CAAC;IAC/C,IAAIF,KAAK,GAAG,IAAI,CAACN,IAAI,CAACc,GAAG,CAAC;IAC1B,IAAIR,KAAK,KAAKM,SAAS,EAAE;MACvBN,KAAK,GAAG,IAAI,CAACN,IAAI,CAACc,GAAG,CAAC,GAAG,IAAI,CAACpB,KAAK,CAACC,MAAM,CAACS,MAAM;MACjD,IAAI,CAACV,KAAK,CAACC,MAAM,CAACkB,IAAI,CAACL,KAAK,CAAC;IAC/B;IACA,IAAI,CAACd,KAAK,CAACE,KAAK,EAAE;IAClB,OAAOU,KAAK;EACd;;EAEA;EACA;EACA;EACA;EACA;;EAEAU,MAAMA,CAACC,SAAS,EAAEvB,KAAK,EAAE;IACvBA,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACwB,OAAO;IAC7BD,SAAS,CAACE,OAAO,CAAC/B,SAAS,CAACgC,gBAAgB,CAAC;IAE7CH,SAAS,CAACI,QAAQ,CAAC,KAAK,EAAE;MACxBC,KAAK,EAAE,2DAA2D;MAClE1B,KAAK,EAAEF,KAAK,CAACE,KAAK;MAClBO,WAAW,EAAET,KAAK,CAACC,MAAM,CAACS;IAC5B,CAAC,CAAC;IAEF,MAAMmB,EAAE,GAAG,IAAI,CAACtB,iBAAiB;IACjCP,KAAK,CAACC,MAAM,CAAC6B,OAAO,CAACC,YAAY,IAAI;MACnCF,EAAE,CAACP,MAAM,CAACC,SAAS,EAAEQ,YAAY,CAAC;IACpC,CAAC,CAAC;IACFR,SAAS,CAACS,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,KAAK;QACR,OAAO,IAAI;MACb,KAAK,IAAI;QACP,IAAI,CAACD,MAAM,GAAG,IAAI,CAAC5B,iBAAiB;QACpC,IAAI,CAAC4B,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb;QACE,MAAM,IAAIG,KAAK,CAAE,qCAAoCC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAE,EAAC,CAAC;IAChF;EACF;EAEAM,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACN,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACK,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACN,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACO,UAAU,CAACN,IAAI,CAAC,EAAE;QACjC,IAAI,CAACpC,KAAK,CAACC,MAAM,CAACkB,IAAI,CAAC,IAAI,CAACgB,MAAM,CAACnC,KAAK,CAAC;QACzC,IAAI,CAACA,KAAK,CAACE,KAAK,EAAE;QAClB,IAAI,CAACiC,MAAM,GAAGjB,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQkB,IAAI;MACV,KAAK,KAAK;QACR,OAAO,KAAK;MACd;QACE,MAAM,IAAIC,KAAK,CAAE,sCAAqCD,IAAK,EAAC,CAAC;IACjE;EACF;AACF;AAEAO,MAAM,CAACC,OAAO,GAAG9C,kBAAkB"}