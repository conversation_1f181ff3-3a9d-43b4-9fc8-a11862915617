{"version": 3, "file": "alignment-xform.js", "names": ["Enums", "require", "utils", "BaseXform", "validation", "horizontalValues", "reduce", "p", "v", "horizontal", "value", "undefined", "verticalValues", "vertical", "wrapText", "shrinkToFit", "textRotation", "validInt", "indent", "Math", "max", "readingOrder", "ReadingOrder", "LeftToRight", "RightToLeft", "textRotationXform", "toXml", "tr", "round", "toModel", "AlignmentXform", "tag", "render", "xmlStream", "model", "add<PERSON><PERSON><PERSON>", "openNode", "<PERSON><PERSON><PERSON><PERSON>", "add", "name", "addAttribute", "closeNode", "commit", "rollback", "parseOpen", "node", "valid", "truthy", "attributes", "parseBoolean", "parseInt", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/alignment-xform.js"], "sourcesContent": ["const Enums = require('../../../doc/enums');\n\nconst utils = require('../../../utils/utils');\nconst BaseXform = require('../base-xform');\n\nconst validation = {\n  horizontalValues: [\n    'left',\n    'center',\n    'right',\n    'fill',\n    'centerContinuous',\n    'distributed',\n    'justify',\n  ].reduce((p, v) => {\n    p[v] = true;\n    return p;\n  }, {}),\n  horizontal(value) {\n    return this.horizontalValues[value] ? value : undefined;\n  },\n\n  verticalValues: ['top', 'middle', 'bottom', 'distributed', 'justify'].reduce((p, v) => {\n    p[v] = true;\n    return p;\n  }, {}),\n  vertical(value) {\n    if (value === 'middle') return 'center';\n    return this.verticalValues[value] ? value : undefined;\n  },\n  wrapText(value) {\n    return value ? true : undefined;\n  },\n  shrinkToFit(value) {\n    return value ? true : undefined;\n  },\n  textRotation(value) {\n    switch (value) {\n      case 'vertical':\n        return value;\n      default:\n        value = utils.validInt(value);\n        return value >= -90 && value <= 90 ? value : undefined;\n    }\n  },\n  indent(value) {\n    value = utils.validInt(value);\n    return Math.max(0, value);\n  },\n  readingOrder(value) {\n    switch (value) {\n      case 'ltr':\n        return Enums.ReadingOrder.LeftToRight;\n      case 'rtl':\n        return Enums.ReadingOrder.RightToLeft;\n      default:\n        return undefined;\n    }\n  },\n};\n\nconst textRotationXform = {\n  toXml(textRotation) {\n    textRotation = validation.textRotation(textRotation);\n    if (textRotation) {\n      if (textRotation === 'vertical') {\n        return 255;\n      }\n\n      const tr = Math.round(textRotation);\n      if (tr >= 0 && tr <= 90) {\n        return tr;\n      }\n\n      if (tr < 0 && tr >= -90) {\n        return 90 - tr;\n      }\n    }\n    return undefined;\n  },\n  toModel(textRotation) {\n    const tr = utils.validInt(textRotation);\n    if (tr !== undefined) {\n      if (tr === 255) {\n        return 'vertical';\n      }\n      if (tr >= 0 && tr <= 90) {\n        return tr;\n      }\n      if (tr > 90 && tr <= 180) {\n        return 90 - tr;\n      }\n    }\n    return undefined;\n  },\n};\n\n// Alignment encapsulates translation from style.alignment model to/from xlsx\nclass AlignmentXform extends BaseXform {\n  get tag() {\n    return 'alignment';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.addRollback();\n    xmlStream.openNode('alignment');\n\n    let isValid = false;\n    function add(name, value) {\n      if (value) {\n        xmlStream.addAttribute(name, value);\n        isValid = true;\n      }\n    }\n    add('horizontal', validation.horizontal(model.horizontal));\n    add('vertical', validation.vertical(model.vertical));\n    add('wrapText', validation.wrapText(model.wrapText) ? '1' : false);\n    add('shrinkToFit', validation.shrinkToFit(model.shrinkToFit) ? '1' : false);\n    add('indent', validation.indent(model.indent));\n    add('textRotation', textRotationXform.toXml(model.textRotation));\n    add('readingOrder', validation.readingOrder(model.readingOrder));\n\n    xmlStream.closeNode();\n\n    if (isValid) {\n      xmlStream.commit();\n    } else {\n      xmlStream.rollback();\n    }\n  }\n\n  parseOpen(node) {\n    const model = {};\n\n    let valid = false;\n    function add(truthy, name, value) {\n      if (truthy) {\n        model[name] = value;\n        valid = true;\n      }\n    }\n    add(node.attributes.horizontal, 'horizontal', node.attributes.horizontal);\n    add(\n      node.attributes.vertical,\n      'vertical',\n      node.attributes.vertical === 'center' ? 'middle' : node.attributes.vertical\n    );\n    add(node.attributes.wrapText, 'wrapText', utils.parseBoolean(node.attributes.wrapText));\n    add(node.attributes.shrinkToFit, 'shrinkToFit', utils.parseBoolean(node.attributes.shrinkToFit));\n    add(node.attributes.indent, 'indent', parseInt(node.attributes.indent, 10));\n    add(\n      node.attributes.textRotation,\n      'textRotation',\n      textRotationXform.toModel(node.attributes.textRotation)\n    );\n    add(\n      node.attributes.readingOrder,\n      'readingOrder',\n      node.attributes.readingOrder === '2' ? 'rtl' : 'ltr'\n    );\n\n    this.model = valid ? model : null;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = AlignmentXform;\n"], "mappings": ";;AAAA,MAAMA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAE3C,MAAMC,KAAK,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC7C,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMG,UAAU,GAAG;EACjBC,gBAAgB,EAAE,CAChB,MAAM,EACN,QAAQ,EACR,OAAO,EACP,MAAM,EACN,kBAAkB,EAClB,aAAa,EACb,SAAS,CACV,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACjBD,CAAC,CAACC,CAAC,CAAC,GAAG,IAAI;IACX,OAAOD,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC;EACNE,UAAUA,CAACC,KAAK,EAAE;IAChB,OAAO,IAAI,CAACL,gBAAgB,CAACK,KAAK,CAAC,GAAGA,KAAK,GAAGC,SAAS;EACzD,CAAC;EAEDC,cAAc,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,CAAC,CAACN,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACrFD,CAAC,CAACC,CAAC,CAAC,GAAG,IAAI;IACX,OAAOD,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC;EACNM,QAAQA,CAACH,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,QAAQ,EAAE,OAAO,QAAQ;IACvC,OAAO,IAAI,CAACE,cAAc,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAGC,SAAS;EACvD,CAAC;EACDG,QAAQA,CAACJ,KAAK,EAAE;IACd,OAAOA,KAAK,GAAG,IAAI,GAAGC,SAAS;EACjC,CAAC;EACDI,WAAWA,CAACL,KAAK,EAAE;IACjB,OAAOA,KAAK,GAAG,IAAI,GAAGC,SAAS;EACjC,CAAC;EACDK,YAAYA,CAACN,KAAK,EAAE;IAClB,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAOA,KAAK;MACd;QACEA,KAAK,GAAGR,KAAK,CAACe,QAAQ,CAACP,KAAK,CAAC;QAC7B,OAAOA,KAAK,IAAI,CAAC,EAAE,IAAIA,KAAK,IAAI,EAAE,GAAGA,KAAK,GAAGC,SAAS;IAC1D;EACF,CAAC;EACDO,MAAMA,CAACR,KAAK,EAAE;IACZA,KAAK,GAAGR,KAAK,CAACe,QAAQ,CAACP,KAAK,CAAC;IAC7B,OAAOS,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEV,KAAK,CAAC;EAC3B,CAAC;EACDW,YAAYA,CAACX,KAAK,EAAE;IAClB,QAAQA,KAAK;MACX,KAAK,KAAK;QACR,OAAOV,KAAK,CAACsB,YAAY,CAACC,WAAW;MACvC,KAAK,KAAK;QACR,OAAOvB,KAAK,CAACsB,YAAY,CAACE,WAAW;MACvC;QACE,OAAOb,SAAS;IACpB;EACF;AACF,CAAC;AAED,MAAMc,iBAAiB,GAAG;EACxBC,KAAKA,CAACV,YAAY,EAAE;IAClBA,YAAY,GAAGZ,UAAU,CAACY,YAAY,CAACA,YAAY,CAAC;IACpD,IAAIA,YAAY,EAAE;MAChB,IAAIA,YAAY,KAAK,UAAU,EAAE;QAC/B,OAAO,GAAG;MACZ;MAEA,MAAMW,EAAE,GAAGR,IAAI,CAACS,KAAK,CAACZ,YAAY,CAAC;MACnC,IAAIW,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,EAAE,EAAE;QACvB,OAAOA,EAAE;MACX;MAEA,IAAIA,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE,EAAE;QACvB,OAAO,EAAE,GAAGA,EAAE;MAChB;IACF;IACA,OAAOhB,SAAS;EAClB,CAAC;EACDkB,OAAOA,CAACb,YAAY,EAAE;IACpB,MAAMW,EAAE,GAAGzB,KAAK,CAACe,QAAQ,CAACD,YAAY,CAAC;IACvC,IAAIW,EAAE,KAAKhB,SAAS,EAAE;MACpB,IAAIgB,EAAE,KAAK,GAAG,EAAE;QACd,OAAO,UAAU;MACnB;MACA,IAAIA,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,EAAE,EAAE;QACvB,OAAOA,EAAE;MACX;MACA,IAAIA,EAAE,GAAG,EAAE,IAAIA,EAAE,IAAI,GAAG,EAAE;QACxB,OAAO,EAAE,GAAGA,EAAE;MAChB;IACF;IACA,OAAOhB,SAAS;EAClB;AACF,CAAC;;AAED;AACA,MAAMmB,cAAc,SAAS3B,SAAS,CAAC;EACrC,IAAI4B,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,WAAW,CAAC,CAAC;IACvBF,SAAS,CAACG,QAAQ,CAAC,WAAW,CAAC;IAE/B,IAAIC,OAAO,GAAG,KAAK;IACnB,SAASC,GAAGA,CAACC,IAAI,EAAE7B,KAAK,EAAE;MACxB,IAAIA,KAAK,EAAE;QACTuB,SAAS,CAACO,YAAY,CAACD,IAAI,EAAE7B,KAAK,CAAC;QACnC2B,OAAO,GAAG,IAAI;MAChB;IACF;IACAC,GAAG,CAAC,YAAY,EAAElC,UAAU,CAACK,UAAU,CAACyB,KAAK,CAACzB,UAAU,CAAC,CAAC;IAC1D6B,GAAG,CAAC,UAAU,EAAElC,UAAU,CAACS,QAAQ,CAACqB,KAAK,CAACrB,QAAQ,CAAC,CAAC;IACpDyB,GAAG,CAAC,UAAU,EAAElC,UAAU,CAACU,QAAQ,CAACoB,KAAK,CAACpB,QAAQ,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;IAClEwB,GAAG,CAAC,aAAa,EAAElC,UAAU,CAACW,WAAW,CAACmB,KAAK,CAACnB,WAAW,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;IAC3EuB,GAAG,CAAC,QAAQ,EAAElC,UAAU,CAACc,MAAM,CAACgB,KAAK,CAAChB,MAAM,CAAC,CAAC;IAC9CoB,GAAG,CAAC,cAAc,EAAEb,iBAAiB,CAACC,KAAK,CAACQ,KAAK,CAAClB,YAAY,CAAC,CAAC;IAChEsB,GAAG,CAAC,cAAc,EAAElC,UAAU,CAACiB,YAAY,CAACa,KAAK,CAACb,YAAY,CAAC,CAAC;IAEhEY,SAAS,CAACQ,SAAS,CAAC,CAAC;IAErB,IAAIJ,OAAO,EAAE;MACXJ,SAAS,CAACS,MAAM,CAAC,CAAC;IACpB,CAAC,MAAM;MACLT,SAAS,CAACU,QAAQ,CAAC,CAAC;IACtB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,MAAMX,KAAK,GAAG,CAAC,CAAC;IAEhB,IAAIY,KAAK,GAAG,KAAK;IACjB,SAASR,GAAGA,CAACS,MAAM,EAAER,IAAI,EAAE7B,KAAK,EAAE;MAChC,IAAIqC,MAAM,EAAE;QACVb,KAAK,CAACK,IAAI,CAAC,GAAG7B,KAAK;QACnBoC,KAAK,GAAG,IAAI;MACd;IACF;IACAR,GAAG,CAACO,IAAI,CAACG,UAAU,CAACvC,UAAU,EAAE,YAAY,EAAEoC,IAAI,CAACG,UAAU,CAACvC,UAAU,CAAC;IACzE6B,GAAG,CACDO,IAAI,CAACG,UAAU,CAACnC,QAAQ,EACxB,UAAU,EACVgC,IAAI,CAACG,UAAU,CAACnC,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAGgC,IAAI,CAACG,UAAU,CAACnC,QACrE,CAAC;IACDyB,GAAG,CAACO,IAAI,CAACG,UAAU,CAAClC,QAAQ,EAAE,UAAU,EAAEZ,KAAK,CAAC+C,YAAY,CAACJ,IAAI,CAACG,UAAU,CAAClC,QAAQ,CAAC,CAAC;IACvFwB,GAAG,CAACO,IAAI,CAACG,UAAU,CAACjC,WAAW,EAAE,aAAa,EAAEb,KAAK,CAAC+C,YAAY,CAACJ,IAAI,CAACG,UAAU,CAACjC,WAAW,CAAC,CAAC;IAChGuB,GAAG,CAACO,IAAI,CAACG,UAAU,CAAC9B,MAAM,EAAE,QAAQ,EAAEgC,QAAQ,CAACL,IAAI,CAACG,UAAU,CAAC9B,MAAM,EAAE,EAAE,CAAC,CAAC;IAC3EoB,GAAG,CACDO,IAAI,CAACG,UAAU,CAAChC,YAAY,EAC5B,cAAc,EACdS,iBAAiB,CAACI,OAAO,CAACgB,IAAI,CAACG,UAAU,CAAChC,YAAY,CACxD,CAAC;IACDsB,GAAG,CACDO,IAAI,CAACG,UAAU,CAAC3B,YAAY,EAC5B,cAAc,EACdwB,IAAI,CAACG,UAAU,CAAC3B,YAAY,KAAK,GAAG,GAAG,KAAK,GAAG,KACjD,CAAC;IAED,IAAI,CAACa,KAAK,GAAGY,KAAK,GAAGZ,KAAK,GAAG,IAAI;EACnC;EAEAiB,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGxB,cAAc"}