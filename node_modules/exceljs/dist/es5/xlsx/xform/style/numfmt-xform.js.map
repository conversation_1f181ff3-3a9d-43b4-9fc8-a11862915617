{"version": 3, "file": "numfmt-xform.js", "names": ["_", "require", "defaultNumFormats", "BaseXform", "hashDefaultFormats", "hash", "each", "dnf", "id", "f", "parseInt", "defaultFmtHash", "NumFmtXform", "constructor", "formatCode", "tag", "render", "xmlStream", "model", "leafNode", "numFmtId", "parseOpen", "node", "name", "attributes", "replace", "parseText", "parseClose", "getDefaultFmtId", "getDefaultFmtCode", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/numfmt-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\nconst defaultNumFormats = require('../../defaultnumformats');\n\nconst BaseXform = require('../base-xform');\n\nfunction hashDefaultFormats() {\n  const hash = {};\n  _.each(defaultNumFormats, (dnf, id) => {\n    if (dnf.f) {\n      hash[dnf.f] = parseInt(id, 10);\n    }\n    // at some point, add the other cultures here...\n  });\n  return hash;\n}\nconst defaultFmtHash = hashDefaultFormats();\n\n// NumFmt encapsulates translation between number format and xlsx\nclass NumFmtXform extends BaseXform {\n  constructor(id, formatCode) {\n    super();\n\n    this.id = id;\n    this.formatCode = formatCode;\n  }\n\n  get tag() {\n    return 'numFmt';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode('numFmt', {numFmtId: model.id, formatCode: model.formatCode});\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case 'numFmt':\n        this.model = {\n          id: parseInt(node.attributes.numFmtId, 10),\n          formatCode: node.attributes.formatCode.replace(/[\\\\](.)/g, '$1'),\n        };\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nNumFmtXform.getDefaultFmtId = function getDefaultFmtId(formatCode) {\n  return defaultFmtHash[formatCode];\n};\n\nNumFmtXform.getDefaultFmtCode = function getDefaultFmtCode(numFmtId) {\n  return defaultNumFormats[numFmtId] && defaultNumFormats[numFmtId].f;\n};\n\nmodule.exports = NumFmtXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC9C,MAAMC,iBAAiB,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAE5D,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;AAE1C,SAASG,kBAAkBA,CAAA,EAAG;EAC5B,MAAMC,IAAI,GAAG,CAAC,CAAC;EACfL,CAAC,CAACM,IAAI,CAACJ,iBAAiB,EAAE,CAACK,GAAG,EAAEC,EAAE,KAAK;IACrC,IAAID,GAAG,CAACE,CAAC,EAAE;MACTJ,IAAI,CAACE,GAAG,CAACE,CAAC,CAAC,GAAGC,QAAQ,CAACF,EAAE,EAAE,EAAE,CAAC;IAChC;IACA;EACF,CAAC,CAAC;;EACF,OAAOH,IAAI;AACb;AACA,MAAMM,cAAc,GAAGP,kBAAkB,CAAC,CAAC;;AAE3C;AACA,MAAMQ,WAAW,SAAST,SAAS,CAAC;EAClCU,WAAWA,CAACL,EAAE,EAAEM,UAAU,EAAE;IAC1B,KAAK,CAAC,CAAC;IAEP,IAAI,CAACN,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACM,UAAU,GAAGA,UAAU;EAC9B;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,QAAQ;EACjB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,QAAQ,EAAE;MAACC,QAAQ,EAAEF,KAAK,CAACV,EAAE;MAAEM,UAAU,EAAEI,KAAK,CAACJ;IAAU,CAAC,CAAC;EAClF;EAEAO,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,QAAQ;QACX,IAAI,CAACL,KAAK,GAAG;UACXV,EAAE,EAAEE,QAAQ,CAACY,IAAI,CAACE,UAAU,CAACJ,QAAQ,EAAE,EAAE,CAAC;UAC1CN,UAAU,EAAEQ,IAAI,CAACE,UAAU,CAACV,UAAU,CAACW,OAAO,CAAC,UAAU,EAAE,IAAI;QACjE,CAAC;QACD,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAf,WAAW,CAACgB,eAAe,GAAG,SAASA,eAAeA,CAACd,UAAU,EAAE;EACjE,OAAOH,cAAc,CAACG,UAAU,CAAC;AACnC,CAAC;AAEDF,WAAW,CAACiB,iBAAiB,GAAG,SAASA,iBAAiBA,CAACT,QAAQ,EAAE;EACnE,OAAOlB,iBAAiB,CAACkB,QAAQ,CAAC,IAAIlB,iBAAiB,CAACkB,QAAQ,CAAC,CAACX,CAAC;AACrE,CAAC;AAEDqB,MAAM,CAACC,OAAO,GAAGnB,WAAW"}