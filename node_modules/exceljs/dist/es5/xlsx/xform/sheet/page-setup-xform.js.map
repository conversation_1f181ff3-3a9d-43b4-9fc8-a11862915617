{"version": 3, "file": "page-setup-xform.js", "names": ["_", "require", "BaseXform", "booleanToXml", "model", "undefined", "pageOrderToXml", "cellCommentsToXml", "errorsToXml", "pageSizeToModel", "value", "parseInt", "PageSetupXform", "tag", "render", "xmlStream", "attributes", "paperSize", "orientation", "horizontalDpi", "verticalDpi", "pageOrder", "blackAndWhite", "draft", "cellComments", "errors", "scale", "fitToWidth", "fitToHeight", "firstPageNumber", "useFirstPageNumber", "usePrinterDefaults", "copies", "some", "leafNode", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/page-setup-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\nconst BaseXform = require('../base-xform');\n\nfunction booleanToXml(model) {\n  return model ? '1' : undefined;\n}\nfunction pageOrderToXml(model) {\n  switch (model) {\n    case 'overThenDown':\n      return model;\n    default:\n      return undefined;\n  }\n}\nfunction cellCommentsToXml(model) {\n  switch (model) {\n    case 'atEnd':\n    case 'asDisplyed':\n      return model;\n    default:\n      return undefined;\n  }\n}\nfunction errorsToXml(model) {\n  switch (model) {\n    case 'dash':\n    case 'blank':\n    case 'NA':\n      return model;\n    default:\n      return undefined;\n  }\n}\nfunction pageSizeToModel(value) {\n  return value !== undefined ? parseInt(value, 10) : undefined;\n}\n\nclass PageSetupXform extends BaseXform {\n  get tag() {\n    return 'pageSetup';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      const attributes = {\n        paperSize: model.paperSize,\n        orientation: model.orientation,\n        horizontalDpi: model.horizontalDpi,\n        verticalDpi: model.verticalDpi,\n        pageOrder: pageOrderToXml(model.pageOrder),\n        blackAndWhite: booleanToXml(model.blackAndWhite),\n        draft: booleanToXml(model.draft),\n        cellComments: cellCommentsToXml(model.cellComments),\n        errors: errorsToXml(model.errors),\n        scale: model.scale,\n        fitToWidth: model.fitToWidth,\n        fitToHeight: model.fitToHeight,\n        firstPageNumber: model.firstPageNumber,\n        useFirstPageNumber: booleanToXml(model.firstPageNumber),\n        usePrinterDefaults: booleanToXml(model.usePrinterDefaults),\n        copies: model.copies,\n      };\n      if (_.some(attributes, value => value !== undefined)) {\n        xmlStream.leafNode(this.tag, attributes);\n      }\n    }\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          paperSize: pageSizeToModel(node.attributes.paperSize),\n          orientation: node.attributes.orientation || 'portrait',\n          horizontalDpi: parseInt(node.attributes.horizontalDpi || '4294967295', 10),\n          verticalDpi: parseInt(node.attributes.verticalDpi || '4294967295', 10),\n          pageOrder: node.attributes.pageOrder || 'downThenOver',\n          blackAndWhite: node.attributes.blackAndWhite === '1',\n          draft: node.attributes.draft === '1',\n          cellComments: node.attributes.cellComments || 'None',\n          errors: node.attributes.errors || 'displayed',\n          scale: parseInt(node.attributes.scale || '100', 10),\n          fitToWidth: parseInt(node.attributes.fitToWidth || '1', 10),\n          fitToHeight: parseInt(node.attributes.fitToHeight || '1', 10),\n          firstPageNumber: parseInt(node.attributes.firstPageNumber || '1', 10),\n          useFirstPageNumber: node.attributes.useFirstPageNumber === '1',\n          usePrinterDefaults: node.attributes.usePrinterDefaults === '1',\n          copies: parseInt(node.attributes.copies || '1', 10),\n        };\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = PageSetupXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC9C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,SAASE,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAOA,KAAK,GAAG,GAAG,GAAGC,SAAS;AAChC;AACA,SAASC,cAAcA,CAACF,KAAK,EAAE;EAC7B,QAAQA,KAAK;IACX,KAAK,cAAc;MACjB,OAAOA,KAAK;IACd;MACE,OAAOC,SAAS;EACpB;AACF;AACA,SAASE,iBAAiBA,CAACH,KAAK,EAAE;EAChC,QAAQA,KAAK;IACX,KAAK,OAAO;IACZ,KAAK,YAAY;MACf,OAAOA,KAAK;IACd;MACE,OAAOC,SAAS;EACpB;AACF;AACA,SAASG,WAAWA,CAACJ,KAAK,EAAE;EAC1B,QAAQA,KAAK;IACX,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,IAAI;MACP,OAAOA,KAAK;IACd;MACE,OAAOC,SAAS;EACpB;AACF;AACA,SAASI,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,KAAKL,SAAS,GAAGM,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC,GAAGL,SAAS;AAC9D;AAEA,MAAMO,cAAc,SAASV,SAAS,CAAC;EACrC,IAAIW,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,MAAMA,CAACC,SAAS,EAAEX,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACT,MAAMY,UAAU,GAAG;QACjBC,SAAS,EAAEb,KAAK,CAACa,SAAS;QAC1BC,WAAW,EAAEd,KAAK,CAACc,WAAW;QAC9BC,aAAa,EAAEf,KAAK,CAACe,aAAa;QAClCC,WAAW,EAAEhB,KAAK,CAACgB,WAAW;QAC9BC,SAAS,EAAEf,cAAc,CAACF,KAAK,CAACiB,SAAS,CAAC;QAC1CC,aAAa,EAAEnB,YAAY,CAACC,KAAK,CAACkB,aAAa,CAAC;QAChDC,KAAK,EAAEpB,YAAY,CAACC,KAAK,CAACmB,KAAK,CAAC;QAChCC,YAAY,EAAEjB,iBAAiB,CAACH,KAAK,CAACoB,YAAY,CAAC;QACnDC,MAAM,EAAEjB,WAAW,CAACJ,KAAK,CAACqB,MAAM,CAAC;QACjCC,KAAK,EAAEtB,KAAK,CAACsB,KAAK;QAClBC,UAAU,EAAEvB,KAAK,CAACuB,UAAU;QAC5BC,WAAW,EAAExB,KAAK,CAACwB,WAAW;QAC9BC,eAAe,EAAEzB,KAAK,CAACyB,eAAe;QACtCC,kBAAkB,EAAE3B,YAAY,CAACC,KAAK,CAACyB,eAAe,CAAC;QACvDE,kBAAkB,EAAE5B,YAAY,CAACC,KAAK,CAAC2B,kBAAkB,CAAC;QAC1DC,MAAM,EAAE5B,KAAK,CAAC4B;MAChB,CAAC;MACD,IAAIhC,CAAC,CAACiC,IAAI,CAACjB,UAAU,EAAEN,KAAK,IAAIA,KAAK,KAAKL,SAAS,CAAC,EAAE;QACpDU,SAAS,CAACmB,QAAQ,CAAC,IAAI,CAACrB,GAAG,EAAEG,UAAU,CAAC;MAC1C;IACF;EACF;EAEAmB,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACxB,GAAG;QACX,IAAI,CAACT,KAAK,GAAG;UACXa,SAAS,EAAER,eAAe,CAAC2B,IAAI,CAACpB,UAAU,CAACC,SAAS,CAAC;UACrDC,WAAW,EAAEkB,IAAI,CAACpB,UAAU,CAACE,WAAW,IAAI,UAAU;UACtDC,aAAa,EAAER,QAAQ,CAACyB,IAAI,CAACpB,UAAU,CAACG,aAAa,IAAI,YAAY,EAAE,EAAE,CAAC;UAC1EC,WAAW,EAAET,QAAQ,CAACyB,IAAI,CAACpB,UAAU,CAACI,WAAW,IAAI,YAAY,EAAE,EAAE,CAAC;UACtEC,SAAS,EAAEe,IAAI,CAACpB,UAAU,CAACK,SAAS,IAAI,cAAc;UACtDC,aAAa,EAAEc,IAAI,CAACpB,UAAU,CAACM,aAAa,KAAK,GAAG;UACpDC,KAAK,EAAEa,IAAI,CAACpB,UAAU,CAACO,KAAK,KAAK,GAAG;UACpCC,YAAY,EAAEY,IAAI,CAACpB,UAAU,CAACQ,YAAY,IAAI,MAAM;UACpDC,MAAM,EAAEW,IAAI,CAACpB,UAAU,CAACS,MAAM,IAAI,WAAW;UAC7CC,KAAK,EAAEf,QAAQ,CAACyB,IAAI,CAACpB,UAAU,CAACU,KAAK,IAAI,KAAK,EAAE,EAAE,CAAC;UACnDC,UAAU,EAAEhB,QAAQ,CAACyB,IAAI,CAACpB,UAAU,CAACW,UAAU,IAAI,GAAG,EAAE,EAAE,CAAC;UAC3DC,WAAW,EAAEjB,QAAQ,CAACyB,IAAI,CAACpB,UAAU,CAACY,WAAW,IAAI,GAAG,EAAE,EAAE,CAAC;UAC7DC,eAAe,EAAElB,QAAQ,CAACyB,IAAI,CAACpB,UAAU,CAACa,eAAe,IAAI,GAAG,EAAE,EAAE,CAAC;UACrEC,kBAAkB,EAAEM,IAAI,CAACpB,UAAU,CAACc,kBAAkB,KAAK,GAAG;UAC9DC,kBAAkB,EAAEK,IAAI,CAACpB,UAAU,CAACe,kBAAkB,KAAK,GAAG;UAC9DC,MAAM,EAAErB,QAAQ,CAACyB,IAAI,CAACpB,UAAU,CAACgB,MAAM,IAAI,GAAG,EAAE,EAAE;QACpD,CAAC;QACD,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAM,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG7B,cAAc"}