{"version": 3, "file": "header-footer-xform.js", "names": ["BaseXform", "require", "HeaderFooterXform", "tag", "render", "xmlStream", "model", "add<PERSON><PERSON><PERSON>", "createTag", "openNode", "differentFirst", "addAttribute", "differentOddEven", "<PERSON><PERSON><PERSON><PERSON>", "leafNode", "oddFooter", "<PERSON><PERSON><PERSON><PERSON>", "evenFooter", "firstHeader", "firstFooter", "closeNode", "commit", "rollback", "parseOpen", "node", "name", "attributes", "parseInt", "currentNode", "parseText", "text", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/header-footer-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass HeaderFooterXform extends BaseXform {\n  get tag() {\n    return 'headerFooter';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      xmlStream.addRollback();\n\n      let createTag = false;\n\n      xmlStream.openNode('headerFooter');\n      if (model.differentFirst) {\n        xmlStream.addAttribute('differentFirst', '1');\n        createTag = true;\n      }\n      if (model.differentOddEven) {\n        xmlStream.addAttribute('differentOddEven', '1');\n        createTag = true;\n      }\n      if (model.oddHeader && typeof model.oddHeader === 'string') {\n        xmlStream.leafNode('oddHeader', null, model.oddHeader);\n        createTag = true;\n      }\n      if (model.oddFooter && typeof model.oddFooter === 'string') {\n        xmlStream.leafNode('oddFooter', null, model.oddFooter);\n        createTag = true;\n      }\n      if (model.evenHeader && typeof model.evenHeader === 'string') {\n        xmlStream.leafNode('evenHeader', null, model.evenHeader);\n        createTag = true;\n      }\n      if (model.evenFooter && typeof model.evenFooter === 'string') {\n        xmlStream.leafNode('evenFooter', null, model.evenFooter);\n        createTag = true;\n      }\n      if (model.firstHeader && typeof model.firstHeader === 'string') {\n        xmlStream.leafNode('firstHeader', null, model.firstHeader);\n        createTag = true;\n      }\n      if (model.firstFooter && typeof model.firstFooter === 'string') {\n        xmlStream.leafNode('firstFooter', null, model.firstFooter);\n        createTag = true;\n      }\n\n      if (createTag) {\n        xmlStream.closeNode();\n        xmlStream.commit();\n      } else {\n        xmlStream.rollback();\n      }\n    }\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case 'headerFooter':\n        this.model = {};\n        if (node.attributes.differentFirst) {\n          this.model.differentFirst = parseInt(node.attributes.differentFirst, 0) === 1;\n        }\n        if (node.attributes.differentOddEven) {\n          this.model.differentOddEven = parseInt(node.attributes.differentOddEven, 0) === 1;\n        }\n        return true;\n\n      case 'oddHeader':\n        this.currentNode = 'oddHeader';\n        return true;\n\n      case 'oddFooter':\n        this.currentNode = 'oddFooter';\n        return true;\n\n      case 'evenHeader':\n        this.currentNode = 'evenHeader';\n        return true;\n\n      case 'evenFooter':\n        this.currentNode = 'evenFooter';\n        return true;\n\n      case 'firstHeader':\n        this.currentNode = 'firstHeader';\n        return true;\n\n      case 'firstFooter':\n        this.currentNode = 'firstFooter';\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    switch (this.currentNode) {\n      case 'oddHeader':\n        this.model.oddHeader = text;\n        break;\n\n      case 'oddFooter':\n        this.model.oddFooter = text;\n        break;\n\n      case 'evenHeader':\n        this.model.evenHeader = text;\n        break;\n\n      case 'evenFooter':\n        this.model.evenFooter = text;\n        break;\n\n      case 'firstHeader':\n        this.model.firstHeader = text;\n        break;\n\n      case 'firstFooter':\n        this.model.firstFooter = text;\n        break;\n\n      default:\n        break;\n    }\n  }\n\n  parseClose() {\n    switch (this.currentNode) {\n      case 'oddHeader':\n      case 'oddFooter':\n      case 'evenHeader':\n      case 'evenFooter':\n      case 'firstHeader':\n      case 'firstFooter':\n        this.currentNode = undefined;\n        return true;\n\n      default:\n        return false;\n    }\n  }\n}\n\nmodule.exports = HeaderFooterXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,iBAAiB,SAASF,SAAS,CAAC;EACxC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACTD,SAAS,CAACE,WAAW,CAAC,CAAC;MAEvB,IAAIC,SAAS,GAAG,KAAK;MAErBH,SAAS,CAACI,QAAQ,CAAC,cAAc,CAAC;MAClC,IAAIH,KAAK,CAACI,cAAc,EAAE;QACxBL,SAAS,CAACM,YAAY,CAAC,gBAAgB,EAAE,GAAG,CAAC;QAC7CH,SAAS,GAAG,IAAI;MAClB;MACA,IAAIF,KAAK,CAACM,gBAAgB,EAAE;QAC1BP,SAAS,CAACM,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC;QAC/CH,SAAS,GAAG,IAAI;MAClB;MACA,IAAIF,KAAK,CAACO,SAAS,IAAI,OAAOP,KAAK,CAACO,SAAS,KAAK,QAAQ,EAAE;QAC1DR,SAAS,CAACS,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAER,KAAK,CAACO,SAAS,CAAC;QACtDL,SAAS,GAAG,IAAI;MAClB;MACA,IAAIF,KAAK,CAACS,SAAS,IAAI,OAAOT,KAAK,CAACS,SAAS,KAAK,QAAQ,EAAE;QAC1DV,SAAS,CAACS,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAER,KAAK,CAACS,SAAS,CAAC;QACtDP,SAAS,GAAG,IAAI;MAClB;MACA,IAAIF,KAAK,CAACU,UAAU,IAAI,OAAOV,KAAK,CAACU,UAAU,KAAK,QAAQ,EAAE;QAC5DX,SAAS,CAACS,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAER,KAAK,CAACU,UAAU,CAAC;QACxDR,SAAS,GAAG,IAAI;MAClB;MACA,IAAIF,KAAK,CAACW,UAAU,IAAI,OAAOX,KAAK,CAACW,UAAU,KAAK,QAAQ,EAAE;QAC5DZ,SAAS,CAACS,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAER,KAAK,CAACW,UAAU,CAAC;QACxDT,SAAS,GAAG,IAAI;MAClB;MACA,IAAIF,KAAK,CAACY,WAAW,IAAI,OAAOZ,KAAK,CAACY,WAAW,KAAK,QAAQ,EAAE;QAC9Db,SAAS,CAACS,QAAQ,CAAC,aAAa,EAAE,IAAI,EAAER,KAAK,CAACY,WAAW,CAAC;QAC1DV,SAAS,GAAG,IAAI;MAClB;MACA,IAAIF,KAAK,CAACa,WAAW,IAAI,OAAOb,KAAK,CAACa,WAAW,KAAK,QAAQ,EAAE;QAC9Dd,SAAS,CAACS,QAAQ,CAAC,aAAa,EAAE,IAAI,EAAER,KAAK,CAACa,WAAW,CAAC;QAC1DX,SAAS,GAAG,IAAI;MAClB;MAEA,IAAIA,SAAS,EAAE;QACbH,SAAS,CAACe,SAAS,CAAC,CAAC;QACrBf,SAAS,CAACgB,MAAM,CAAC,CAAC;MACpB,CAAC,MAAM;QACLhB,SAAS,CAACiB,QAAQ,CAAC,CAAC;MACtB;IACF;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,cAAc;QACjB,IAAI,CAACnB,KAAK,GAAG,CAAC,CAAC;QACf,IAAIkB,IAAI,CAACE,UAAU,CAAChB,cAAc,EAAE;UAClC,IAAI,CAACJ,KAAK,CAACI,cAAc,GAAGiB,QAAQ,CAACH,IAAI,CAACE,UAAU,CAAChB,cAAc,EAAE,CAAC,CAAC,KAAK,CAAC;QAC/E;QACA,IAAIc,IAAI,CAACE,UAAU,CAACd,gBAAgB,EAAE;UACpC,IAAI,CAACN,KAAK,CAACM,gBAAgB,GAAGe,QAAQ,CAACH,IAAI,CAACE,UAAU,CAACd,gBAAgB,EAAE,CAAC,CAAC,KAAK,CAAC;QACnF;QACA,OAAO,IAAI;MAEb,KAAK,WAAW;QACd,IAAI,CAACgB,WAAW,GAAG,WAAW;QAC9B,OAAO,IAAI;MAEb,KAAK,WAAW;QACd,IAAI,CAACA,WAAW,GAAG,WAAW;QAC9B,OAAO,IAAI;MAEb,KAAK,YAAY;QACf,IAAI,CAACA,WAAW,GAAG,YAAY;QAC/B,OAAO,IAAI;MAEb,KAAK,YAAY;QACf,IAAI,CAACA,WAAW,GAAG,YAAY;QAC/B,OAAO,IAAI;MAEb,KAAK,aAAa;QAChB,IAAI,CAACA,WAAW,GAAG,aAAa;QAChC,OAAO,IAAI;MAEb,KAAK,aAAa;QAChB,IAAI,CAACA,WAAW,GAAG,aAAa;QAChC,OAAO,IAAI;MAEb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQ,IAAI,CAACF,WAAW;MACtB,KAAK,WAAW;QACd,IAAI,CAACtB,KAAK,CAACO,SAAS,GAAGiB,IAAI;QAC3B;MAEF,KAAK,WAAW;QACd,IAAI,CAACxB,KAAK,CAACS,SAAS,GAAGe,IAAI;QAC3B;MAEF,KAAK,YAAY;QACf,IAAI,CAACxB,KAAK,CAACU,UAAU,GAAGc,IAAI;QAC5B;MAEF,KAAK,YAAY;QACf,IAAI,CAACxB,KAAK,CAACW,UAAU,GAAGa,IAAI;QAC5B;MAEF,KAAK,aAAa;QAChB,IAAI,CAACxB,KAAK,CAACY,WAAW,GAAGY,IAAI;QAC7B;MAEF,KAAK,aAAa;QAChB,IAAI,CAACxB,KAAK,CAACa,WAAW,GAAGW,IAAI;QAC7B;MAEF;QACE;IACJ;EACF;EAEAC,UAAUA,CAAA,EAAG;IACX,QAAQ,IAAI,CAACH,WAAW;MACtB,KAAK,WAAW;MAChB,KAAK,WAAW;MAChB,KAAK,YAAY;MACjB,KAAK,YAAY;MACjB,KAAK,aAAa;MAClB,KAAK,aAAa;QAChB,IAAI,CAACA,WAAW,GAAGI,SAAS;QAC5B,OAAO,IAAI;MAEb;QACE,OAAO,KAAK;IAChB;EACF;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGhC,iBAAiB"}