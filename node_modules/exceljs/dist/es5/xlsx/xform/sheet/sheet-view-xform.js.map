{"version": 3, "file": "sheet-view-xform.js", "names": ["co<PERSON><PERSON><PERSON>", "require", "BaseXform", "VIEW_STATES", "frozen", "frozenSplit", "split", "SheetViewXform", "tag", "prepare", "model", "state", "render", "xmlStream", "openNode", "workbookViewId", "add", "name", "value", "included", "addAttribute", "rightToLeft", "tabSelected", "showRuler", "showRowColHeaders", "showGridLines", "zoomScale", "zoomScaleNormal", "style", "topLeftCell", "xSplit", "ySplit", "activePane", "get<PERSON><PERSON><PERSON>", "address", "leafNode", "undefined", "pane", "activeCell", "sqref", "closeNode", "parseOpen", "node", "sheetView", "parseInt", "attributes", "view", "selections", "parseText", "parseClose", "selection", "topLeft", "reconcile", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/sheet-view-xform.js"], "sourcesContent": ["const colCache = require('../../../utils/col-cache');\nconst BaseXform = require('../base-xform');\n\nconst VIEW_STATES = {\n  frozen: 'frozen',\n  frozenSplit: 'frozen',\n  split: 'split',\n};\n\nclass SheetViewXform extends BaseXform {\n  get tag() {\n    return 'sheetView';\n  }\n\n  prepare(model) {\n    switch (model.state) {\n      case 'frozen':\n      case 'split':\n        break;\n      default:\n        model.state = 'normal';\n        break;\n    }\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode('sheetView', {\n      workbookViewId: model.workbookViewId || 0,\n    });\n    const add = function(name, value, included) {\n      if (included) {\n        xmlStream.addAttribute(name, value);\n      }\n    };\n    add('rightToLeft', '1', model.rightToLeft === true);\n    add('tabSelected', '1', model.tabSelected);\n    add('showRuler', '0', model.showRuler === false);\n    add('showRowColHeaders', '0', model.showRowColHeaders === false);\n    add('showGridLines', '0', model.showGridLines === false);\n    add('zoomScale', model.zoomScale, model.zoomScale);\n    add('zoomScaleNormal', model.zoomScaleNormal, model.zoomScaleNormal);\n    add('view', model.style, model.style);\n\n    let topLeftCell;\n    let xSplit;\n    let ySplit;\n    let activePane;\n    switch (model.state) {\n      case 'frozen':\n        xSplit = model.xSplit || 0;\n        ySplit = model.ySplit || 0;\n        topLeftCell = model.topLeftCell || colCache.getAddress(ySplit + 1, xSplit + 1).address;\n        activePane =\n          (model.xSplit && model.ySplit && 'bottomRight') ||\n          (model.xSplit && 'topRight') ||\n          'bottomLeft';\n\n        xmlStream.leafNode('pane', {\n          xSplit: model.xSplit || undefined,\n          ySplit: model.ySplit || undefined,\n          topLeftCell,\n          activePane,\n          state: 'frozen',\n        });\n        xmlStream.leafNode('selection', {\n          pane: activePane,\n          activeCell: model.activeCell,\n          sqref: model.activeCell,\n        });\n        break;\n      case 'split':\n        if (model.activePane === 'topLeft') {\n          model.activePane = undefined;\n        }\n        xmlStream.leafNode('pane', {\n          xSplit: model.xSplit || undefined,\n          ySplit: model.ySplit || undefined,\n          topLeftCell: model.topLeftCell,\n          activePane: model.activePane,\n        });\n        xmlStream.leafNode('selection', {\n          pane: model.activePane,\n          activeCell: model.activeCell,\n          sqref: model.activeCell,\n        });\n        break;\n      case 'normal':\n        if (model.activeCell) {\n          xmlStream.leafNode('selection', {\n            activeCell: model.activeCell,\n            sqref: model.activeCell,\n          });\n        }\n        break;\n      default:\n        break;\n    }\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case 'sheetView':\n        this.sheetView = {\n          workbookViewId: parseInt(node.attributes.workbookViewId, 10),\n          rightToLeft: node.attributes.rightToLeft === '1',\n          tabSelected: node.attributes.tabSelected === '1',\n          showRuler: !(node.attributes.showRuler === '0'),\n          showRowColHeaders: !(node.attributes.showRowColHeaders === '0'),\n          showGridLines: !(node.attributes.showGridLines === '0'),\n          zoomScale: parseInt(node.attributes.zoomScale || '100', 10),\n          zoomScaleNormal: parseInt(node.attributes.zoomScaleNormal || '100', 10),\n          style: node.attributes.view,\n        };\n        this.pane = undefined;\n        this.selections = {};\n        return true;\n\n      case 'pane':\n        this.pane = {\n          xSplit: parseInt(node.attributes.xSplit || '0', 10),\n          ySplit: parseInt(node.attributes.ySplit || '0', 10),\n          topLeftCell: node.attributes.topLeftCell,\n          activePane: node.attributes.activePane || 'topLeft',\n          state: node.attributes.state,\n        };\n        return true;\n\n      case 'selection': {\n        const name = node.attributes.pane || 'topLeft';\n        this.selections[name] = {\n          pane: name,\n          activeCell: node.attributes.activeCell,\n        };\n        return true;\n      }\n\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    let model;\n    let selection;\n    switch (name) {\n      case 'sheetView':\n        if (this.sheetView && this.pane) {\n          model = this.model = {\n            workbookViewId: this.sheetView.workbookViewId,\n            rightToLeft: this.sheetView.rightToLeft,\n            state: VIEW_STATES[this.pane.state] || 'split', // split is default\n            xSplit: this.pane.xSplit,\n            ySplit: this.pane.ySplit,\n            topLeftCell: this.pane.topLeftCell,\n            showRuler: this.sheetView.showRuler,\n            showRowColHeaders: this.sheetView.showRowColHeaders,\n            showGridLines: this.sheetView.showGridLines,\n            zoomScale: this.sheetView.zoomScale,\n            zoomScaleNormal: this.sheetView.zoomScaleNormal,\n          };\n          if (this.model.state === 'split') {\n            model.activePane = this.pane.activePane;\n          }\n          selection = this.selections[this.pane.activePane];\n          if (selection && selection.activeCell) {\n            model.activeCell = selection.activeCell;\n          }\n          if (this.sheetView.style) {\n            model.style = this.sheetView.style;\n          }\n        } else {\n          model = this.model = {\n            workbookViewId: this.sheetView.workbookViewId,\n            rightToLeft: this.sheetView.rightToLeft,\n            state: 'normal',\n            showRuler: this.sheetView.showRuler,\n            showRowColHeaders: this.sheetView.showRowColHeaders,\n            showGridLines: this.sheetView.showGridLines,\n            zoomScale: this.sheetView.zoomScale,\n            zoomScaleNormal: this.sheetView.zoomScaleNormal,\n          };\n          selection = this.selections.topLeft;\n          if (selection && selection.activeCell) {\n            model.activeCell = selection.activeCell;\n          }\n          if (this.sheetView.style) {\n            model.style = this.sheetView.style;\n          }\n        }\n        return false;\n      default:\n        return true;\n    }\n  }\n\n  reconcile() {}\n}\n\nmodule.exports = SheetViewXform;\n"], "mappings": ";;AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,QAAQ;EACrBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,cAAc,SAASL,SAAS,CAAC;EACrC,IAAIM,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,OAAOA,CAACC,KAAK,EAAE;IACb,QAAQA,KAAK,CAACC,KAAK;MACjB,KAAK,QAAQ;MACb,KAAK,OAAO;QACV;MACF;QACED,KAAK,CAACC,KAAK,GAAG,QAAQ;QACtB;IACJ;EACF;EAEAC,MAAMA,CAACC,SAAS,EAAEH,KAAK,EAAE;IACvBG,SAAS,CAACC,QAAQ,CAAC,WAAW,EAAE;MAC9BC,cAAc,EAAEL,KAAK,CAACK,cAAc,IAAI;IAC1C,CAAC,CAAC;IACF,MAAMC,GAAG,GAAG,SAAAA,CAASC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;MAC1C,IAAIA,QAAQ,EAAE;QACZN,SAAS,CAACO,YAAY,CAACH,IAAI,EAAEC,KAAK,CAAC;MACrC;IACF,CAAC;IACDF,GAAG,CAAC,aAAa,EAAE,GAAG,EAAEN,KAAK,CAACW,WAAW,KAAK,IAAI,CAAC;IACnDL,GAAG,CAAC,aAAa,EAAE,GAAG,EAAEN,KAAK,CAACY,WAAW,CAAC;IAC1CN,GAAG,CAAC,WAAW,EAAE,GAAG,EAAEN,KAAK,CAACa,SAAS,KAAK,KAAK,CAAC;IAChDP,GAAG,CAAC,mBAAmB,EAAE,GAAG,EAAEN,KAAK,CAACc,iBAAiB,KAAK,KAAK,CAAC;IAChER,GAAG,CAAC,eAAe,EAAE,GAAG,EAAEN,KAAK,CAACe,aAAa,KAAK,KAAK,CAAC;IACxDT,GAAG,CAAC,WAAW,EAAEN,KAAK,CAACgB,SAAS,EAAEhB,KAAK,CAACgB,SAAS,CAAC;IAClDV,GAAG,CAAC,iBAAiB,EAAEN,KAAK,CAACiB,eAAe,EAAEjB,KAAK,CAACiB,eAAe,CAAC;IACpEX,GAAG,CAAC,MAAM,EAAEN,KAAK,CAACkB,KAAK,EAAElB,KAAK,CAACkB,KAAK,CAAC;IAErC,IAAIC,WAAW;IACf,IAAIC,MAAM;IACV,IAAIC,MAAM;IACV,IAAIC,UAAU;IACd,QAAQtB,KAAK,CAACC,KAAK;MACjB,KAAK,QAAQ;QACXmB,MAAM,GAAGpB,KAAK,CAACoB,MAAM,IAAI,CAAC;QAC1BC,MAAM,GAAGrB,KAAK,CAACqB,MAAM,IAAI,CAAC;QAC1BF,WAAW,GAAGnB,KAAK,CAACmB,WAAW,IAAI7B,QAAQ,CAACiC,UAAU,CAACF,MAAM,GAAG,CAAC,EAAED,MAAM,GAAG,CAAC,CAAC,CAACI,OAAO;QACtFF,UAAU,GACPtB,KAAK,CAACoB,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI,aAAa,IAC7CrB,KAAK,CAACoB,MAAM,IAAI,UAAW,IAC5B,YAAY;QAEdjB,SAAS,CAACsB,QAAQ,CAAC,MAAM,EAAE;UACzBL,MAAM,EAAEpB,KAAK,CAACoB,MAAM,IAAIM,SAAS;UACjCL,MAAM,EAAErB,KAAK,CAACqB,MAAM,IAAIK,SAAS;UACjCP,WAAW;UACXG,UAAU;UACVrB,KAAK,EAAE;QACT,CAAC,CAAC;QACFE,SAAS,CAACsB,QAAQ,CAAC,WAAW,EAAE;UAC9BE,IAAI,EAAEL,UAAU;UAChBM,UAAU,EAAE5B,KAAK,CAAC4B,UAAU;UAC5BC,KAAK,EAAE7B,KAAK,CAAC4B;QACf,CAAC,CAAC;QACF;MACF,KAAK,OAAO;QACV,IAAI5B,KAAK,CAACsB,UAAU,KAAK,SAAS,EAAE;UAClCtB,KAAK,CAACsB,UAAU,GAAGI,SAAS;QAC9B;QACAvB,SAAS,CAACsB,QAAQ,CAAC,MAAM,EAAE;UACzBL,MAAM,EAAEpB,KAAK,CAACoB,MAAM,IAAIM,SAAS;UACjCL,MAAM,EAAErB,KAAK,CAACqB,MAAM,IAAIK,SAAS;UACjCP,WAAW,EAAEnB,KAAK,CAACmB,WAAW;UAC9BG,UAAU,EAAEtB,KAAK,CAACsB;QACpB,CAAC,CAAC;QACFnB,SAAS,CAACsB,QAAQ,CAAC,WAAW,EAAE;UAC9BE,IAAI,EAAE3B,KAAK,CAACsB,UAAU;UACtBM,UAAU,EAAE5B,KAAK,CAAC4B,UAAU;UAC5BC,KAAK,EAAE7B,KAAK,CAAC4B;QACf,CAAC,CAAC;QACF;MACF,KAAK,QAAQ;QACX,IAAI5B,KAAK,CAAC4B,UAAU,EAAE;UACpBzB,SAAS,CAACsB,QAAQ,CAAC,WAAW,EAAE;YAC9BG,UAAU,EAAE5B,KAAK,CAAC4B,UAAU;YAC5BC,KAAK,EAAE7B,KAAK,CAAC4B;UACf,CAAC,CAAC;QACJ;QACA;MACF;QACE;IACJ;IACAzB,SAAS,CAAC2B,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACzB,IAAI;MACf,KAAK,WAAW;QACd,IAAI,CAAC0B,SAAS,GAAG;UACf5B,cAAc,EAAE6B,QAAQ,CAACF,IAAI,CAACG,UAAU,CAAC9B,cAAc,EAAE,EAAE,CAAC;UAC5DM,WAAW,EAAEqB,IAAI,CAACG,UAAU,CAACxB,WAAW,KAAK,GAAG;UAChDC,WAAW,EAAEoB,IAAI,CAACG,UAAU,CAACvB,WAAW,KAAK,GAAG;UAChDC,SAAS,EAAE,EAAEmB,IAAI,CAACG,UAAU,CAACtB,SAAS,KAAK,GAAG,CAAC;UAC/CC,iBAAiB,EAAE,EAAEkB,IAAI,CAACG,UAAU,CAACrB,iBAAiB,KAAK,GAAG,CAAC;UAC/DC,aAAa,EAAE,EAAEiB,IAAI,CAACG,UAAU,CAACpB,aAAa,KAAK,GAAG,CAAC;UACvDC,SAAS,EAAEkB,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACnB,SAAS,IAAI,KAAK,EAAE,EAAE,CAAC;UAC3DC,eAAe,EAAEiB,QAAQ,CAACF,IAAI,CAACG,UAAU,CAAClB,eAAe,IAAI,KAAK,EAAE,EAAE,CAAC;UACvEC,KAAK,EAAEc,IAAI,CAACG,UAAU,CAACC;QACzB,CAAC;QACD,IAAI,CAACT,IAAI,GAAGD,SAAS;QACrB,IAAI,CAACW,UAAU,GAAG,CAAC,CAAC;QACpB,OAAO,IAAI;MAEb,KAAK,MAAM;QACT,IAAI,CAACV,IAAI,GAAG;UACVP,MAAM,EAAEc,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACf,MAAM,IAAI,GAAG,EAAE,EAAE,CAAC;UACnDC,MAAM,EAAEa,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACd,MAAM,IAAI,GAAG,EAAE,EAAE,CAAC;UACnDF,WAAW,EAAEa,IAAI,CAACG,UAAU,CAAChB,WAAW;UACxCG,UAAU,EAAEU,IAAI,CAACG,UAAU,CAACb,UAAU,IAAI,SAAS;UACnDrB,KAAK,EAAE+B,IAAI,CAACG,UAAU,CAAClC;QACzB,CAAC;QACD,OAAO,IAAI;MAEb,KAAK,WAAW;QAAE;UAChB,MAAMM,IAAI,GAAGyB,IAAI,CAACG,UAAU,CAACR,IAAI,IAAI,SAAS;UAC9C,IAAI,CAACU,UAAU,CAAC9B,IAAI,CAAC,GAAG;YACtBoB,IAAI,EAAEpB,IAAI;YACVqB,UAAU,EAAEI,IAAI,CAACG,UAAU,CAACP;UAC9B,CAAC;UACD,OAAO,IAAI;QACb;MAEA;QACE,OAAO,KAAK;IAChB;EACF;EAEAU,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAChC,IAAI,EAAE;IACf,IAAIP,KAAK;IACT,IAAIwC,SAAS;IACb,QAAQjC,IAAI;MACV,KAAK,WAAW;QACd,IAAI,IAAI,CAAC0B,SAAS,IAAI,IAAI,CAACN,IAAI,EAAE;UAC/B3B,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG;YACnBK,cAAc,EAAE,IAAI,CAAC4B,SAAS,CAAC5B,cAAc;YAC7CM,WAAW,EAAE,IAAI,CAACsB,SAAS,CAACtB,WAAW;YACvCV,KAAK,EAAER,WAAW,CAAC,IAAI,CAACkC,IAAI,CAAC1B,KAAK,CAAC,IAAI,OAAO;YAAE;YAChDmB,MAAM,EAAE,IAAI,CAACO,IAAI,CAACP,MAAM;YACxBC,MAAM,EAAE,IAAI,CAACM,IAAI,CAACN,MAAM;YACxBF,WAAW,EAAE,IAAI,CAACQ,IAAI,CAACR,WAAW;YAClCN,SAAS,EAAE,IAAI,CAACoB,SAAS,CAACpB,SAAS;YACnCC,iBAAiB,EAAE,IAAI,CAACmB,SAAS,CAACnB,iBAAiB;YACnDC,aAAa,EAAE,IAAI,CAACkB,SAAS,CAAClB,aAAa;YAC3CC,SAAS,EAAE,IAAI,CAACiB,SAAS,CAACjB,SAAS;YACnCC,eAAe,EAAE,IAAI,CAACgB,SAAS,CAAChB;UAClC,CAAC;UACD,IAAI,IAAI,CAACjB,KAAK,CAACC,KAAK,KAAK,OAAO,EAAE;YAChCD,KAAK,CAACsB,UAAU,GAAG,IAAI,CAACK,IAAI,CAACL,UAAU;UACzC;UACAkB,SAAS,GAAG,IAAI,CAACH,UAAU,CAAC,IAAI,CAACV,IAAI,CAACL,UAAU,CAAC;UACjD,IAAIkB,SAAS,IAAIA,SAAS,CAACZ,UAAU,EAAE;YACrC5B,KAAK,CAAC4B,UAAU,GAAGY,SAAS,CAACZ,UAAU;UACzC;UACA,IAAI,IAAI,CAACK,SAAS,CAACf,KAAK,EAAE;YACxBlB,KAAK,CAACkB,KAAK,GAAG,IAAI,CAACe,SAAS,CAACf,KAAK;UACpC;QACF,CAAC,MAAM;UACLlB,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG;YACnBK,cAAc,EAAE,IAAI,CAAC4B,SAAS,CAAC5B,cAAc;YAC7CM,WAAW,EAAE,IAAI,CAACsB,SAAS,CAACtB,WAAW;YACvCV,KAAK,EAAE,QAAQ;YACfY,SAAS,EAAE,IAAI,CAACoB,SAAS,CAACpB,SAAS;YACnCC,iBAAiB,EAAE,IAAI,CAACmB,SAAS,CAACnB,iBAAiB;YACnDC,aAAa,EAAE,IAAI,CAACkB,SAAS,CAAClB,aAAa;YAC3CC,SAAS,EAAE,IAAI,CAACiB,SAAS,CAACjB,SAAS;YACnCC,eAAe,EAAE,IAAI,CAACgB,SAAS,CAAChB;UAClC,CAAC;UACDuB,SAAS,GAAG,IAAI,CAACH,UAAU,CAACI,OAAO;UACnC,IAAID,SAAS,IAAIA,SAAS,CAACZ,UAAU,EAAE;YACrC5B,KAAK,CAAC4B,UAAU,GAAGY,SAAS,CAACZ,UAAU;UACzC;UACA,IAAI,IAAI,CAACK,SAAS,CAACf,KAAK,EAAE;YACxBlB,KAAK,CAACkB,KAAK,GAAG,IAAI,CAACe,SAAS,CAACf,KAAK;UACpC;QACF;QACA,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEAwB,SAASA,CAAA,EAAG,CAAC;AACf;AAEAC,MAAM,CAACC,OAAO,GAAG/C,cAAc"}