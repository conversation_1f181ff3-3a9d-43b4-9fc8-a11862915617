{"version": 3, "file": "row-xform.js", "names": ["BaseXform", "require", "utils", "CellXform", "RowXform", "constructor", "options", "maxItems", "map", "c", "tag", "prepare", "model", "styleId", "styles", "addStyleModel", "style", "cellXform", "cells", "for<PERSON>ach", "cellModel", "render", "xmlStream", "openNode", "addAttribute", "number", "height", "hidden", "min", "max", "outlineLevel", "collapsed", "closeNode", "parseOpen", "node", "parser", "name", "numRowsSeen", "spans", "attributes", "split", "span", "parseInt", "undefined", "r", "s", "parseBoolean", "bestFit", "ht", "parseFloat", "parseText", "text", "parseClose", "push", "length", "Error", "reconcile", "getStyleModel", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/row-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst utils = require('../../../utils/utils');\n\nconst CellXform = require('./cell-xform');\n\nclass RowXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.maxItems = options && options.maxItems;\n    this.map = {\n      c: new CellXform(),\n    };\n  }\n\n  get tag() {\n    return 'row';\n  }\n\n  prepare(model, options) {\n    const styleId = options.styles.addStyleModel(model.style);\n    if (styleId) {\n      model.styleId = styleId;\n    }\n    const cellXform = this.map.c;\n    model.cells.forEach(cellModel => {\n      cellXform.prepare(cellModel, options);\n    });\n  }\n\n  render(xmlStream, model, options) {\n    xmlStream.openNode('row');\n    xmlStream.addAttribute('r', model.number);\n    if (model.height) {\n      xmlStream.addAttribute('ht', model.height);\n      xmlStream.addAttribute('customHeight', '1');\n    }\n    if (model.hidden) {\n      xmlStream.addAttribute('hidden', '1');\n    }\n    if (model.min > 0 && model.max > 0 && model.min <= model.max) {\n      xmlStream.addAttribute('spans', `${model.min}:${model.max}`);\n    }\n    if (model.styleId) {\n      xmlStream.addAttribute('s', model.styleId);\n      xmlStream.addAttribute('customFormat', '1');\n    }\n    xmlStream.addAttribute('x14ac:dyDescent', '0.25');\n    if (model.outlineLevel) {\n      xmlStream.addAttribute('outlineLevel', model.outlineLevel);\n    }\n    if (model.collapsed) {\n      xmlStream.addAttribute('collapsed', '1');\n    }\n\n    const cellXform = this.map.c;\n    model.cells.forEach(cellModel => {\n      cellXform.render(xmlStream, cellModel, options);\n    });\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    if (node.name === 'row') {\n      this.numRowsSeen += 1;\n      const spans = node.attributes.spans\n        ? node.attributes.spans.split(':').map(span => parseInt(span, 10))\n        : [undefined, undefined];\n      const model = (this.model = {\n        number: parseInt(node.attributes.r, 10),\n        min: spans[0],\n        max: spans[1],\n        cells: [],\n      });\n      if (node.attributes.s) {\n        model.styleId = parseInt(node.attributes.s, 10);\n      }\n      if (utils.parseBoolean(node.attributes.hidden)) {\n        model.hidden = true;\n      }\n      if (utils.parseBoolean(node.attributes.bestFit)) {\n        model.bestFit = true;\n      }\n      if (node.attributes.ht) {\n        model.height = parseFloat(node.attributes.ht);\n      }\n      if (node.attributes.outlineLevel) {\n        model.outlineLevel = parseInt(node.attributes.outlineLevel, 10);\n      }\n      if (utils.parseBoolean(node.attributes.collapsed)) {\n        model.collapsed = true;\n      }\n      return true;\n    }\n\n    this.parser = this.map[node.name];\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    return false;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.cells.push(this.parser.model);\n        if (this.maxItems && this.model.cells.length > this.maxItems) {\n          throw new Error(`Max column count (${this.maxItems}) exceeded`);\n        }\n        this.parser = undefined;\n      }\n      return true;\n    }\n    return false;\n  }\n\n  reconcile(model, options) {\n    model.style = model.styleId ? options.styles.getStyleModel(model.styleId) : {};\n    if (model.styleId !== undefined) {\n      model.styleId = undefined;\n    }\n\n    const cellXform = this.map.c;\n    model.cells.forEach(cellModel => {\n      cellXform.reconcile(cellModel, options);\n    });\n  }\n}\n\nmodule.exports = RowXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,KAAK,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAE7C,MAAME,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;AAEzC,MAAMG,QAAQ,SAASJ,SAAS,CAAC;EAC/BK,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,QAAQ,GAAGD,OAAO,IAAIA,OAAO,CAACC,QAAQ;IAC3C,IAAI,CAACC,GAAG,GAAG;MACTC,CAAC,EAAE,IAAIN,SAAS,CAAC;IACnB,CAAC;EACH;EAEA,IAAIO,GAAGA,CAAA,EAAG;IACR,OAAO,KAAK;EACd;EAEAC,OAAOA,CAACC,KAAK,EAAEN,OAAO,EAAE;IACtB,MAAMO,OAAO,GAAGP,OAAO,CAACQ,MAAM,CAACC,aAAa,CAACH,KAAK,CAACI,KAAK,CAAC;IACzD,IAAIH,OAAO,EAAE;MACXD,KAAK,CAACC,OAAO,GAAGA,OAAO;IACzB;IACA,MAAMI,SAAS,GAAG,IAAI,CAACT,GAAG,CAACC,CAAC;IAC5BG,KAAK,CAACM,KAAK,CAACC,OAAO,CAACC,SAAS,IAAI;MAC/BH,SAAS,CAACN,OAAO,CAACS,SAAS,EAAEd,OAAO,CAAC;IACvC,CAAC,CAAC;EACJ;EAEAe,MAAMA,CAACC,SAAS,EAAEV,KAAK,EAAEN,OAAO,EAAE;IAChCgB,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC;IACzBD,SAAS,CAACE,YAAY,CAAC,GAAG,EAAEZ,KAAK,CAACa,MAAM,CAAC;IACzC,IAAIb,KAAK,CAACc,MAAM,EAAE;MAChBJ,SAAS,CAACE,YAAY,CAAC,IAAI,EAAEZ,KAAK,CAACc,MAAM,CAAC;MAC1CJ,SAAS,CAACE,YAAY,CAAC,cAAc,EAAE,GAAG,CAAC;IAC7C;IACA,IAAIZ,KAAK,CAACe,MAAM,EAAE;MAChBL,SAAS,CAACE,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;IACvC;IACA,IAAIZ,KAAK,CAACgB,GAAG,GAAG,CAAC,IAAIhB,KAAK,CAACiB,GAAG,GAAG,CAAC,IAAIjB,KAAK,CAACgB,GAAG,IAAIhB,KAAK,CAACiB,GAAG,EAAE;MAC5DP,SAAS,CAACE,YAAY,CAAC,OAAO,EAAG,GAAEZ,KAAK,CAACgB,GAAI,IAAGhB,KAAK,CAACiB,GAAI,EAAC,CAAC;IAC9D;IACA,IAAIjB,KAAK,CAACC,OAAO,EAAE;MACjBS,SAAS,CAACE,YAAY,CAAC,GAAG,EAAEZ,KAAK,CAACC,OAAO,CAAC;MAC1CS,SAAS,CAACE,YAAY,CAAC,cAAc,EAAE,GAAG,CAAC;IAC7C;IACAF,SAAS,CAACE,YAAY,CAAC,iBAAiB,EAAE,MAAM,CAAC;IACjD,IAAIZ,KAAK,CAACkB,YAAY,EAAE;MACtBR,SAAS,CAACE,YAAY,CAAC,cAAc,EAAEZ,KAAK,CAACkB,YAAY,CAAC;IAC5D;IACA,IAAIlB,KAAK,CAACmB,SAAS,EAAE;MACnBT,SAAS,CAACE,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC;IAC1C;IAEA,MAAMP,SAAS,GAAG,IAAI,CAACT,GAAG,CAACC,CAAC;IAC5BG,KAAK,CAACM,KAAK,CAACC,OAAO,CAACC,SAAS,IAAI;MAC/BH,SAAS,CAACI,MAAM,CAACC,SAAS,EAAEF,SAAS,EAAEd,OAAO,CAAC;IACjD,CAAC,CAAC;IAEFgB,SAAS,CAACU,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,IAAIA,IAAI,CAACE,IAAI,KAAK,KAAK,EAAE;MACvB,IAAI,CAACC,WAAW,IAAI,CAAC;MACrB,MAAMC,KAAK,GAAGJ,IAAI,CAACK,UAAU,CAACD,KAAK,GAC/BJ,IAAI,CAACK,UAAU,CAACD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAChC,GAAG,CAACiC,IAAI,IAAIC,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC,GAChE,CAACE,SAAS,EAAEA,SAAS,CAAC;MAC1B,MAAM/B,KAAK,GAAI,IAAI,CAACA,KAAK,GAAG;QAC1Ba,MAAM,EAAEiB,QAAQ,CAACR,IAAI,CAACK,UAAU,CAACK,CAAC,EAAE,EAAE,CAAC;QACvChB,GAAG,EAAEU,KAAK,CAAC,CAAC,CAAC;QACbT,GAAG,EAAES,KAAK,CAAC,CAAC,CAAC;QACbpB,KAAK,EAAE;MACT,CAAE;MACF,IAAIgB,IAAI,CAACK,UAAU,CAACM,CAAC,EAAE;QACrBjC,KAAK,CAACC,OAAO,GAAG6B,QAAQ,CAACR,IAAI,CAACK,UAAU,CAACM,CAAC,EAAE,EAAE,CAAC;MACjD;MACA,IAAI3C,KAAK,CAAC4C,YAAY,CAACZ,IAAI,CAACK,UAAU,CAACZ,MAAM,CAAC,EAAE;QAC9Cf,KAAK,CAACe,MAAM,GAAG,IAAI;MACrB;MACA,IAAIzB,KAAK,CAAC4C,YAAY,CAACZ,IAAI,CAACK,UAAU,CAACQ,OAAO,CAAC,EAAE;QAC/CnC,KAAK,CAACmC,OAAO,GAAG,IAAI;MACtB;MACA,IAAIb,IAAI,CAACK,UAAU,CAACS,EAAE,EAAE;QACtBpC,KAAK,CAACc,MAAM,GAAGuB,UAAU,CAACf,IAAI,CAACK,UAAU,CAACS,EAAE,CAAC;MAC/C;MACA,IAAId,IAAI,CAACK,UAAU,CAACT,YAAY,EAAE;QAChClB,KAAK,CAACkB,YAAY,GAAGY,QAAQ,CAACR,IAAI,CAACK,UAAU,CAACT,YAAY,EAAE,EAAE,CAAC;MACjE;MACA,IAAI5B,KAAK,CAAC4C,YAAY,CAACZ,IAAI,CAACK,UAAU,CAACR,SAAS,CAAC,EAAE;QACjDnB,KAAK,CAACmB,SAAS,GAAG,IAAI;MACxB;MACA,OAAO,IAAI;IACb;IAEA,IAAI,CAACI,MAAM,GAAG,IAAI,CAAC3B,GAAG,CAAC0B,IAAI,CAACE,IAAI,CAAC;IACjC,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAgB,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACe,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAAChB,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACiB,UAAU,CAAChB,IAAI,CAAC,EAAE;QACjC,IAAI,CAACxB,KAAK,CAACM,KAAK,CAACmC,IAAI,CAAC,IAAI,CAAClB,MAAM,CAACvB,KAAK,CAAC;QACxC,IAAI,IAAI,CAACL,QAAQ,IAAI,IAAI,CAACK,KAAK,CAACM,KAAK,CAACoC,MAAM,GAAG,IAAI,CAAC/C,QAAQ,EAAE;UAC5D,MAAM,IAAIgD,KAAK,CAAE,qBAAoB,IAAI,CAAChD,QAAS,YAAW,CAAC;QACjE;QACA,IAAI,CAAC4B,MAAM,GAAGQ,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAa,SAASA,CAAC5C,KAAK,EAAEN,OAAO,EAAE;IACxBM,KAAK,CAACI,KAAK,GAAGJ,KAAK,CAACC,OAAO,GAAGP,OAAO,CAACQ,MAAM,CAAC2C,aAAa,CAAC7C,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC9E,IAAID,KAAK,CAACC,OAAO,KAAK8B,SAAS,EAAE;MAC/B/B,KAAK,CAACC,OAAO,GAAG8B,SAAS;IAC3B;IAEA,MAAM1B,SAAS,GAAG,IAAI,CAACT,GAAG,CAACC,CAAC;IAC5BG,KAAK,CAACM,KAAK,CAACC,OAAO,CAACC,SAAS,IAAI;MAC/BH,SAAS,CAACuC,SAAS,CAACpC,SAAS,EAAEd,OAAO,CAAC;IACzC,CAAC,CAAC;EACJ;AACF;AAEAoD,MAAM,CAACC,OAAO,GAAGvD,QAAQ"}