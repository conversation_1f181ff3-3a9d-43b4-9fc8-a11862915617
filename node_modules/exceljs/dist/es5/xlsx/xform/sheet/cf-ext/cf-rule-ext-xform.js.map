{"version": 3, "file": "cf-rule-ext-xform.js", "names": ["v4", "uuidv4", "require", "BaseXform", "CompositeXform", "DatabarExtXform", "IconSetExtXform", "extIcons", "CfRuleExtXform", "constructor", "map", "databarXform", "iconSetXform", "tag", "isExt", "rule", "type", "custom", "iconSet", "prepare", "model", "x14Id", "toUpperCase", "render", "xmlStream", "renderDataBar", "renderIconSet", "openNode", "id", "closeNode", "priority", "createNewModel", "_ref", "attributes", "toIntValue", "onParserClose", "name", "parser", "Object", "assign", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf-ext/cf-rule-ext-xform.js"], "sourcesContent": ["const {v4: uuidv4} = require('uuid');\nconst BaseXform = require('../../base-xform');\nconst CompositeXform = require('../../composite-xform');\n\nconst DatabarExtXform = require('./databar-ext-xform');\nconst IconSetExtXform = require('./icon-set-ext-xform');\n\nconst extIcons = {\n  '3Triangles': true,\n  '3Stars': true,\n  '5Boxes': true,\n};\n\nclass CfRuleExtXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'x14:dataBar': (this.databarXform = new DatabarExtXform()),\n      'x14:iconSet': (this.iconSetXform = new IconSetExtXform()),\n    };\n  }\n\n  get tag() {\n    return 'x14:cfRule';\n  }\n\n  static isExt(rule) {\n    // is this rule primitive?\n    if (rule.type === 'dataBar') {\n      return DatabarExtXform.isExt(rule);\n    }\n    if (rule.type === 'iconSet') {\n      if (rule.custom || extIcons[rule.iconSet]) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  prepare(model) {\n    if (CfRuleExtXform.isExt(model)) {\n      model.x14Id = `{${uuidv4()}}`.toUpperCase();\n    }\n  }\n\n  render(xmlStream, model) {\n    if (!CfRuleExtXform.isExt(model)) {\n      return;\n    }\n\n    switch (model.type) {\n      case 'dataBar':\n        this.renderDataBar(xmlStream, model);\n        break;\n      case 'iconSet':\n        this.renderIconSet(xmlStream, model);\n        break;\n    }\n  }\n\n  renderDataBar(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: 'dataBar',\n      id: model.x14Id,\n    });\n\n    this.databarXform.render(xmlStream, model);\n\n    xmlStream.closeNode();\n  }\n\n  renderIconSet(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: 'iconSet',\n      priority: model.priority,\n      id: model.x14Id || `{${uuidv4()}}`,\n    });\n\n    this.iconSetXform.render(xmlStream, model);\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel({attributes}) {\n    return {\n      type: attributes.type,\n      x14Id: attributes.id,\n      priority: BaseXform.toIntValue(attributes.priority),\n    };\n  }\n\n  onParserClose(name, parser) {\n    Object.assign(this.model, parser.model);\n  }\n}\n\nmodule.exports = CfRuleExtXform;\n"], "mappings": ";;AAAA,MAAM;EAACA,EAAE,EAAEC;AAAM,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;AACpC,MAAMC,SAAS,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAC7C,MAAME,cAAc,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAMG,eAAe,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AACtD,MAAMI,eAAe,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAEvD,MAAMK,QAAQ,GAAG;EACf,YAAY,EAAE,IAAI;EAClB,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,cAAc,SAASJ,cAAc,CAAC;EAC1CK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,aAAa,EAAG,IAAI,CAACC,YAAY,GAAG,IAAIN,eAAe,CAAC,CAAE;MAC1D,aAAa,EAAG,IAAI,CAACO,YAAY,GAAG,IAAIN,eAAe,CAAC;IAC1D,CAAC;EACH;EAEA,IAAIO,GAAGA,CAAA,EAAG;IACR,OAAO,YAAY;EACrB;EAEA,OAAOC,KAAKA,CAACC,IAAI,EAAE;IACjB;IACA,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;MAC3B,OAAOX,eAAe,CAACS,KAAK,CAACC,IAAI,CAAC;IACpC;IACA,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;MAC3B,IAAID,IAAI,CAACE,MAAM,IAAIV,QAAQ,CAACQ,IAAI,CAACG,OAAO,CAAC,EAAE;QACzC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEAC,OAAOA,CAACC,KAAK,EAAE;IACb,IAAIZ,cAAc,CAACM,KAAK,CAACM,KAAK,CAAC,EAAE;MAC/BA,KAAK,CAACC,KAAK,GAAI,IAAGpB,MAAM,CAAC,CAAE,GAAE,CAACqB,WAAW,CAAC,CAAC;IAC7C;EACF;EAEAC,MAAMA,CAACC,SAAS,EAAEJ,KAAK,EAAE;IACvB,IAAI,CAACZ,cAAc,CAACM,KAAK,CAACM,KAAK,CAAC,EAAE;MAChC;IACF;IAEA,QAAQA,KAAK,CAACJ,IAAI;MAChB,KAAK,SAAS;QACZ,IAAI,CAACS,aAAa,CAACD,SAAS,EAAEJ,KAAK,CAAC;QACpC;MACF,KAAK,SAAS;QACZ,IAAI,CAACM,aAAa,CAACF,SAAS,EAAEJ,KAAK,CAAC;QACpC;IACJ;EACF;EAEAK,aAAaA,CAACD,SAAS,EAAEJ,KAAK,EAAE;IAC9BI,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACd,GAAG,EAAE;MAC3BG,IAAI,EAAE,SAAS;MACfY,EAAE,EAAER,KAAK,CAACC;IACZ,CAAC,CAAC;IAEF,IAAI,CAACV,YAAY,CAACY,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAC;IAE1CI,SAAS,CAACK,SAAS,CAAC,CAAC;EACvB;EAEAH,aAAaA,CAACF,SAAS,EAAEJ,KAAK,EAAE;IAC9BI,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACd,GAAG,EAAE;MAC3BG,IAAI,EAAE,SAAS;MACfc,QAAQ,EAAEV,KAAK,CAACU,QAAQ;MACxBF,EAAE,EAAER,KAAK,CAACC,KAAK,IAAK,IAAGpB,MAAM,CAAC,CAAE;IAClC,CAAC,CAAC;IAEF,IAAI,CAACW,YAAY,CAACW,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAC;IAE1CI,SAAS,CAACK,SAAS,CAAC,CAAC;EACvB;EAEAE,cAAcA,CAAAC,IAAA,EAAe;IAAA,IAAd;MAACC;IAAU,CAAC,GAAAD,IAAA;IACzB,OAAO;MACLhB,IAAI,EAAEiB,UAAU,CAACjB,IAAI;MACrBK,KAAK,EAAEY,UAAU,CAACL,EAAE;MACpBE,QAAQ,EAAE3B,SAAS,CAAC+B,UAAU,CAACD,UAAU,CAACH,QAAQ;IACpD,CAAC;EACH;EAEAK,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1BC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACnB,KAAK,EAAEiB,MAAM,CAACjB,KAAK,CAAC;EACzC;AACF;AAEAoB,MAAM,CAACC,OAAO,GAAGjC,cAAc"}