{"version": 3, "file": "icon-set-xform.js", "names": ["BaseXform", "require", "CompositeXform", "CfvoXform", "IconSetXform", "constructor", "map", "cfvo", "cfvoXform", "tag", "render", "xmlStream", "model", "openNode", "iconSet", "toStringAttribute", "reverse", "toBoolAttribute", "showValue", "for<PERSON>ach", "closeNode", "createNewModel", "_ref", "attributes", "toStringValue", "toBoolValue", "onParserClose", "name", "parser", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/icon-set-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\nconst CompositeXform = require('../../composite-xform');\n\nconst CfvoXform = require('./cfvo-xform');\n\nclass IconSetXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      cfvo: (this.cfvoXform = new CfvoXform()),\n    };\n  }\n\n  get tag() {\n    return 'iconSet';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      iconSet: BaseXform.toStringAttribute(model.iconSet, '3TrafficLights'),\n      reverse: BaseXform.toBoolAttribute(model.reverse, false),\n      showValue: BaseXform.toBoolAttribute(model.showValue, true),\n    });\n\n    model.cfvo.forEach(cfvo => {\n      this.cfvoXform.render(xmlStream, cfvo);\n    });\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel({attributes}) {\n    return {\n      iconSet: BaseXform.toStringValue(attributes.iconSet, '3TrafficLights'),\n      reverse: BaseXform.toBoolValue(attributes.reverse),\n      showValue: BaseXform.toBoolValue(attributes.showValue),\n      cfvo: [],\n    };\n  }\n\n  onParserClose(name, parser) {\n    this.model[name].push(parser.model);\n  }\n}\n\nmodule.exports = IconSetXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC7C,MAAMC,cAAc,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAME,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;AAEzC,MAAMG,YAAY,SAASF,cAAc,CAAC;EACxCG,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,IAAI,EAAG,IAAI,CAACC,SAAS,GAAG,IAAIL,SAAS,CAAC;IACxC,CAAC;EACH;EAEA,IAAIM,GAAGA,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,OAAO,EAAEd,SAAS,CAACe,iBAAiB,CAACH,KAAK,CAACE,OAAO,EAAE,gBAAgB,CAAC;MACrEE,OAAO,EAAEhB,SAAS,CAACiB,eAAe,CAACL,KAAK,CAACI,OAAO,EAAE,KAAK,CAAC;MACxDE,SAAS,EAAElB,SAAS,CAACiB,eAAe,CAACL,KAAK,CAACM,SAAS,EAAE,IAAI;IAC5D,CAAC,CAAC;IAEFN,KAAK,CAACL,IAAI,CAACY,OAAO,CAACZ,IAAI,IAAI;MACzB,IAAI,CAACC,SAAS,CAACE,MAAM,CAACC,SAAS,EAAEJ,IAAI,CAAC;IACxC,CAAC,CAAC;IAEFI,SAAS,CAACS,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAAC,IAAA,EAAe;IAAA,IAAd;MAACC;IAAU,CAAC,GAAAD,IAAA;IACzB,OAAO;MACLR,OAAO,EAAEd,SAAS,CAACwB,aAAa,CAACD,UAAU,CAACT,OAAO,EAAE,gBAAgB,CAAC;MACtEE,OAAO,EAAEhB,SAAS,CAACyB,WAAW,CAACF,UAAU,CAACP,OAAO,CAAC;MAClDE,SAAS,EAAElB,SAAS,CAACyB,WAAW,CAACF,UAAU,CAACL,SAAS,CAAC;MACtDX,IAAI,EAAE;IACR,CAAC;EACH;EAEAmB,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,IAAI,CAAChB,KAAK,CAACe,IAAI,CAAC,CAACE,IAAI,CAACD,MAAM,CAAChB,KAAK,CAAC;EACrC;AACF;AAEAkB,MAAM,CAACC,OAAO,GAAG3B,YAAY"}