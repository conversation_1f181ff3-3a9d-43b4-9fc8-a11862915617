{"version": 3, "file": "cf-rule-xform.js", "names": ["BaseXform", "require", "CompositeXform", "Range", "DatabarXform", "ExtLstRefXform", "FormulaXform", "ColorScaleXform", "IconSetXform", "extIcons", "getTextFormula", "model", "formulae", "range", "ref", "tl", "operator", "text", "undefined", "getTimePeriodFormula", "timePeriod", "opType", "attributes", "type", "CfRuleXform", "constructor", "map", "dataBar", "databarXform", "extLst", "extLstRefXform", "formula", "formulaXform", "colorScale", "colorScaleXform", "iconSet", "iconSetXform", "tag", "isPrimitive", "rule", "custom", "render", "xmlStream", "renderExpression", "renderCellIs", "renderTop10", "renderAboveAverage", "renderDataBar", "renderColorScale", "renderIconSet", "renderText", "renderTimePeriod", "openNode", "dxfId", "priority", "closeNode", "for<PERSON>ach", "leafNode", "percent", "toBoolAttribute", "bottom", "rank", "toIntValue", "aboveAverage", "toStringAttribute", "createNewModel", "_ref", "toBoolValue", "onParserClose", "name", "parser", "Object", "assign", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/cf-rule-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\nconst CompositeXform = require('../../composite-xform');\n\nconst Range = require('../../../../doc/range');\n\nconst DatabarXform = require('./databar-xform');\nconst ExtLstRefXform = require('./ext-lst-ref-xform');\nconst FormulaXform = require('./formula-xform');\nconst ColorScaleXform = require('./color-scale-xform');\nconst IconSetXform = require('./icon-set-xform');\n\nconst extIcons = {\n  '3Triangles': true,\n  '3Stars': true,\n  '5Boxes': true,\n};\n\nconst getTextFormula = model => {\n  if (model.formulae && model.formulae[0]) {\n    return model.formulae[0];\n  }\n\n  const range = new Range(model.ref);\n  const {tl} = range;\n  switch (model.operator) {\n    case 'containsText':\n      return `NOT(ISERROR(SEARCH(\"${model.text}\",${tl})))`;\n    case 'containsBlanks':\n      return `LEN(TRIM(${tl}))=0`;\n    case 'notContainsBlanks':\n      return `LEN(TRIM(${tl}))>0`;\n    case 'containsErrors':\n      return `ISERROR(${tl})`;\n    case 'notContainsErrors':\n      return `NOT(ISERROR(${tl}))`;\n    default:\n      return undefined;\n  }\n};\n\nconst getTimePeriodFormula = model => {\n  if (model.formulae && model.formulae[0]) {\n    return model.formulae[0];\n  }\n\n  const range = new Range(model.ref);\n  const {tl} = range;\n  switch (model.timePeriod) {\n    case 'thisWeek':\n      return `AND(TODAY()-ROUNDDOWN(${tl},0)<=WEEKDAY(TODAY())-1,ROUNDDOWN(${tl},0)-TODAY()<=7-WEEKDAY(TODAY()))`;\n    case 'lastWeek':\n      return `AND(TODAY()-ROUNDDOWN(${tl},0)>=(WEEKDAY(TODAY())),TODAY()-ROUNDDOWN(${tl},0)<(WEEKDAY(TODAY())+7))`;\n    case 'nextWeek':\n      return `AND(ROUNDDOWN(${tl},0)-TODAY()>(7-WEEKDAY(TODAY())),ROUNDDOWN(${tl},0)-TODAY()<(15-WEEKDAY(TODAY())))`;\n    case 'yesterday':\n      return `FLOOR(${tl},1)=TODAY()-1`;\n    case 'today':\n      return `FLOOR(${tl},1)=TODAY()`;\n    case 'tomorrow':\n      return `FLOOR(${tl},1)=TODAY()+1`;\n    case 'last7Days':\n      return `AND(TODAY()-FLOOR(${tl},1)<=6,FLOOR(${tl},1)<=TODAY())`;\n    case 'lastMonth':\n      return `AND(MONTH(${tl})=MONTH(EDATE(TODAY(),0-1)),YEAR(${tl})=YEAR(EDATE(TODAY(),0-1)))`;\n    case 'thisMonth':\n      return `AND(MONTH(${tl})=MONTH(TODAY()),YEAR(${tl})=YEAR(TODAY()))`;\n    case 'nextMonth':\n      return `AND(MONTH(${tl})=MONTH(EDATE(TODAY(),0+1)),YEAR(${tl})=YEAR(EDATE(TODAY(),0+1)))`;\n    default:\n      return undefined;\n  }\n};\n\nconst opType = attributes => {\n  const {type, operator} = attributes;\n  switch (type) {\n    case 'containsText':\n    case 'containsBlanks':\n    case 'notContainsBlanks':\n    case 'containsErrors':\n    case 'notContainsErrors':\n      return {\n        type: 'containsText',\n        operator: type,\n      };\n\n    default:\n      return {type, operator};\n  }\n};\n\nclass CfRuleXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      dataBar: (this.databarXform = new DatabarXform()),\n      extLst: (this.extLstRefXform = new ExtLstRefXform()),\n      formula: (this.formulaXform = new FormulaXform()),\n      colorScale: (this.colorScaleXform = new ColorScaleXform()),\n      iconSet: (this.iconSetXform = new IconSetXform()),\n    };\n  }\n\n  get tag() {\n    return 'cfRule';\n  }\n\n  static isPrimitive(rule) {\n    // is this rule primitive?\n    if (rule.type === 'iconSet') {\n      if (rule.custom || extIcons[rule.iconSet]) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  render(xmlStream, model) {\n    switch (model.type) {\n      case 'expression':\n        this.renderExpression(xmlStream, model);\n        break;\n      case 'cellIs':\n        this.renderCellIs(xmlStream, model);\n        break;\n      case 'top10':\n        this.renderTop10(xmlStream, model);\n        break;\n      case 'aboveAverage':\n        this.renderAboveAverage(xmlStream, model);\n        break;\n      case 'dataBar':\n        this.renderDataBar(xmlStream, model);\n        break;\n      case 'colorScale':\n        this.renderColorScale(xmlStream, model);\n        break;\n      case 'iconSet':\n        this.renderIconSet(xmlStream, model);\n        break;\n      case 'containsText':\n        this.renderText(xmlStream, model);\n        break;\n      case 'timePeriod':\n        this.renderTimePeriod(xmlStream, model);\n        break;\n    }\n  }\n\n  renderExpression(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: 'expression',\n      dxfId: model.dxfId,\n      priority: model.priority,\n    });\n\n    this.formulaXform.render(xmlStream, model.formulae[0]);\n\n    xmlStream.closeNode();\n  }\n\n  renderCellIs(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: 'cellIs',\n      dxfId: model.dxfId,\n      priority: model.priority,\n      operator: model.operator,\n    });\n\n    model.formulae.forEach(formula => {\n      this.formulaXform.render(xmlStream, formula);\n    });\n\n    xmlStream.closeNode();\n  }\n\n  renderTop10(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      type: 'top10',\n      dxfId: model.dxfId,\n      priority: model.priority,\n      percent: BaseXform.toBoolAttribute(model.percent, false),\n      bottom: BaseXform.toBoolAttribute(model.bottom, false),\n      rank: BaseXform.toIntValue(model.rank, 10, true),\n    });\n  }\n\n  renderAboveAverage(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      type: 'aboveAverage',\n      dxfId: model.dxfId,\n      priority: model.priority,\n      aboveAverage: BaseXform.toBoolAttribute(model.aboveAverage, true),\n    });\n  }\n\n  renderDataBar(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: 'dataBar',\n      priority: model.priority,\n    });\n\n    this.databarXform.render(xmlStream, model);\n    this.extLstRefXform.render(xmlStream, model);\n\n    xmlStream.closeNode();\n  }\n\n  renderColorScale(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: 'colorScale',\n      priority: model.priority,\n    });\n\n    this.colorScaleXform.render(xmlStream, model);\n\n    xmlStream.closeNode();\n  }\n\n  renderIconSet(xmlStream, model) {\n    // iconset is all primitive or all extLst\n    if (!CfRuleXform.isPrimitive(model)) {\n      return;\n    }\n\n    xmlStream.openNode(this.tag, {\n      type: 'iconSet',\n      priority: model.priority,\n    });\n\n    this.iconSetXform.render(xmlStream, model);\n\n    xmlStream.closeNode();\n  }\n\n  renderText(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: model.operator,\n      dxfId: model.dxfId,\n      priority: model.priority,\n      operator: BaseXform.toStringAttribute(model.operator, 'containsText'),\n    });\n\n    const formula = getTextFormula(model);\n    if (formula) {\n      this.formulaXform.render(xmlStream, formula);\n    }\n\n    xmlStream.closeNode();\n  }\n\n  renderTimePeriod(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: 'timePeriod',\n      dxfId: model.dxfId,\n      priority: model.priority,\n      timePeriod: model.timePeriod,\n    });\n\n    const formula = getTimePeriodFormula(model);\n    if (formula) {\n      this.formulaXform.render(xmlStream, formula);\n    }\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel({attributes}) {\n    return {\n      ...opType(attributes),\n      dxfId: BaseXform.toIntValue(attributes.dxfId),\n      priority: BaseXform.toIntValue(attributes.priority),\n      timePeriod: attributes.timePeriod,\n      percent: BaseXform.toBoolValue(attributes.percent),\n      bottom: BaseXform.toBoolValue(attributes.bottom),\n      rank: BaseXform.toIntValue(attributes.rank),\n      aboveAverage: BaseXform.toBoolValue(attributes.aboveAverage),\n    };\n  }\n\n  onParserClose(name, parser) {\n    switch (name) {\n      case 'dataBar':\n      case 'extLst':\n      case 'colorScale':\n      case 'iconSet':\n        // merge parser model with ours\n        Object.assign(this.model, parser.model);\n        break;\n\n      case 'formula':\n        // except - formula is a string and appends to formulae\n        this.model.formulae = this.model.formulae || [];\n        this.model.formulae.push(parser.model);\n        break;\n    }\n  }\n}\n\nmodule.exports = CfRuleXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC7C,MAAMC,cAAc,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAME,KAAK,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAE9C,MAAMG,YAAY,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AAC/C,MAAMI,cAAc,GAAGJ,OAAO,CAAC,qBAAqB,CAAC;AACrD,MAAMK,YAAY,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAC/C,MAAMM,eAAe,GAAGN,OAAO,CAAC,qBAAqB,CAAC;AACtD,MAAMO,YAAY,GAAGP,OAAO,CAAC,kBAAkB,CAAC;AAEhD,MAAMQ,QAAQ,GAAG;EACf,YAAY,EAAE,IAAI;EAClB,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE;IACvC,OAAOD,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC1B;EAEA,MAAMC,KAAK,GAAG,IAAIV,KAAK,CAACQ,KAAK,CAACG,GAAG,CAAC;EAClC,MAAM;IAACC;EAAE,CAAC,GAAGF,KAAK;EAClB,QAAQF,KAAK,CAACK,QAAQ;IACpB,KAAK,cAAc;MACjB,OAAQ,uBAAsBL,KAAK,CAACM,IAAK,KAAIF,EAAG,KAAI;IACtD,KAAK,gBAAgB;MACnB,OAAQ,YAAWA,EAAG,MAAK;IAC7B,KAAK,mBAAmB;MACtB,OAAQ,YAAWA,EAAG,MAAK;IAC7B,KAAK,gBAAgB;MACnB,OAAQ,WAAUA,EAAG,GAAE;IACzB,KAAK,mBAAmB;MACtB,OAAQ,eAAcA,EAAG,IAAG;IAC9B;MACE,OAAOG,SAAS;EACpB;AACF,CAAC;AAED,MAAMC,oBAAoB,GAAGR,KAAK,IAAI;EACpC,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE;IACvC,OAAOD,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC1B;EAEA,MAAMC,KAAK,GAAG,IAAIV,KAAK,CAACQ,KAAK,CAACG,GAAG,CAAC;EAClC,MAAM;IAACC;EAAE,CAAC,GAAGF,KAAK;EAClB,QAAQF,KAAK,CAACS,UAAU;IACtB,KAAK,UAAU;MACb,OAAQ,yBAAwBL,EAAG,qCAAoCA,EAAG,kCAAiC;IAC7G,KAAK,UAAU;MACb,OAAQ,yBAAwBA,EAAG,6CAA4CA,EAAG,2BAA0B;IAC9G,KAAK,UAAU;MACb,OAAQ,iBAAgBA,EAAG,8CAA6CA,EAAG,oCAAmC;IAChH,KAAK,WAAW;MACd,OAAQ,SAAQA,EAAG,eAAc;IACnC,KAAK,OAAO;MACV,OAAQ,SAAQA,EAAG,aAAY;IACjC,KAAK,UAAU;MACb,OAAQ,SAAQA,EAAG,eAAc;IACnC,KAAK,WAAW;MACd,OAAQ,qBAAoBA,EAAG,gBAAeA,EAAG,eAAc;IACjE,KAAK,WAAW;MACd,OAAQ,aAAYA,EAAG,oCAAmCA,EAAG,6BAA4B;IAC3F,KAAK,WAAW;MACd,OAAQ,aAAYA,EAAG,yBAAwBA,EAAG,kBAAiB;IACrE,KAAK,WAAW;MACd,OAAQ,aAAYA,EAAG,oCAAmCA,EAAG,6BAA4B;IAC3F;MACE,OAAOG,SAAS;EACpB;AACF,CAAC;AAED,MAAMG,MAAM,GAAGC,UAAU,IAAI;EAC3B,MAAM;IAACC,IAAI;IAAEP;EAAQ,CAAC,GAAGM,UAAU;EACnC,QAAQC,IAAI;IACV,KAAK,cAAc;IACnB,KAAK,gBAAgB;IACrB,KAAK,mBAAmB;IACxB,KAAK,gBAAgB;IACrB,KAAK,mBAAmB;MACtB,OAAO;QACLA,IAAI,EAAE,cAAc;QACpBP,QAAQ,EAAEO;MACZ,CAAC;IAEH;MACE,OAAO;QAACA,IAAI;QAAEP;MAAQ,CAAC;EAC3B;AACF,CAAC;AAED,MAAMQ,WAAW,SAAStB,cAAc,CAAC;EACvCuB,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,OAAO,EAAG,IAAI,CAACC,YAAY,GAAG,IAAIxB,YAAY,CAAC,CAAE;MACjDyB,MAAM,EAAG,IAAI,CAACC,cAAc,GAAG,IAAIzB,cAAc,CAAC,CAAE;MACpD0B,OAAO,EAAG,IAAI,CAACC,YAAY,GAAG,IAAI1B,YAAY,CAAC,CAAE;MACjD2B,UAAU,EAAG,IAAI,CAACC,eAAe,GAAG,IAAI3B,eAAe,CAAC,CAAE;MAC1D4B,OAAO,EAAG,IAAI,CAACC,YAAY,GAAG,IAAI5B,YAAY,CAAC;IACjD,CAAC;EACH;EAEA,IAAI6B,GAAGA,CAAA,EAAG;IACR,OAAO,QAAQ;EACjB;EAEA,OAAOC,WAAWA,CAACC,IAAI,EAAE;IACvB;IACA,IAAIA,IAAI,CAAChB,IAAI,KAAK,SAAS,EAAE;MAC3B,IAAIgB,IAAI,CAACC,MAAM,IAAI/B,QAAQ,CAAC8B,IAAI,CAACJ,OAAO,CAAC,EAAE;QACzC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EAEAM,MAAMA,CAACC,SAAS,EAAE/B,KAAK,EAAE;IACvB,QAAQA,KAAK,CAACY,IAAI;MAChB,KAAK,YAAY;QACf,IAAI,CAACoB,gBAAgB,CAACD,SAAS,EAAE/B,KAAK,CAAC;QACvC;MACF,KAAK,QAAQ;QACX,IAAI,CAACiC,YAAY,CAACF,SAAS,EAAE/B,KAAK,CAAC;QACnC;MACF,KAAK,OAAO;QACV,IAAI,CAACkC,WAAW,CAACH,SAAS,EAAE/B,KAAK,CAAC;QAClC;MACF,KAAK,cAAc;QACjB,IAAI,CAACmC,kBAAkB,CAACJ,SAAS,EAAE/B,KAAK,CAAC;QACzC;MACF,KAAK,SAAS;QACZ,IAAI,CAACoC,aAAa,CAACL,SAAS,EAAE/B,KAAK,CAAC;QACpC;MACF,KAAK,YAAY;QACf,IAAI,CAACqC,gBAAgB,CAACN,SAAS,EAAE/B,KAAK,CAAC;QACvC;MACF,KAAK,SAAS;QACZ,IAAI,CAACsC,aAAa,CAACP,SAAS,EAAE/B,KAAK,CAAC;QACpC;MACF,KAAK,cAAc;QACjB,IAAI,CAACuC,UAAU,CAACR,SAAS,EAAE/B,KAAK,CAAC;QACjC;MACF,KAAK,YAAY;QACf,IAAI,CAACwC,gBAAgB,CAACT,SAAS,EAAE/B,KAAK,CAAC;QACvC;IACJ;EACF;EAEAgC,gBAAgBA,CAACD,SAAS,EAAE/B,KAAK,EAAE;IACjC+B,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACf,GAAG,EAAE;MAC3Bd,IAAI,EAAE,YAAY;MAClB8B,KAAK,EAAE1C,KAAK,CAAC0C,KAAK;MAClBC,QAAQ,EAAE3C,KAAK,CAAC2C;IAClB,CAAC,CAAC;IAEF,IAAI,CAACtB,YAAY,CAACS,MAAM,CAACC,SAAS,EAAE/B,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEtD8B,SAAS,CAACa,SAAS,CAAC,CAAC;EACvB;EAEAX,YAAYA,CAACF,SAAS,EAAE/B,KAAK,EAAE;IAC7B+B,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACf,GAAG,EAAE;MAC3Bd,IAAI,EAAE,QAAQ;MACd8B,KAAK,EAAE1C,KAAK,CAAC0C,KAAK;MAClBC,QAAQ,EAAE3C,KAAK,CAAC2C,QAAQ;MACxBtC,QAAQ,EAAEL,KAAK,CAACK;IAClB,CAAC,CAAC;IAEFL,KAAK,CAACC,QAAQ,CAAC4C,OAAO,CAACzB,OAAO,IAAI;MAChC,IAAI,CAACC,YAAY,CAACS,MAAM,CAACC,SAAS,EAAEX,OAAO,CAAC;IAC9C,CAAC,CAAC;IAEFW,SAAS,CAACa,SAAS,CAAC,CAAC;EACvB;EAEAV,WAAWA,CAACH,SAAS,EAAE/B,KAAK,EAAE;IAC5B+B,SAAS,CAACe,QAAQ,CAAC,IAAI,CAACpB,GAAG,EAAE;MAC3Bd,IAAI,EAAE,OAAO;MACb8B,KAAK,EAAE1C,KAAK,CAAC0C,KAAK;MAClBC,QAAQ,EAAE3C,KAAK,CAAC2C,QAAQ;MACxBI,OAAO,EAAE1D,SAAS,CAAC2D,eAAe,CAAChD,KAAK,CAAC+C,OAAO,EAAE,KAAK,CAAC;MACxDE,MAAM,EAAE5D,SAAS,CAAC2D,eAAe,CAAChD,KAAK,CAACiD,MAAM,EAAE,KAAK,CAAC;MACtDC,IAAI,EAAE7D,SAAS,CAAC8D,UAAU,CAACnD,KAAK,CAACkD,IAAI,EAAE,EAAE,EAAE,IAAI;IACjD,CAAC,CAAC;EACJ;EAEAf,kBAAkBA,CAACJ,SAAS,EAAE/B,KAAK,EAAE;IACnC+B,SAAS,CAACe,QAAQ,CAAC,IAAI,CAACpB,GAAG,EAAE;MAC3Bd,IAAI,EAAE,cAAc;MACpB8B,KAAK,EAAE1C,KAAK,CAAC0C,KAAK;MAClBC,QAAQ,EAAE3C,KAAK,CAAC2C,QAAQ;MACxBS,YAAY,EAAE/D,SAAS,CAAC2D,eAAe,CAAChD,KAAK,CAACoD,YAAY,EAAE,IAAI;IAClE,CAAC,CAAC;EACJ;EAEAhB,aAAaA,CAACL,SAAS,EAAE/B,KAAK,EAAE;IAC9B+B,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACf,GAAG,EAAE;MAC3Bd,IAAI,EAAE,SAAS;MACf+B,QAAQ,EAAE3C,KAAK,CAAC2C;IAClB,CAAC,CAAC;IAEF,IAAI,CAAC1B,YAAY,CAACa,MAAM,CAACC,SAAS,EAAE/B,KAAK,CAAC;IAC1C,IAAI,CAACmB,cAAc,CAACW,MAAM,CAACC,SAAS,EAAE/B,KAAK,CAAC;IAE5C+B,SAAS,CAACa,SAAS,CAAC,CAAC;EACvB;EAEAP,gBAAgBA,CAACN,SAAS,EAAE/B,KAAK,EAAE;IACjC+B,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACf,GAAG,EAAE;MAC3Bd,IAAI,EAAE,YAAY;MAClB+B,QAAQ,EAAE3C,KAAK,CAAC2C;IAClB,CAAC,CAAC;IAEF,IAAI,CAACpB,eAAe,CAACO,MAAM,CAACC,SAAS,EAAE/B,KAAK,CAAC;IAE7C+B,SAAS,CAACa,SAAS,CAAC,CAAC;EACvB;EAEAN,aAAaA,CAACP,SAAS,EAAE/B,KAAK,EAAE;IAC9B;IACA,IAAI,CAACa,WAAW,CAACc,WAAW,CAAC3B,KAAK,CAAC,EAAE;MACnC;IACF;IAEA+B,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACf,GAAG,EAAE;MAC3Bd,IAAI,EAAE,SAAS;MACf+B,QAAQ,EAAE3C,KAAK,CAAC2C;IAClB,CAAC,CAAC;IAEF,IAAI,CAAClB,YAAY,CAACK,MAAM,CAACC,SAAS,EAAE/B,KAAK,CAAC;IAE1C+B,SAAS,CAACa,SAAS,CAAC,CAAC;EACvB;EAEAL,UAAUA,CAACR,SAAS,EAAE/B,KAAK,EAAE;IAC3B+B,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACf,GAAG,EAAE;MAC3Bd,IAAI,EAAEZ,KAAK,CAACK,QAAQ;MACpBqC,KAAK,EAAE1C,KAAK,CAAC0C,KAAK;MAClBC,QAAQ,EAAE3C,KAAK,CAAC2C,QAAQ;MACxBtC,QAAQ,EAAEhB,SAAS,CAACgE,iBAAiB,CAACrD,KAAK,CAACK,QAAQ,EAAE,cAAc;IACtE,CAAC,CAAC;IAEF,MAAMe,OAAO,GAAGrB,cAAc,CAACC,KAAK,CAAC;IACrC,IAAIoB,OAAO,EAAE;MACX,IAAI,CAACC,YAAY,CAACS,MAAM,CAACC,SAAS,EAAEX,OAAO,CAAC;IAC9C;IAEAW,SAAS,CAACa,SAAS,CAAC,CAAC;EACvB;EAEAJ,gBAAgBA,CAACT,SAAS,EAAE/B,KAAK,EAAE;IACjC+B,SAAS,CAACU,QAAQ,CAAC,IAAI,CAACf,GAAG,EAAE;MAC3Bd,IAAI,EAAE,YAAY;MAClB8B,KAAK,EAAE1C,KAAK,CAAC0C,KAAK;MAClBC,QAAQ,EAAE3C,KAAK,CAAC2C,QAAQ;MACxBlC,UAAU,EAAET,KAAK,CAACS;IACpB,CAAC,CAAC;IAEF,MAAMW,OAAO,GAAGZ,oBAAoB,CAACR,KAAK,CAAC;IAC3C,IAAIoB,OAAO,EAAE;MACX,IAAI,CAACC,YAAY,CAACS,MAAM,CAACC,SAAS,EAAEX,OAAO,CAAC;IAC9C;IAEAW,SAAS,CAACa,SAAS,CAAC,CAAC;EACvB;EAEAU,cAAcA,CAAAC,IAAA,EAAe;IAAA,IAAd;MAAC5C;IAAU,CAAC,GAAA4C,IAAA;IACzB,OAAO;MACL,GAAG7C,MAAM,CAACC,UAAU,CAAC;MACrB+B,KAAK,EAAErD,SAAS,CAAC8D,UAAU,CAACxC,UAAU,CAAC+B,KAAK,CAAC;MAC7CC,QAAQ,EAAEtD,SAAS,CAAC8D,UAAU,CAACxC,UAAU,CAACgC,QAAQ,CAAC;MACnDlC,UAAU,EAAEE,UAAU,CAACF,UAAU;MACjCsC,OAAO,EAAE1D,SAAS,CAACmE,WAAW,CAAC7C,UAAU,CAACoC,OAAO,CAAC;MAClDE,MAAM,EAAE5D,SAAS,CAACmE,WAAW,CAAC7C,UAAU,CAACsC,MAAM,CAAC;MAChDC,IAAI,EAAE7D,SAAS,CAAC8D,UAAU,CAACxC,UAAU,CAACuC,IAAI,CAAC;MAC3CE,YAAY,EAAE/D,SAAS,CAACmE,WAAW,CAAC7C,UAAU,CAACyC,YAAY;IAC7D,CAAC;EACH;EAEAK,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,QAAQD,IAAI;MACV,KAAK,SAAS;MACd,KAAK,QAAQ;MACb,KAAK,YAAY;MACjB,KAAK,SAAS;QACZ;QACAE,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7D,KAAK,EAAE2D,MAAM,CAAC3D,KAAK,CAAC;QACvC;MAEF,KAAK,SAAS;QACZ;QACA,IAAI,CAACA,KAAK,CAACC,QAAQ,GAAG,IAAI,CAACD,KAAK,CAACC,QAAQ,IAAI,EAAE;QAC/C,IAAI,CAACD,KAAK,CAACC,QAAQ,CAAC6D,IAAI,CAACH,MAAM,CAAC3D,KAAK,CAAC;QACtC;IACJ;EACF;AACF;AAEA+D,MAAM,CAACC,OAAO,GAAGnD,WAAW"}