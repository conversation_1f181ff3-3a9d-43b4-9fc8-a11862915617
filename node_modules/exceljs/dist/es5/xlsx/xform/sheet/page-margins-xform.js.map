{"version": 3, "file": "page-margins-xform.js", "names": ["_", "require", "BaseXform", "PageMarginsXform", "tag", "render", "xmlStream", "model", "attributes", "left", "right", "top", "bottom", "header", "footer", "some", "value", "undefined", "leafNode", "parseOpen", "node", "name", "parseFloat", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/page-margins-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\nconst BaseXform = require('../base-xform');\n\nclass PageMarginsXform extends BaseXform {\n  get tag() {\n    return 'pageMargins';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      const attributes = {\n        left: model.left,\n        right: model.right,\n        top: model.top,\n        bottom: model.bottom,\n        header: model.header,\n        footer: model.footer,\n      };\n      if (_.some(attributes, value => value !== undefined)) {\n        xmlStream.leafNode(this.tag, attributes);\n      }\n    }\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          left: parseFloat(node.attributes.left || 0.7),\n          right: parseFloat(node.attributes.right || 0.7),\n          top: parseFloat(node.attributes.top || 0.75),\n          bottom: parseFloat(node.attributes.bottom || 0.75),\n          header: parseFloat(node.attributes.header || 0.3),\n          footer: parseFloat(node.attributes.footer || 0.3),\n        };\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = PageMarginsXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC9C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,gBAAgB,SAASD,SAAS,CAAC;EACvC,IAAIE,GAAGA,CAAA,EAAG;IACR,OAAO,aAAa;EACtB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACT,MAAMC,UAAU,GAAG;QACjBC,IAAI,EAAEF,KAAK,CAACE,IAAI;QAChBC,KAAK,EAAEH,KAAK,CAACG,KAAK;QAClBC,GAAG,EAAEJ,KAAK,CAACI,GAAG;QACdC,MAAM,EAAEL,KAAK,CAACK,MAAM;QACpBC,MAAM,EAAEN,KAAK,CAACM,MAAM;QACpBC,MAAM,EAAEP,KAAK,CAACO;MAChB,CAAC;MACD,IAAId,CAAC,CAACe,IAAI,CAACP,UAAU,EAAEQ,KAAK,IAAIA,KAAK,KAAKC,SAAS,CAAC,EAAE;QACpDX,SAAS,CAACY,QAAQ,CAAC,IAAI,CAACd,GAAG,EAAEI,UAAU,CAAC;MAC1C;IACF;EACF;EAEAW,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACjB,GAAG;QACX,IAAI,CAACG,KAAK,GAAG;UACXE,IAAI,EAAEa,UAAU,CAACF,IAAI,CAACZ,UAAU,CAACC,IAAI,IAAI,GAAG,CAAC;UAC7CC,KAAK,EAAEY,UAAU,CAACF,IAAI,CAACZ,UAAU,CAACE,KAAK,IAAI,GAAG,CAAC;UAC/CC,GAAG,EAAEW,UAAU,CAACF,IAAI,CAACZ,UAAU,CAACG,GAAG,IAAI,IAAI,CAAC;UAC5CC,MAAM,EAAEU,UAAU,CAACF,IAAI,CAACZ,UAAU,CAACI,MAAM,IAAI,IAAI,CAAC;UAClDC,MAAM,EAAES,UAAU,CAACF,IAAI,CAACZ,UAAU,CAACK,MAAM,IAAI,GAAG,CAAC;UACjDC,MAAM,EAAEQ,UAAU,CAACF,IAAI,CAACZ,UAAU,CAACM,MAAM,IAAI,GAAG;QAClD,CAAC;QACD,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAS,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGvB,gBAAgB"}