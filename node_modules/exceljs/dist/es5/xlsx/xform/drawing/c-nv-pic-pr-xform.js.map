{"version": 3, "file": "c-nv-pic-pr-xform.js", "names": ["BaseXform", "require", "CNvPicPrXform", "tag", "render", "xmlStream", "openNode", "leafNode", "noChangeAspect", "closeNode", "parseOpen", "node", "name", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/c-nv-pic-pr-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass CNvPicPrXform extends BaseXform {\n  get tag() {\n    return 'xdr:cNvPicPr';\n  }\n\n  render(xmlStream) {\n    xmlStream.openNode(this.tag);\n    xmlStream.leafNode('a:picLocks', {\n      noChangeAspect: '1',\n    });\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        return true;\n      default:\n        return true;\n    }\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        // unprocessed internal nodes\n        return true;\n    }\n  }\n}\n\nmodule.exports = CNvPicPrXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,aAAa,SAASF,SAAS,CAAC;EACpC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAC,MAAMA,CAACC,SAAS,EAAE;IAChBA,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACH,GAAG,CAAC;IAC5BE,SAAS,CAACE,QAAQ,CAAC,YAAY,EAAE;MAC/BC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFH,SAAS,CAACI,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACT,GAAG;QACX,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAU,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACF,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,IAAI,CAACT,GAAG;QACX,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;AACF;AAEAY,MAAM,CAACC,OAAO,GAAGd,aAAa"}