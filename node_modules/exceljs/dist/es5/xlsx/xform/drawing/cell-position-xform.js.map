{"version": 3, "file": "cell-position-xform.js", "names": ["BaseXform", "require", "IntegerXform", "CellPositionXform", "constructor", "options", "tag", "map", "zero", "render", "xmlStream", "model", "openNode", "nativeCol", "nativeColOff", "nativeRow", "nativeRowOff", "closeNode", "parseOpen", "node", "parser", "name", "reset", "parseText", "text", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/cell-position-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst IntegerXform = require('../simple/integer-xform');\n\nclass CellPositionXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.tag = options.tag;\n    this.map = {\n      'xdr:col': new IntegerXform({tag: 'xdr:col', zero: true}),\n      'xdr:colOff': new IntegerXform({tag: 'xdr:colOff', zero: true}),\n      'xdr:row': new IntegerXform({tag: 'xdr:row', zero: true}),\n      'xdr:rowOff': new IntegerXform({tag: 'xdr:rowOff', zero: true}),\n    };\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n\n    this.map['xdr:col'].render(xmlStream, model.nativeCol);\n    this.map['xdr:colOff'].render(xmlStream, model.nativeColOff);\n\n    this.map['xdr:row'].render(xmlStream, model.nativeRow);\n    this.map['xdr:rowOff'].render(xmlStream, model.nativeRowOff);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.model = {\n          nativeCol: this.map['xdr:col'].model,\n          nativeColOff: this.map['xdr:colOff'].model,\n          nativeRow: this.map['xdr:row'].model,\n          nativeRowOff: this.map['xdr:rowOff'].model,\n        };\n        return false;\n      default:\n        // not quite sure how we get here!\n        return true;\n    }\n  }\n}\n\nmodule.exports = CellPositionXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,YAAY,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAEvD,MAAME,iBAAiB,SAASH,SAAS,CAAC;EACxCI,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACtB,IAAI,CAACC,GAAG,GAAG;MACT,SAAS,EAAE,IAAIL,YAAY,CAAC;QAACI,GAAG,EAAE,SAAS;QAAEE,IAAI,EAAE;MAAI,CAAC,CAAC;MACzD,YAAY,EAAE,IAAIN,YAAY,CAAC;QAACI,GAAG,EAAE,YAAY;QAAEE,IAAI,EAAE;MAAI,CAAC,CAAC;MAC/D,SAAS,EAAE,IAAIN,YAAY,CAAC;QAACI,GAAG,EAAE,SAAS;QAAEE,IAAI,EAAE;MAAI,CAAC,CAAC;MACzD,YAAY,EAAE,IAAIN,YAAY,CAAC;QAACI,GAAG,EAAE,YAAY;QAAEE,IAAI,EAAE;MAAI,CAAC;IAChE,CAAC;EACH;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACN,GAAG,CAAC;IAE5B,IAAI,CAACC,GAAG,CAAC,SAAS,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACE,SAAS,CAAC;IACtD,IAAI,CAACN,GAAG,CAAC,YAAY,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACG,YAAY,CAAC;IAE5D,IAAI,CAACP,GAAG,CAAC,SAAS,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACI,SAAS,CAAC;IACtD,IAAI,CAACR,GAAG,CAAC,YAAY,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACK,YAAY,CAAC;IAE5DN,SAAS,CAACO,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACf,GAAG;QACX,IAAI,CAACgB,KAAK,CAAC,CAAC;QACZ;MACF;QACE,IAAI,CAACF,MAAM,GAAG,IAAI,CAACb,GAAG,CAACY,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACD,MAAM,GAAGM,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQL,IAAI;MACV,KAAK,IAAI,CAACf,GAAG;QACX,IAAI,CAACK,KAAK,GAAG;UACXE,SAAS,EAAE,IAAI,CAACN,GAAG,CAAC,SAAS,CAAC,CAACI,KAAK;UACpCG,YAAY,EAAE,IAAI,CAACP,GAAG,CAAC,YAAY,CAAC,CAACI,KAAK;UAC1CI,SAAS,EAAE,IAAI,CAACR,GAAG,CAAC,SAAS,CAAC,CAACI,KAAK;UACpCK,YAAY,EAAE,IAAI,CAACT,GAAG,CAAC,YAAY,CAAC,CAACI;QACvC,CAAC;QACD,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAGzB,iBAAiB"}