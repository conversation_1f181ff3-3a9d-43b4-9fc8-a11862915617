// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.v1;

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Iam.V1";
option go_package = "google.golang.org/genproto/googleapis/iam/v1;iam";
option java_multiple_files = true;
option java_outer_classname = "OptionsProto";
option java_package = "com.google.iam.v1";
option php_namespace = "Google\\Cloud\\Iam\\V1";

// Encapsulates settings provided to GetIamPolicy.
message GetPolicyOptions {
  // Optional. The maximum policy version that will be used to format the
  // policy.
  //
  // Valid values are 0, 1, and 3. Requests specifying an invalid value will be
  // rejected.
  //
  // Requests for policies with any conditional role bindings must specify
  // version 3. Policies with no conditional role bindings may specify any valid
  // value or leave the field unset.
  //
  // The policy in the response might use the policy version that you specified,
  // or it might use a lower policy version. For example, if you specify version
  // 3, but the policy has no conditional role bindings, the response uses
  // version 1.
  //
  // To learn which resources support conditions in their IAM policies, see the
  // [IAM
  // documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
  int32 requested_policy_version = 1;
}
