// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.api.servicecontrol.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/rpc/context/attribute_context.proto";
import "google/rpc/status.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.ServiceControl.V2";
option go_package = "google.golang.org/genproto/googleapis/api/servicecontrol/v2;servicecontrol";
option java_multiple_files = true;
option java_outer_classname = "ServiceControllerProto";
option java_package = "com.google.api.servicecontrol.v2";
option objc_class_prefix = "GASC";
option php_namespace = "Google\\Cloud\\ServiceControl\\V2";
option ruby_package = "Google::Cloud::ServiceControl::V2";

// [Service Control API
// v2](https://cloud.google.com/service-infrastructure/docs/service-control/access-control)
//
// Private Preview. This feature is only available for approved services.
//
// This API provides admission control and telemetry reporting for services
// that are integrated with [Service
// Infrastructure](https://cloud.google.com/service-infrastructure).
service ServiceController {
  option (google.api.default_host) = "servicecontrol.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/servicecontrol";

  // Private Preview. This feature is only available for approved services.
  //
  // This method provides admission control for services that are integrated
  // with [Service
  // Infrastructure](https://cloud.google.com/service-infrastructure). It checks
  // whether an operation should be allowed based on the service configuration
  // and relevant policies. It must be called before the operation is executed.
  // For more information, see
  // [Admission
  // Control](https://cloud.google.com/service-infrastructure/docs/admission-control).
  //
  // NOTE: The admission control has an expected policy propagation delay of
  // 60s. The caller **must** not depend on the most recent policy changes.
  //
  // NOTE: The admission control has a hard limit of 1 referenced resources
  // per call. If an operation refers to more than 1 resources, the caller
  // must call the Check method multiple times.
  //
  // This method requires the `servicemanagement.services.check` permission
  // on the specified service. For more information, see
  // [Service Control API Access
  // Control](https://cloud.google.com/service-infrastructure/docs/service-control/access-control).
  rpc Check(CheckRequest) returns (CheckResponse) {
    option (google.api.http) = {
      post: "/v2/services/{service_name}:check"
      body: "*"
    };
  }

  // Private Preview. This feature is only available for approved services.
  //
  // This method provides telemetry reporting for services that are integrated
  // with [Service
  // Infrastructure](https://cloud.google.com/service-infrastructure). It
  // reports a list of operations that have occurred on a service. It must be
  // called after the operations have been executed. For more information, see
  // [Telemetry
  // Reporting](https://cloud.google.com/service-infrastructure/docs/telemetry-reporting).
  //
  // NOTE: The telemetry reporting has a hard limit of 1000 operations and 1MB
  // per Report call. It is recommended to have no more than 100 operations per
  // call.
  //
  // This method requires the `servicemanagement.services.report` permission
  // on the specified service. For more information, see
  // [Service Control API Access
  // Control](https://cloud.google.com/service-infrastructure/docs/service-control/access-control).
  rpc Report(ReportRequest) returns (ReportResponse) {
    option (google.api.http) = {
      post: "/v2/services/{service_name}:report"
      body: "*"
    };
  }
}

// Request message for the Check method.
message CheckRequest {
  // The service name as specified in its service configuration. For example,
  // `"pubsub.googleapis.com"`.
  //
  // See
  // [google.api.Service](https://cloud.google.com/service-management/reference/rpc/google.api#google.api.Service)
  // for the definition of a service name.
  string service_name = 1;

  // Specifies the version of the service configuration that should be used to
  // process the request. Must not be empty. Set this field to 'latest' to
  // specify using the latest configuration.
  string service_config_id = 2;

  // Describes attributes about the operation being executed by the service.
  google.rpc.context.AttributeContext attributes = 3;

  // Describes the resources and the policies applied to each resource.
  repeated ResourceInfo resources = 4;

  // Optional. Contains a comma-separated list of flags.
  string flags = 5;
}

// Describes a resource referenced in the request.
message ResourceInfo {
  // The name of the resource referenced in the request.
  string name = 1;

  // The resource type in the format of "{service}/{kind}".
  string type = 2;

  // The resource permission needed for this request.
  // The format must be "{service}/{plural}.{verb}".
  string permission = 3;

  // Optional. The identifier of the container of this resource. For Google
  // Cloud APIs, the resource container must be one of the following formats:
  //     - `projects/<project-id or project-number>`
  //     - `folders/<folder-id>`
  //     - `organizations/<organization-id>`
  // For the policy enforcement on the container level (VPCSC and Location
  // Policy check), this field takes precedence on the container extracted from
  // name when presents.
  string container = 4;

  // Optional. The location of the resource. The value must be a valid zone,
  // region or multiregion. For example: "europe-west4" or
  // "northamerica-northeast1-a"
  string location = 5;
}

// Response message for the Check method.
message CheckResponse {
  // Operation is allowed when this field is not set. Any non-'OK' status
  // indicates a denial; [google.rpc.Status.details][google.rpc.Status.details]
  // would contain additional details about the denial.
  google.rpc.Status status = 1;

  // Returns a set of request contexts generated from the `CheckRequest`.
  map<string, string> headers = 2;
}

// Request message for the Report method.
message ReportRequest {
  // The service name as specified in its service configuration. For example,
  // `"pubsub.googleapis.com"`.
  //
  // See
  // [google.api.Service](https://cloud.google.com/service-management/reference/rpc/google.api#google.api.Service)
  // for the definition of a service name.
  string service_name = 1;

  // Specifies the version of the service configuration that should be used to
  // process the request. Must not be empty. Set this field to 'latest' to
  // specify using the latest configuration.
  string service_config_id = 2;

  // Describes the list of operations to be reported. Each operation is
  // represented as an AttributeContext, and contains all attributes around an
  // API access.
  repeated google.rpc.context.AttributeContext operations = 3;
}

// Response message for the Report method.
// If the request contains any invalid data, the server returns an RPC error.
message ReportResponse {}
