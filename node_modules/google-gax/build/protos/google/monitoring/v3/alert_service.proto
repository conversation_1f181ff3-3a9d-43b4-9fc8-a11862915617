// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/monitoring/v3/alert.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Monitoring.V3";
option go_package = "google.golang.org/genproto/googleapis/monitoring/v3;monitoring";
option java_multiple_files = true;
option java_outer_classname = "AlertServiceProto";
option java_package = "com.google.monitoring.v3";
option php_namespace = "Google\\Cloud\\Monitoring\\V3";
option ruby_package = "Google::Cloud::Monitoring::V3";

// The AlertPolicyService API is used to manage (list, create, delete,
// edit) alert policies in Cloud Monitoring. An alerting policy is
// a description of the conditions under which some aspect of your
// system is considered to be "unhealthy" and the ways to notify
// people or services about this state. In addition to using this API, alert
// policies can also be managed through
// [Cloud Monitoring](https://cloud.google.com/monitoring/docs/),
// which can be reached by clicking the "Monitoring" tab in
// [Cloud console](https://console.cloud.google.com/).
service AlertPolicyService {
  option (google.api.default_host) = "monitoring.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/monitoring,"
      "https://www.googleapis.com/auth/monitoring.read";

  // Lists the existing alerting policies for the workspace.
  rpc ListAlertPolicies(ListAlertPoliciesRequest) returns (ListAlertPoliciesResponse) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*}/alertPolicies"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a single alerting policy.
  rpc GetAlertPolicy(GetAlertPolicyRequest) returns (AlertPolicy) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/alertPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new alerting policy.
  rpc CreateAlertPolicy(CreateAlertPolicyRequest) returns (AlertPolicy) {
    option (google.api.http) = {
      post: "/v3/{name=projects/*}/alertPolicies"
      body: "alert_policy"
    };
    option (google.api.method_signature) = "name,alert_policy";
  }

  // Deletes an alerting policy.
  rpc DeleteAlertPolicy(DeleteAlertPolicyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3/{name=projects/*/alertPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an alerting policy. You can either replace the entire policy with
  // a new one or replace only certain fields in the current alerting policy by
  // specifying the fields to be updated via `updateMask`. Returns the
  // updated alerting policy.
  rpc UpdateAlertPolicy(UpdateAlertPolicyRequest) returns (AlertPolicy) {
    option (google.api.http) = {
      patch: "/v3/{alert_policy.name=projects/*/alertPolicies/*}"
      body: "alert_policy"
    };
    option (google.api.method_signature) = "update_mask,alert_policy";
  }
}

// The protocol for the `CreateAlertPolicy` request.
message CreateAlertPolicyRequest {
  // Required. The [project](https://cloud.google.com/monitoring/api/v3#project_name) in
  // which to create the alerting policy. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]
  //
  // Note that this field names the parent container in which the alerting
  // policy will be written, not the name of the created policy. |name| must be
  // a host project of a Metrics Scope, otherwise INVALID_ARGUMENT error will
  // return. The alerting policy that is returned will have a name that contains
  // a normalized representation of this name as a prefix but adds a suffix of
  // the form `/alertPolicies/[ALERT_POLICY_ID]`, identifying the policy in the
  // container.
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "monitoring.googleapis.com/AlertPolicy"
    }
  ];

  // Required. The requested alerting policy. You should omit the `name` field in this
  // policy. The name will be returned in the new policy, including
  // a new `[ALERT_POLICY_ID]` value.
  AlertPolicy alert_policy = 2 [(google.api.field_behavior) = REQUIRED];
}

// The protocol for the `GetAlertPolicy` request.
message GetAlertPolicyRequest {
  // Required. The alerting policy to retrieve. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]/alertPolicies/[ALERT_POLICY_ID]
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "monitoring.googleapis.com/AlertPolicy"
    }
  ];
}

// The protocol for the `ListAlertPolicies` request.
message ListAlertPoliciesRequest {
  // Required. The [project](https://cloud.google.com/monitoring/api/v3#project_name)
  // whose alert policies are to be listed. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]
  //
  // Note that this field names the parent container in which the alerting
  // policies to be listed are stored. To retrieve a single alerting policy
  // by name, use the
  // [GetAlertPolicy][google.monitoring.v3.AlertPolicyService.GetAlertPolicy]
  // operation, instead.
  string name = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "monitoring.googleapis.com/AlertPolicy"
    }
  ];

  // If provided, this field specifies the criteria that must be met by
  // alert policies to be included in the response.
  //
  // For more details, see [sorting and
  // filtering](https://cloud.google.com/monitoring/api/v3/sorting-and-filtering).
  string filter = 5;

  // A comma-separated list of fields by which to sort the result. Supports
  // the same set of field references as the `filter` field. Entries can be
  // prefixed with a minus sign to sort by the field in descending order.
  //
  // For more details, see [sorting and
  // filtering](https://cloud.google.com/monitoring/api/v3/sorting-and-filtering).
  string order_by = 6;

  // The maximum number of results to return in a single response.
  int32 page_size = 2;

  // If this field is not empty then it must contain the `nextPageToken` value
  // returned by a previous call to this method.  Using this field causes the
  // method to return more results from the previous method call.
  string page_token = 3;
}

// The protocol for the `ListAlertPolicies` response.
message ListAlertPoliciesResponse {
  // The returned alert policies.
  repeated AlertPolicy alert_policies = 3;

  // If there might be more results than were returned, then this field is set
  // to a non-empty value. To see the additional results,
  // use that value as `page_token` in the next call to this method.
  string next_page_token = 2;

  // The total number of alert policies in all pages. This number is only an
  // estimate, and may change in subsequent pages. https://aip.dev/158
  int32 total_size = 4;
}

// The protocol for the `UpdateAlertPolicy` request.
message UpdateAlertPolicyRequest {
  // Optional. A list of alerting policy field names. If this field is not
  // empty, each listed field in the existing alerting policy is set to the
  // value of the corresponding field in the supplied policy (`alert_policy`),
  // or to the field's default value if the field is not in the supplied
  // alerting policy.  Fields not listed retain their previous value.
  //
  // Examples of valid field masks include `display_name`, `documentation`,
  // `documentation.content`, `documentation.mime_type`, `user_labels`,
  // `user_label.nameofkey`, `enabled`, `conditions`, `combiner`, etc.
  //
  // If this field is empty, then the supplied alerting policy replaces the
  // existing policy. It is the same as deleting the existing policy and
  // adding the supplied policy, except for the following:
  //
  // +   The new policy will have the same `[ALERT_POLICY_ID]` as the former
  //     policy. This gives you continuity with the former policy in your
  //     notifications and incidents.
  // +   Conditions in the new policy will keep their former `[CONDITION_ID]` if
  //     the supplied condition includes the `name` field with that
  //     `[CONDITION_ID]`. If the supplied condition omits the `name` field,
  //     then a new `[CONDITION_ID]` is created.
  google.protobuf.FieldMask update_mask = 2;

  // Required. The updated alerting policy or the updated values for the
  // fields listed in `update_mask`.
  // If `update_mask` is not empty, any fields in this policy that are
  // not in `update_mask` are ignored.
  AlertPolicy alert_policy = 3 [(google.api.field_behavior) = REQUIRED];
}

// The protocol for the `DeleteAlertPolicy` request.
message DeleteAlertPolicyRequest {
  // Required. The alerting policy to delete. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]/alertPolicies/[ALERT_POLICY_ID]
  //
  // For more information, see [AlertPolicy][google.monitoring.v3.AlertPolicy].
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "monitoring.googleapis.com/AlertPolicy"
    }
  ];
}
