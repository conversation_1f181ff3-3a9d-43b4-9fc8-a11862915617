{"version": 3, "file": "streaming.js", "sourceRoot": "", "sources": ["../../../src/streamingCalls/streaming.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAaH,gDAA2C;AAE3C,8DAA8D;AAC9D,MAAM,SAAS,GAAyB,OAAO,CAAC,WAAW,CAAC,CAAC;AAC7D,8DAA8D;AAC9D,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAgC9C;;;GAGG;AACH,IAAY,UASX;AATD,WAAY,UAAU;IACpB,+DAA+D;IAC/D,mEAAoB,CAAA;IAEpB,iEAAiE;IACjE,mEAAoB,CAAA;IAEpB,6CAA6C;IAC7C,+DAAkB,CAAA;AACpB,CAAC,EATW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QASrB;AAED,MAAa,WAAY,SAAQ,SAAS;IAOxC;;;;;;;OAOG;IACH,YAAY,IAAgB,EAAE,QAAqB,EAAE,IAAc;QACjE,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE;YAC1B,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,IAAI,KAAK,UAAU,CAAC,gBAAgB;YAC9C,QAAQ,EAAE,IAAI,KAAK,UAAU,CAAC,gBAAgB;SAC9B,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;IACH,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,MAAc;QAC1B,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC3D,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9B,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,yGAAyG;QACzG,+DAA+D;QAC/D,qEAAqE;QACrE,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;oBACtB,IAAI,EAAE,GAAG;oBACT,OAAO,EAAE,EAAE;oBACX,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QAEH,wEAAwE;QACxE,6CAA6C;QAC7C,OAAO;QACP,4FAA4F;QAC5F,kJAAkJ;QAClJ,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;YAC/B,0CAA0C;YAC1C,wEAAwE;YACxE,iEAAiE;YACjE,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;gBACtB,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI;gBACb,QAAQ;aACT,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;YACzB,yBAAW,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,SAAS,CACP,OAA+B,EAC/B,QAAY,EACZ,sBAA2C,EAAE;QAE7C,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,gBAAgB,EAAE;YAC7C,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAsB,CAAC;gBACtE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;aAC1B;iBAAM;gBACL,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,EAAE;oBACrC,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,GAAG,EAAE;wBACZ,IAAI,IAAI,CAAC,eAAe,EAAE;4BACxB,IAAI,IAAI,CAAC,MAAM,EAAE;gCACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;6BACtB;4BACD,OAAO;yBACR;wBACD,MAAM,MAAM,GAAG,OAAO,CACpB,QAAQ,EACR,IAAI,CAAC,SAAS,CACM,CAAC;wBACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;wBACrB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;wBAC3B,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,OAAO,EAAE,mBAAoB,CAAC,OAAO;oBACrC,mBAAmB,EAAE,mBAAoB,CAAC,mBAAmB;oBAC7D,iBAAiB,EAAE,mBAAoB,CAAC,iBAAiB;oBACzD,aAAa,EAAE,mBAAoB,CAAC,aAAa;iBAClD,CAAC,CAAC;gBACH,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;aAC/B;YACD,OAAO;SACR;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAsB,CAAC;QACtE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAE3B,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,gBAAgB,EAAE;YAC7C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC1B;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,cAAc,EAAE;YAC3C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC1B;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;SACtB;IACH,CAAC;CACF;AA7ID,kCA6IC"}