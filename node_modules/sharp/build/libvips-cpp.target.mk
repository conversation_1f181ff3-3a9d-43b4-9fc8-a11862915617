# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := libvips-cpp
### Rules for final target.
$(obj).target/libvips-cpp.stamp: TOOLSET := $(TOOLSET)
$(obj).target/libvips-cpp.stamp:  FORCE_DO_CMD
	$(call do_cmd,touch)

all_deps += $(obj).target/libvips-cpp.stamp
# Add target alias
.PHONY: libvips-cpp
libvips-cpp: $(obj).target/libvips-cpp.stamp

# Add target alias to "all" target.
.PHONY: all
all: libvips-cpp

