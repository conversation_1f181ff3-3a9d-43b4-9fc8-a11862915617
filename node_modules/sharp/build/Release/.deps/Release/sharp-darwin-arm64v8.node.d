cmd_Release/sharp-darwin-arm64v8.node := c++ -bundle -undefined dynamic_lookup -Wl,-search_paths_first -mmacosx-version-min=10.13 -arch arm64 -L./Release -stdlib=libc++  -o Release/sharp-darwin-arm64v8.node Release/obj.target/sharp-darwin-arm64v8/src/common.o Release/obj.target/sharp-darwin-arm64v8/src/metadata.o Release/obj.target/sharp-darwin-arm64v8/src/stats.o Release/obj.target/sharp-darwin-arm64v8/src/operations.o Release/obj.target/sharp-darwin-arm64v8/src/pipeline.o Release/obj.target/sharp-darwin-arm64v8/src/utilities.o Release/obj.target/sharp-darwin-arm64v8/src/sharp.o Release/nothing.a -L/opt/homebrew/Cellar/vips/8.16.1/lib -lvips-cpp -lvips -L/opt/homebrew/Cellar/glib/2.84.1/lib -lgio-2.0 -lgobject-2.0 -lglib-2.0 -L/opt/homebrew/opt/gettext/lib -lintl
