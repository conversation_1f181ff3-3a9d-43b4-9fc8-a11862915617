cmd_Release/obj.target/sharp-darwin-arm64v8/src/common.o := c++ -o Release/obj.target/sharp-darwin-arm64v8/src/common.o ../src/common.cc '-DNODE_GYP_MODULE_NAME=sharp-darwin-arm64v8' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_FILE_OFFSET_BITS=64' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D<PERSON><PERSON><PERSON><PERSON>_NO_PINSHARED' '-DOPENSSL_THREADS' '-DNAPI_VERSION=7' '-DNODE_ADDON_API_DISABLE_DEPRECATED' '-DNODE_API_SWALLOW_UNTHROWABLE_EXCEPTIONS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node -I/Users/<USER>/Library/Caches/node-gyp/20.19.4/src -I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/v8/include -I../node_modules/node-addon-api -I/opt/homebrew/Cellar/vips/8.16.1/include -I/opt/homebrew/opt/libarchive/include -I/opt/homebrew/Cellar/fftw/3.3.10_2/include -I/opt/homebrew/Cellar/cfitsio/4.6.2/include -I/opt/homebrew/Cellar/libimagequant/4.3.4/include -I/opt/homebrew/Cellar/cgif/0.5.0/include -I/opt/homebrew/Cellar/libexif/0.6.25/include -I/opt/homebrew/Cellar/libspng/0.7.4/include -I/opt/homebrew/opt/webp/include -I/opt/homebrew/opt/webp/include/webp -I/opt/homebrew/Cellar/librsvg/2.60.0/include/librsvg-2.0 -I/opt/homebrew/include/gdk-pixbuf-2.0 -I/opt/homebrew/Cellar/libtiff/4.7.0/include -I/opt/homebrew/opt/zstd/include -I/opt/homebrew/Cellar/xz/5.8.1/include -I/opt/homebrew/opt/jpeg-turbo/include -I/opt/homebrew/Cellar/cairo/1.18.4/include/cairo -I/opt/homebrew/Cellar/cairo/1.18.4/include -I/opt/homebrew/Cellar/pango/1.56.3/include/pango-1.0 -I/opt/homebrew/Cellar/glib/2.84.1/include -I/opt/homebrew/Cellar/fribidi/1.0.16/include/fribidi -I/opt/homebrew/Cellar/harfbuzz/11.2.1/include/harfbuzz -I/opt/homebrew/Cellar/graphite2/1.3.14/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr/include/ffi -I/opt/homebrew/Cellar/fontconfig/2.16.0/include -I/opt/homebrew/opt/freetype/include/freetype2 -I/opt/homebrew/opt/libpng/include/libpng16 -I/opt/homebrew/Cellar/libxext/1.3.6/include -I/opt/homebrew/Cellar/xorgproto/2024.1/include -I/opt/homebrew/Cellar/libxrender/0.9.12/include -I/opt/homebrew/Cellar/libx11/1.8.12/include -I/opt/homebrew/Cellar/libxcb/1.17.0/include -I/opt/homebrew/Cellar/libxau/1.0.12/include -I/opt/homebrew/Cellar/libxdmcp/1.1.5/include -I/opt/homebrew/Cellar/pixman/0.46.0/include/pixman-1 -I/opt/homebrew/Cellar/libmatio/1.5.28/include -I/opt/homebrew/opt/hdf5/include -I/opt/homebrew/opt/little-cms2/include -I/opt/homebrew/Cellar/openexr/3.3.3/include -I/opt/homebrew/Cellar/openexr/3.3.3/include/OpenEXR -I/opt/homebrew/Cellar/imath/3.1.12/include -I/opt/homebrew/Cellar/imath/3.1.12/include/Imath -I/opt/homebrew/Cellar/libdeflate/1.23/include -I/opt/homebrew/Cellar/openjpeg/2.5.3/include/openjpeg-2.5 -I/opt/homebrew/Cellar/highway/1.2.0/include -I/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0 -I/opt/homebrew/Cellar/glib/2.84.1/lib/glib-2.0/include -I/opt/homebrew/opt/gettext/include -I/usr/local/include  -O3 -gdwarf-2 -mmacosx-version-min=10.13 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=c++11 -stdlib=libc++ -fexceptions -Wall -Oz -MMD -MF ./Release/.deps/Release/obj.target/sharp-darwin-arm64v8/src/common.o.d.raw   -c
Release/obj.target/sharp-darwin-arm64v8/src/common.o: ../src/common.cc \
  ../node_modules/node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node/node_api_types.h \
  ../node_modules/node-addon-api/napi-inl.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/vips8 \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/version.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib-object.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gbinding.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/galloca.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtypes.h \
  /opt/homebrew/Cellar/glib/2.84.1/lib/glib-2.0/include/glibconfig.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmacros.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gversionmacros.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/glib-visibility.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/garray.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gasyncqueue.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gthread.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gatomic.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/glib-typeof.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gerror.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gquark.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gutils.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbacktrace.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbase64.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbitlock.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbookmarkfile.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gdatetime.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtimezone.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbytes.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gcharset.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gchecksum.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gconvert.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gdataset.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gdate.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gdir.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/genviron.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gfileutils.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ggettext.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ghash.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/glist.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmem.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gnode.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ghmac.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ghook.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ghostutils.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/giochannel.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmain.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gpoll.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gslist.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstring.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gunicode.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstrfuncs.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gkeyfile.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmappedfile.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmarkup.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmessages.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gvariant.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gvarianttype.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/goption.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gpathbuf.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gpattern.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gprimes.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gqsort.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gqueue.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/grand.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/grcbox.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/grefcount.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/grefstring.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gregex.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gscanner.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gsequence.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gshell.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gslice.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gspawn.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstringchunk.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstrvbuilder.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtestutils.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gthreadpool.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtimer.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtrashstack.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtree.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/guri.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/guuid.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gversion.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gallocator.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gcache.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gcompletion.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gmain.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/grel.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gthread.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/glib-autocleanups.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gobject.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gtype.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gobject-visibility.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gvalue.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gparam.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gclosure.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gsignal.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gmarshal.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gboxed.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/glib-types.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gbindinggroup.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/genums.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/glib-enumtypes.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gparamspecs.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gsignalgroup.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gsourceclosure.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gtypemodule.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gtypeplugin.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gvaluearray.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gvaluetypes.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gobject-autocleanups.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/VError8.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/vips.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstdio.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gprintf.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gmodule.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gmodule/gmodule-visibility.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gio.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/giotypes.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gioenums.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gio-visibility.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gaction.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gactiongroup.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gactiongroupexporter.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gactionmap.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gappinfo.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gapplication.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gapplicationcommandline.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gasyncinitable.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginitable.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gasyncresult.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gbufferedinputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfilterinputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gbufferedoutputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfilteroutputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/goutputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gbytesicon.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gcancellable.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gcharsetconverter.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gconverter.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gcontenttype.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gconverterinputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gconverteroutputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gcredentials.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdatagrambased.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdatainputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdataoutputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusactiongroup.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusaddress.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusauthobserver.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbuserror.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusinterface.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusinterfaceskeleton.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusintrospection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusmenumodel.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusmessage.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusmethodinvocation.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusnameowning.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusnamewatching.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobject.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectmanager.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectmanagerclient.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectmanagerserver.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectproxy.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectskeleton.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusproxy.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusserver.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusutils.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdebugcontroller.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdebugcontrollerdbus.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdrive.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdtlsclientconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdtlsconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdtlsserverconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gemblemedicon.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gicon.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gemblem.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfile.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileattribute.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileenumerator.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileicon.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileinfo.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileinputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileiostream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/giostream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gioerror.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfilemonitor.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfilenamecompleter.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileoutputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginetaddress.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginetaddressmask.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginetsocketaddress.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketaddress.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gioenumtypes.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/giomodule.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gioscheduler.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/glistmodel.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gliststore.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gloadableicon.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmemoryinputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmemorymonitor.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmemoryoutputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmenu.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmenumodel.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmenuexporter.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmount.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmountoperation.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnativesocketaddress.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnativevolumemonitor.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gvolumemonitor.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnetworkaddress.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnetworkmonitor.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnetworkservice.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnotification.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpermission.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpollableinputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpollableoutputstream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpollableutils.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpowerprofilemonitor.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpropertyaction.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gproxy.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gproxyaddress.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gproxyaddressenumerator.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketaddressenumerator.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gproxyresolver.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gremoteactiongroup.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gresolver.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gresource.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gseekable.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsettings.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsettingsschema.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleaction.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleactiongroup.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleasyncresult.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleiostream.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimplepermission.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleproxyresolver.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocket.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketclient.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketconnectable.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketcontrolmessage.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketlistener.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketservice.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsrvtarget.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsubprocess.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsubprocesslauncher.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtask.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtcpconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtcpwrapperconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtestdbus.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gthemedicon.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gthreadedsocketservice.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsbackend.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlscertificate.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsclientconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsdatabase.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsfiledatabase.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsinteraction.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlspassword.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsserverconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gunixconnection.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gunixcredentialsmessage.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gunixfdlist.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gunixsocketaddress.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gvfs.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gvolume.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gzlibcompressor.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gzlibdecompressor.h \
  /opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gio-autocleanups.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/basic.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/buf.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/dbuf.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/util.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/object.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/type.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/gate.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/connection.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/sbuf.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/rect.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/private.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/image.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/region.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/memory.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/error.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/format.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/generate.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/interpolate.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/semaphore.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/thread.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/threadpool.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/header.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/operation.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/foreign.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/enumtypes.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/arithmetic.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/conversion.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/convolution.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/morphology.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/mosaicing.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/histogram.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/freqfilt.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/resample.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/colour.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/draw.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/create.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/vips7compat.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/mask.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/dispatch.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/almostdeprecated.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/video.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/VImage8.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/VInterpolate8.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/VRegion8.h \
  /opt/homebrew/Cellar/vips/8.16.1/include/vips/VConnection8.h \
  ../src/common.h
../src/common.cc:
../node_modules/node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node/node_api_types.h:
../node_modules/node-addon-api/napi-inl.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/vips8:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/version.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib-object.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gbinding.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/galloca.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtypes.h:
/opt/homebrew/Cellar/glib/2.84.1/lib/glib-2.0/include/glibconfig.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmacros.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gversionmacros.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/glib-visibility.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/garray.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gasyncqueue.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gthread.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gatomic.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/glib-typeof.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gerror.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gquark.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gutils.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbacktrace.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbase64.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbitlock.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbookmarkfile.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gdatetime.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtimezone.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gbytes.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gcharset.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gchecksum.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gconvert.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gdataset.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gdate.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gdir.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/genviron.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gfileutils.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ggettext.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ghash.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/glist.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmem.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gnode.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ghmac.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ghook.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/ghostutils.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/giochannel.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmain.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gpoll.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gslist.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstring.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gunicode.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstrfuncs.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gkeyfile.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmappedfile.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmarkup.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gmessages.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gvariant.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gvarianttype.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/goption.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gpathbuf.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gpattern.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gprimes.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gqsort.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gqueue.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/grand.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/grcbox.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/grefcount.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/grefstring.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gregex.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gscanner.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gsequence.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gshell.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gslice.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gspawn.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstringchunk.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstrvbuilder.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtestutils.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gthreadpool.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtimer.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtrashstack.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gtree.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/guri.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/guuid.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gversion.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gallocator.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gcache.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gcompletion.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gmain.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/grel.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/deprecated/gthread.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/glib-autocleanups.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gobject.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gtype.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gobject-visibility.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gvalue.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gparam.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gclosure.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gsignal.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gmarshal.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gboxed.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/glib-types.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gbindinggroup.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/genums.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/glib-enumtypes.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gparamspecs.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gsignalgroup.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gsourceclosure.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gtypemodule.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gtypeplugin.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gvaluearray.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gvaluetypes.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gobject/gobject-autocleanups.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/VError8.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/vips.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gstdio.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/glib/gprintf.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gmodule.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gmodule/gmodule-visibility.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gio.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/giotypes.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gioenums.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gio-visibility.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gaction.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gactiongroup.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gactiongroupexporter.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gactionmap.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gappinfo.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gapplication.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gapplicationcommandline.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gasyncinitable.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginitable.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gasyncresult.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gbufferedinputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfilterinputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gbufferedoutputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfilteroutputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/goutputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gbytesicon.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gcancellable.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gcharsetconverter.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gconverter.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gcontenttype.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gconverterinputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gconverteroutputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gcredentials.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdatagrambased.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdatainputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdataoutputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusactiongroup.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusaddress.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusauthobserver.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbuserror.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusinterface.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusinterfaceskeleton.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusintrospection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusmenumodel.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusmessage.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusmethodinvocation.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusnameowning.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusnamewatching.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobject.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectmanager.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectmanagerclient.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectmanagerserver.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectproxy.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusobjectskeleton.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusproxy.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusserver.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdbusutils.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdebugcontroller.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdebugcontrollerdbus.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdrive.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdtlsclientconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdtlsconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gdtlsserverconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gemblemedicon.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gicon.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gemblem.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfile.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileattribute.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileenumerator.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileicon.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileinfo.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileinputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileiostream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/giostream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gioerror.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfilemonitor.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfilenamecompleter.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gfileoutputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginetaddress.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginetaddressmask.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/ginetsocketaddress.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketaddress.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gioenumtypes.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/giomodule.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gioscheduler.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/glistmodel.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gliststore.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gloadableicon.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmemoryinputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmemorymonitor.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmemoryoutputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmenu.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmenumodel.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmenuexporter.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmount.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gmountoperation.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnativesocketaddress.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnativevolumemonitor.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gvolumemonitor.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnetworkaddress.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnetworkmonitor.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnetworkservice.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gnotification.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpermission.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpollableinputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpollableoutputstream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpollableutils.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpowerprofilemonitor.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gpropertyaction.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gproxy.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gproxyaddress.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gproxyaddressenumerator.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketaddressenumerator.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gproxyresolver.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gremoteactiongroup.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gresolver.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gresource.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gseekable.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsettings.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsettingsschema.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleaction.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleactiongroup.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleasyncresult.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleiostream.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimplepermission.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsimpleproxyresolver.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocket.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketclient.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketconnectable.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketcontrolmessage.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketlistener.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsocketservice.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsrvtarget.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsubprocess.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gsubprocesslauncher.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtask.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtcpconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtcpwrapperconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtestdbus.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gthemedicon.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gthreadedsocketservice.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsbackend.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlscertificate.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsclientconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsdatabase.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsfiledatabase.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsinteraction.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlspassword.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gtlsserverconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gunixconnection.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gunixcredentialsmessage.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gunixfdlist.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gunixsocketaddress.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gvfs.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gvolume.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gzlibcompressor.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gzlibdecompressor.h:
/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0/gio/gio-autocleanups.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/basic.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/buf.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/dbuf.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/util.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/object.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/type.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/gate.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/connection.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/sbuf.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/rect.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/private.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/image.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/region.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/memory.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/error.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/format.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/generate.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/interpolate.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/semaphore.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/thread.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/threadpool.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/header.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/operation.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/foreign.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/enumtypes.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/arithmetic.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/conversion.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/convolution.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/morphology.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/mosaicing.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/histogram.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/freqfilt.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/resample.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/colour.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/draw.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/create.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/vips7compat.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/mask.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/dispatch.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/almostdeprecated.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/video.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/VImage8.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/VInterpolate8.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/VRegion8.h:
/opt/homebrew/Cellar/vips/8.16.1/include/vips/VConnection8.h:
../src/common.h:
