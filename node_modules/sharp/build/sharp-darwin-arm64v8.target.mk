# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := sharp-darwin-arm64v8
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=sharp-darwin-arm64v8' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DNAPI_VERSION=7' \
	'-DNODE_ADDON_API_DISABLE_DEPRECATED' \
	'-DNODE_API_SWALLOW_UNTHROWABLE_EXCEPTIONS' \
	'-DBUILDING_NODE_EXTENSION' \
	'-DDEBUG' \
	'-D_DEBUG'

# Flags passed to all source files.
CFLAGS_Debug := \
	-O0 \
	-gdwarf-2 \
	-mmacosx-version-min=10.13 \
	-arch arm64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter

# Flags passed to only C files.
CFLAGS_C_Debug := \
	-fno-strict-aliasing

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-std=c++11 \
	-stdlib=libc++ \
	-fexceptions \
	-Wall \
	-Oz

# Flags passed to only ObjC files.
CFLAGS_OBJC_Debug :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Debug :=

INCS_Debug := \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/src \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/v8/include \
	-I$(srcdir)/node_modules/node-addon-api \
	-I/opt/homebrew/Cellar/vips/8.16.1/include \
	-I/opt/homebrew/opt/libarchive/include \
	-I/opt/homebrew/Cellar/fftw/3.3.10_2/include \
	-I/opt/homebrew/Cellar/cfitsio/4.6.2/include \
	-I/opt/homebrew/Cellar/libimagequant/4.3.4/include \
	-I/opt/homebrew/Cellar/cgif/0.5.0/include \
	-I/opt/homebrew/Cellar/libexif/0.6.25/include \
	-I/opt/homebrew/Cellar/libspng/0.7.4/include \
	-I/opt/homebrew/opt/webp/include \
	-I/opt/homebrew/opt/webp/include/webp \
	-I/opt/homebrew/Cellar/librsvg/2.60.0/include/librsvg-2.0 \
	-I/opt/homebrew/include/gdk-pixbuf-2.0 \
	-I/opt/homebrew/Cellar/libtiff/4.7.0/include \
	-I/opt/homebrew/opt/zstd/include \
	-I/opt/homebrew/Cellar/xz/5.8.1/include \
	-I/opt/homebrew/opt/jpeg-turbo/include \
	-I/opt/homebrew/Cellar/cairo/1.18.4/include/cairo \
	-I/opt/homebrew/Cellar/cairo/1.18.4/include \
	-I/opt/homebrew/Cellar/pango/1.56.3/include/pango-1.0 \
	-I/opt/homebrew/Cellar/glib/2.84.1/include \
	-I/opt/homebrew/Cellar/fribidi/1.0.16/include/fribidi \
	-I/opt/homebrew/Cellar/harfbuzz/11.2.1/include/harfbuzz \
	-I/opt/homebrew/Cellar/graphite2/1.3.14/include \
	-I/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr/include/ffi \
	-I/opt/homebrew/Cellar/fontconfig/2.16.0/include \
	-I/opt/homebrew/opt/freetype/include/freetype2 \
	-I/opt/homebrew/opt/libpng/include/libpng16 \
	-I/opt/homebrew/Cellar/libxext/1.3.6/include \
	-I/opt/homebrew/Cellar/xorgproto/2024.1/include \
	-I/opt/homebrew/Cellar/libxrender/0.9.12/include \
	-I/opt/homebrew/Cellar/libx11/1.8.12/include \
	-I/opt/homebrew/Cellar/libxcb/1.17.0/include \
	-I/opt/homebrew/Cellar/libxau/1.0.12/include \
	-I/opt/homebrew/Cellar/libxdmcp/1.1.5/include \
	-I/opt/homebrew/Cellar/pixman/0.46.0/include/pixman-1 \
	-I/opt/homebrew/Cellar/libmatio/1.5.28/include \
	-I/opt/homebrew/opt/hdf5/include \
	-I/opt/homebrew/opt/little-cms2/include \
	-I/opt/homebrew/Cellar/openexr/3.3.3/include \
	-I/opt/homebrew/Cellar/openexr/3.3.3/include/OpenEXR \
	-I/opt/homebrew/Cellar/imath/3.1.12/include \
	-I/opt/homebrew/Cellar/imath/3.1.12/include/Imath \
	-I/opt/homebrew/Cellar/libdeflate/1.23/include \
	-I/opt/homebrew/Cellar/openjpeg/2.5.3/include/openjpeg-2.5 \
	-I/opt/homebrew/Cellar/highway/1.2.0/include \
	-I/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0 \
	-I/opt/homebrew/Cellar/glib/2.84.1/lib/glib-2.0/include \
	-I/opt/homebrew/opt/gettext/include \
	-I/usr/local/include

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=sharp-darwin-arm64v8' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DNAPI_VERSION=7' \
	'-DNODE_ADDON_API_DISABLE_DEPRECATED' \
	'-DNODE_API_SWALLOW_UNTHROWABLE_EXCEPTIONS' \
	'-DBUILDING_NODE_EXTENSION'

# Flags passed to all source files.
CFLAGS_Release := \
	-O3 \
	-gdwarf-2 \
	-mmacosx-version-min=10.13 \
	-arch arm64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter

# Flags passed to only C files.
CFLAGS_C_Release := \
	-fno-strict-aliasing

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-std=c++11 \
	-stdlib=libc++ \
	-fexceptions \
	-Wall \
	-Oz

# Flags passed to only ObjC files.
CFLAGS_OBJC_Release :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Release :=

INCS_Release := \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/src \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/20.19.4/deps/v8/include \
	-I$(srcdir)/node_modules/node-addon-api \
	-I/opt/homebrew/Cellar/vips/8.16.1/include \
	-I/opt/homebrew/opt/libarchive/include \
	-I/opt/homebrew/Cellar/fftw/3.3.10_2/include \
	-I/opt/homebrew/Cellar/cfitsio/4.6.2/include \
	-I/opt/homebrew/Cellar/libimagequant/4.3.4/include \
	-I/opt/homebrew/Cellar/cgif/0.5.0/include \
	-I/opt/homebrew/Cellar/libexif/0.6.25/include \
	-I/opt/homebrew/Cellar/libspng/0.7.4/include \
	-I/opt/homebrew/opt/webp/include \
	-I/opt/homebrew/opt/webp/include/webp \
	-I/opt/homebrew/Cellar/librsvg/2.60.0/include/librsvg-2.0 \
	-I/opt/homebrew/include/gdk-pixbuf-2.0 \
	-I/opt/homebrew/Cellar/libtiff/4.7.0/include \
	-I/opt/homebrew/opt/zstd/include \
	-I/opt/homebrew/Cellar/xz/5.8.1/include \
	-I/opt/homebrew/opt/jpeg-turbo/include \
	-I/opt/homebrew/Cellar/cairo/1.18.4/include/cairo \
	-I/opt/homebrew/Cellar/cairo/1.18.4/include \
	-I/opt/homebrew/Cellar/pango/1.56.3/include/pango-1.0 \
	-I/opt/homebrew/Cellar/glib/2.84.1/include \
	-I/opt/homebrew/Cellar/fribidi/1.0.16/include/fribidi \
	-I/opt/homebrew/Cellar/harfbuzz/11.2.1/include/harfbuzz \
	-I/opt/homebrew/Cellar/graphite2/1.3.14/include \
	-I/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr/include/ffi \
	-I/opt/homebrew/Cellar/fontconfig/2.16.0/include \
	-I/opt/homebrew/opt/freetype/include/freetype2 \
	-I/opt/homebrew/opt/libpng/include/libpng16 \
	-I/opt/homebrew/Cellar/libxext/1.3.6/include \
	-I/opt/homebrew/Cellar/xorgproto/2024.1/include \
	-I/opt/homebrew/Cellar/libxrender/0.9.12/include \
	-I/opt/homebrew/Cellar/libx11/1.8.12/include \
	-I/opt/homebrew/Cellar/libxcb/1.17.0/include \
	-I/opt/homebrew/Cellar/libxau/1.0.12/include \
	-I/opt/homebrew/Cellar/libxdmcp/1.1.5/include \
	-I/opt/homebrew/Cellar/pixman/0.46.0/include/pixman-1 \
	-I/opt/homebrew/Cellar/libmatio/1.5.28/include \
	-I/opt/homebrew/opt/hdf5/include \
	-I/opt/homebrew/opt/little-cms2/include \
	-I/opt/homebrew/Cellar/openexr/3.3.3/include \
	-I/opt/homebrew/Cellar/openexr/3.3.3/include/OpenEXR \
	-I/opt/homebrew/Cellar/imath/3.1.12/include \
	-I/opt/homebrew/Cellar/imath/3.1.12/include/Imath \
	-I/opt/homebrew/Cellar/libdeflate/1.23/include \
	-I/opt/homebrew/Cellar/openjpeg/2.5.3/include/openjpeg-2.5 \
	-I/opt/homebrew/Cellar/highway/1.2.0/include \
	-I/opt/homebrew/Cellar/glib/2.84.1/include/glib-2.0 \
	-I/opt/homebrew/Cellar/glib/2.84.1/lib/glib-2.0/include \
	-I/opt/homebrew/opt/gettext/include \
	-I/usr/local/include

OBJS := \
	$(obj).target/$(TARGET)/src/common.o \
	$(obj).target/$(TARGET)/src/metadata.o \
	$(obj).target/$(TARGET)/src/stats.o \
	$(obj).target/$(TARGET)/src/operations.o \
	$(obj).target/$(TARGET)/src/pipeline.o \
	$(obj).target/$(TARGET)/src/utilities.o \
	$(obj).target/$(TARGET)/src/sharp.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# Make sure our dependencies are built before any of us.
$(OBJS): | $(builddir)/nothing.a $(obj).target/libvips-cpp.stamp

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))
$(OBJS): GYP_OBJCFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE)) $(CFLAGS_OBJC_$(BUILDTYPE))
$(OBJS): GYP_OBJCXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE)) $(CFLAGS_OBJCC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=10.13 \
	-arch arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Debug := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LDFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first \
	-mmacosx-version-min=10.13 \
	-arch arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Release := \
	-undefined dynamic_lookup \
	-Wl,-search_paths_first

LIBS := \
	-L/opt/homebrew/Cellar/vips/8.16.1/lib \
	-lvips-cpp \
	-lvips \
	-L/opt/homebrew/Cellar/glib/2.84.1/lib \
	-lgio-2.0 \
	-lgobject-2.0 \
	-lglib-2.0 \
	-L/opt/homebrew/opt/gettext/lib \
	-lintl

$(builddir)/sharp-darwin-arm64v8.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/sharp-darwin-arm64v8.node: LIBS := $(LIBS)
$(builddir)/sharp-darwin-arm64v8.node: GYP_LIBTOOLFLAGS := $(LIBTOOLFLAGS_$(BUILDTYPE))
$(builddir)/sharp-darwin-arm64v8.node: TOOLSET := $(TOOLSET)
$(builddir)/sharp-darwin-arm64v8.node: $(OBJS) $(builddir)/nothing.a FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(builddir)/sharp-darwin-arm64v8.node
# Add target alias
.PHONY: sharp-darwin-arm64v8
sharp-darwin-arm64v8: $(builddir)/sharp-darwin-arm64v8.node

# Short alias for building this executable.
.PHONY: sharp-darwin-arm64v8.node
sharp-darwin-arm64v8.node: $(builddir)/sharp-darwin-arm64v8.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/sharp-darwin-arm64v8.node

