// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.firestore.admin.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.Firestore.Admin.V1";
option go_package = "cloud.google.com/go/firestore/apiv1/admin/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "DatabaseProto";
option java_package = "com.google.firestore.admin.v1";
option objc_class_prefix = "GCFS";
option php_namespace = "Google\\Cloud\\Firestore\\Admin\\V1";
option ruby_package = "Google::Cloud::Firestore::Admin::V1";

// A Cloud Firestore Database.
// Currently only one database is allowed per cloud project; this database
// must have a `database_id` of '(default)'.
message Database {
  option (google.api.resource) = {
    type: "firestore.googleapis.com/Database"
    pattern: "projects/{project}/databases/{database}"
    style: DECLARATIVE_FRIENDLY
  };

  // The type of the database.
  // See https://cloud.google.com/datastore/docs/firestore-or-datastore for
  // information about how to choose.
  //
  // Mode changes are only allowed if the database is empty.
  enum DatabaseType {
    // The default value. This value is used if the database type is omitted.
    DATABASE_TYPE_UNSPECIFIED = 0;

    // Firestore Native Mode
    FIRESTORE_NATIVE = 1;

    // Firestore in Datastore Mode.
    DATASTORE_MODE = 2;
  }

  // The type of concurrency control mode for transactions.
  enum ConcurrencyMode {
    // Not used.
    CONCURRENCY_MODE_UNSPECIFIED = 0;

    // Use optimistic concurrency control by default. This mode is available
    // for Cloud Firestore databases.
    OPTIMISTIC = 1;

    // Use pessimistic concurrency control by default. This mode is available
    // for Cloud Firestore databases.
    //
    // This is the default setting for Cloud Firestore.
    PESSIMISTIC = 2;

    // Use optimistic concurrency control with entity groups by default.
    //
    // This is the only available mode for Cloud Datastore.
    //
    // This mode is also available for Cloud Firestore with Datastore Mode but
    // is not recommended.
    OPTIMISTIC_WITH_ENTITY_GROUPS = 3;
  }

  // The type of App Engine integration mode.
  enum AppEngineIntegrationMode {
    // Not used.
    APP_ENGINE_INTEGRATION_MODE_UNSPECIFIED = 0;

    // If an App Engine application exists in the same region as this database,
    // App Engine configuration will impact this database. This includes
    // disabling of the application & database, as well as disabling writes to
    // the database.
    ENABLED = 1;

    // Appengine has no affect on the ability of this database to serve
    // requests.
    DISABLED = 2;
  }

  // The resource name of the Database.
  // Format: `projects/{project}/databases/{database}`
  string name = 1;

  // The location of the database. Available databases are listed at
  // https://cloud.google.com/firestore/docs/locations.
  string location_id = 9;

  // The type of the database.
  // See https://cloud.google.com/datastore/docs/firestore-or-datastore for
  // information about how to choose.
  DatabaseType type = 10;

  // The concurrency control mode to use for this database.
  ConcurrencyMode concurrency_mode = 15;

  // The App Engine integration mode to use for this database.
  AppEngineIntegrationMode app_engine_integration_mode = 19;

  // Output only. The key_prefix for this database. This key_prefix is used, in combination
  // with the project id ("<key prefix>~<project id>") to construct the
  // application id that is returned from the Cloud Datastore APIs in Google App
  // Engine first generation runtimes.
  //
  // This value may be empty in which case the appid to use for URL-encoded keys
  // is the project_id (eg: foo instead of v~foo).
  string key_prefix = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 99;
}
