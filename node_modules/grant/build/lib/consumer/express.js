'use strict';

var express = require('express');
var _consumer = require('../consumer');

module.exports = function (config) {
  var app = express();
  var consumer = _consumer({ config });
  app.config = consumer.config;

  app.use('/connect/:provider/:override?', function (req, res, next) {
    if (!req.session) {
      throw new Error('Grant: mount session middleware first');
    }
    if (req.method === 'POST' && !req.body) {
      throw new Error('Grant: mount body parser middleware first');
    }

    // callback
    if (req.method === 'GET' && req.params.override === 'callback') {
      var session = req.session.grant = req.session.grant || {};
      var query = req.query;
      var state = res.locals.grant = res.locals.grant || {};

      consumer.callback({ session, query, state }) // mutates session/state
      .then(function (_ref) {
        var url = _ref.url,
            error = _ref.error;
        return error ? res.end(error) : url ? redirect(req, res, url) : next();
      });
    }

    // connect
    else {
        var session = req.session.grant = {};
        session.provider = req.params.provider;

        if (req.params.override) {
          session.override = req.params.override;
        }
        if (req.method === 'GET' && Object.keys(req.query || {}).length) {
          session.dynamic = req.query;
        } else if (req.method === 'POST' && Object.keys(req.body || {}).length) {
          session.dynamic = req.body;
        }

        var state = res.locals.grant;

        consumer.connect({ session, state }) // mutates session
        .then(function (_ref2) {
          var url = _ref2.url,
              error = _ref2.error;
          return error ? res.end(error) : redirect(req, res, url);
        });
      }
  });

  return app;
};

var redirect = function redirect(req, res, url) {
  return typeof req.session.save === 'function' && Object.getPrototypeOf(req.session).save.length ? req.session.save(function () {
    return res.redirect(url);
  }) : res.redirect(url);
};