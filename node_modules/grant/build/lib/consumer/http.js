'use strict';

function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

var http = require('http');
var qs = require('qs');
var cookie = require('cookie');
var signature = require('cookie-signature');
var _consumer = require('../consumer');

var _store = function _store() {
  var cache = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return {
    get: function () {
      var _ref2 = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee(_ref) {
        var id = _ref.id;
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                return _context.abrupt('return', cache[id]);

              case 1:
              case 'end':
                return _context.stop();
            }
          }
        }, _callee, undefined);
      }));

      function get(_x2) {
        return _ref2.apply(this, arguments);
      }

      return get;
    }(),
    set: function () {
      var _ref4 = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee2(_ref3) {
        var id = _ref3.id,
            state = _ref3.state;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                return _context2.abrupt('return', cache[id] = state);

              case 1:
              case 'end':
                return _context2.stop();
            }
          }
        }, _callee2, undefined);
      }));

      function set(_x3) {
        return _ref4.apply(this, arguments);
      }

      return set;
    }()
  };
};

var store = _store();
var secret = 'grant';

// /:path*/connect/:provider/:override? + querystring
var regex = /^(?:\/([^\\/]+?(?:\/[^\\/]+?)*))?\/connect\/([^\\/]+?)(?:\/([^\\/]+?))?\/?(?:\?([^/]+))?$/i;

module.exports = function (config) {
  var _this = this;

  var app = http.createServer();
  var consumer = _consumer({ config });
  app.config = consumer.config;

  app.on('request', function () {
    var _ref5 = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee3(req, res) {
      var match, provider, override, query, _cookie$parse, grant, id, state, _ref6, error, url, body, _sign, sid, data, header, _ref7;

      return regeneratorRuntime.wrap(function _callee3$(_context3) {
        while (1) {
          switch (_context3.prev = _context3.next) {
            case 0:
              match = regex.exec(req.url);

              if (match) {
                _context3.next = 3;
                break;
              }

              return _context3.abrupt('return');

            case 3:
              provider = match[2];
              override = match[3];
              query = qs.parse(match[4]);

              // callback

              if (!(req.method === 'GET' && override === 'callback')) {
                _context3.next = 22;
                break;
              }

              _cookie$parse = cookie.parse(req.headers.cookie), grant = _cookie$parse.grant;
              id = signature.unsign(grant, secret);
              _context3.next = 11;
              return store.get({ id });

            case 11:
              state = _context3.sent;
              _context3.next = 14;
              return consumer.callback({ state, query });

            case 14:
              _ref6 = _context3.sent;
              error = _ref6.error;
              url = _ref6.url;
              _context3.next = 19;
              return store.set({ id, state });

            case 19:
              // when callback is missing
              if (error) {
                res.statusCode = 400;
                res.end(error);
              } else {
                res.statusCode = 302;
                res.setHeader('location', url);
                res.end();
              }
              _context3.next = 48;
              break;

            case 22:
              state = {};

              state.provider = provider;

              if (override) {
                state.override = override;
              }

              if (!(req.method === 'GET' && Object.keys(query).length)) {
                _context3.next = 29;
                break;
              }

              state.dynamic = query;
              _context3.next = 36;
              break;

            case 29:
              if (!(req.method === 'POST')) {
                _context3.next = 36;
                break;
              }

              _context3.t0 = qs;
              _context3.next = 33;
              return buffer(res);

            case 33:
              _context3.t1 = _context3.sent;
              body = _context3.t0.parse.call(_context3.t0, _context3.t1);

              if (Object.keys(body).length) {
                state.dynamic = body;
              }

            case 36:

              // session
              id = uuid();
              // cookie

              _sign = sign({ id, secret }), sid = _sign.sid, data = _sign.data;
              header = [].concat(res.getHeader('set-cookie'), data).filter(Boolean);

              res.setHeader('set-cookie', header);

              _context3.next = 42;
              return consumer.connect({ state });

            case 42:
              _ref7 = _context3.sent;
              error = _ref7.error;
              url = _ref7.url;
              _context3.next = 47;
              return store.set({ id, state });

            case 47:
              // when callback is missing
              if (error) {
                res.statusCode = 400;
                res.end(error);
              } else {
                res.statusCode = 302;
                res.setHeader('location', url);
                res.end();
              }

            case 48:
            case 'end':
              return _context3.stop();
          }
        }
      }, _callee3, _this);
    }));

    return function (_x4, _x5) {
      return _ref5.apply(this, arguments);
    };
  }());

  return app;
};

var uuid = function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0;
    var v = c == 'x' ? r : r & 0x3 | 0x8;
    return v.toString(16);
  });
};

var sign = function sign(_ref8) {
  var id = _ref8.id,
      secret = _ref8.secret;

  var name = 'grant';
  var options = { path: '/', httpOnly: true, secure: false, maxAge: null };
  var sid = signature.sign(id, secret);
  var data = cookie.serialize(name, sid, options);
  return { sid, data };
};

var buffer = function buffer(res) {
  return new Promise(function (resolve, reject) {
    var body = [];
    res.on('data', function (chunk) {
      return body.push(chunk);
    }).on('end', function () {
      return resolve(Buffer.concat(body).toString('utf8'));
    }).on('error', reject);
  });
};