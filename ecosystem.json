{"apps": [{"name": "PSA", "script": "src", "log_date_format": "YYYY-MM-DD HH:mm Z", "cron_restart": "30 16 * * *", "error_file": "/dev/null", "out_file": "/dev/null", "instances": 1, "node_args": "--max-old-space-size=1024", "max_memory_restart": "1024M", "env": {"NODE_ENV": "uat", "PORT": 4040, "LOGLEVEL": "DEBUG", "AWS_ACCESS_KEY_ID": "********************", "AWS_SECRET_ACCESS_KEY": "teTultZagygcr+sZQRFASmxmvidpJGH9m32ityoR"}, "env_prod": {"NODE_ENV": "production", "PORT": 4040, "LOGLEVEL": "INFO", "AWS_ACCESS_KEY_ID": "AWSKEY", "AWS_SECRET_ACCESS_KEY": "AWSSECRET", "SMS_USR": "SMSUSER", "SMS_PWD": "SMSPWD"}}]}