#!/bin/bash
# Usage: ./dataarchive.sh "mongodb connection" "collection" "data-retention" "file-retention" "s3 bucket"
currentFolder=./script/dataarchive

mongodb=$1
tableName=$2
dataRetention=$3
fileRetention=$4
bucketStorage=$5
startDate=`date --date="${dataRetention} days ago" +"%Y-%m-%d"`
namefile="${currentFolder}/${tableName}archive_${startDate}.json"

[ -d ${currentFolder} ] || mkdir -p ${currentFolder}

echo "db.${tableName}.find({ createdAt: { \$lte: ISODate('${startDate}T00:00:00Z') } }).forEach(function(result) {
  if ('${tableName}' === 'conversations') {
    var messages = db.messages.find({ conversationId: result._id }).count()
    if (messages < 1) {
	  print(JSON.stringify(result));
	  db.${tableName}.deleteOne(result);
    }
  } else {
    print(JSON.stringify(result));
    db.${tableName}.deleteOne(result);
  }
});" >> ${currentFolder}/dataarchive${tableName}.js

mongo --quiet ${mongodb} ${currentFolder}/dataarchive${tableName}.js >> $namefile

rm ${currentFolder}/dataarchive${tableName}.js

#comment temp
aws s3 cp $namefile s3://${bucketStorage}/dataarchive/${tableName}/

#for delete local file and s3 old file
find ${currentFolder}/ -regex '.*'${tableName}'archive.*' -mtime +${fileRetention} -exec rm '{}' \;
