#!/bin/sh
startDate=`date --date="7 days ago" +"%Y-%m-%d"`
currentFolder=./script/
echo "Start archive data $startDate"
cat <<< '
db.messages.find({createdAt: {$lte: ISODate("'$startDate'T00:00:00Z") }}).forEach(function(doc){db.messages_archive_data.insert(doc);db.messages.deleteOne(doc);});
' >> $currentFolder/messagesarchive.js
mongo --quiet "$1" $currentFolder/messagesarchive.js
rm $currentFolder/messagesarchive.js
