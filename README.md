# psa-messaging

> A webservice for psa messaging app

## About

This project uses [<PERSON><PERSON><PERSON>](http://feathersjs.com). An open source web framework for building modern real-time applications.

## Getting Started

Getting up and running is as easy as 1, 2, 3.

1. Make sure you have [NodeJS](https://nodejs.org/) and [npm](https://www.npmjs.com/) installed.
2. Install your dependencies

    ```
    cd path/to/psa-messaging
    npm install
    ```

3. Start your app

    ```
    npm start
    ```

### Environment setup for Windows system

**Before reading these steps, please make sure you have already cloned the Git repository to your desired directory.**

This is the recommended setup for developers who are using Windows:

1. Download [Docker](https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe?utm_source=docker&utm_medium=webreferral&utm_campaign=dd-smartbutton&utm_location=module)

2. After opening Docker, you MAY be prompted to download and install Linux kernel update package. Simply follow **steps #4 & #5** in [this manual](https://docs.microsoft.com/en-us/windows/wsl/install-manual#step-4---download-the-linux-kernel-update-package)
  - Note: you need to restart your Docker after this installation

3. Navigate to your repo root directory and run `npm i` in your terminal to setup the `node_modules` packages for your project
  - _If you only need to setup the project environment, the steps end here._

4. Navigate to your repo root directory and run `docker-compose up` in your terminal if you want to run this webservice in local
  - To stop your webservice in local, just use `Ctrl+C` in the terminal that is running your webservice

## Testing

Simply run `npm test` and all your tests in the `test/` directory will be run.

## Scaffolding

Feathers has a powerful command line interface. Here are a few things it can do:

```
$ npm install -g @feathersjs/cli          # Install Feathers CLI

$ feathers generate service               # Generate a new Service
$ feathers generate hook                  # Generate a new Hook
$ feathers help                           # Show all commands
```

## Help

For more information on all the things you can do with Feathers visit [docs.feathersjs.com](http://docs.feathersjs.com).
