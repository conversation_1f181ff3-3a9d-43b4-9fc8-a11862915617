version: "3.4"
services:

  redis:
    container_name: psa-redis
    image: redis:3-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/redis/data
    networks:
      - psa

  mongodb:
    container_name: psa-mongodb
    image: mongo
    environment:
      - "MONGO_INITDB_DATABASE:psa-messaging"
      - "MONGO_INITDB_ROOT_USERNAME:root"
      - "MONGO_INITDB_ROOT_PASSWORD:rootpassword"
    ports:
      - 27017:27017
    expose:
      - "27017"
    volumes:
      - mongodb-data:/data/db
    networks:
      - psa

  mailcatcher:
    container_name: psa-mailcatcher
    image: tophfr/mailcatcher
    restart: on-failure:10
    ports:
      - "1080:80"
      - "1025:25"
    networks:
      - psa

  webservice:
    container_name: psa-webservice
    image: node:12-alpine
    depends_on:
      - redis
      - mongodb
    ports:
      - 3030:3030
    expose:
      - "3030"
    volumes:
      - type: bind
        source: ./
        target: /app
      - type: volume
        source: nodemodules # name of the volume, see below
        target: /app/node_modules
        volume:
          nocopy: true
    working_dir: /app
    command: sh -c 'yarn && yarn dev'
    networks:
      - psa


networks:
  psa:

volumes:
  mongodb-data:
  redis-data:
  nodemodules: