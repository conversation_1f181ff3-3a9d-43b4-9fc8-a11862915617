const assert = require('assert');
const app = require('../../src/app');

describe('\'otp\' service', function (){
  this.timeout(40000);
  const service = app.service('otp');

  it('registered the service', () => {
    assert.ok(service, 'Registered the service');
  });


  it('Generate OTP code', async () => {
    const token = await service.generate('my-secret-for-otp-1');

    assert.ok(token, 'OTP generated');
  });


  it('validate correct OTP code', async () => {
    const token = await service.generate('my-secret-for-otp-2');

    const validate = await service.validate({secret: 'my-secret-for-otp-2', token}).catch(() => {
      assert.fail('was supposed to success');
    });

    assert.ok(validate, 'OTP validated');
  });


  it('validate invalid OTP code', async () => {
    try {
      await service.validate({secret: 'my-secret-for-otp-3', token: 123456 });
      assert.fail('was supposed to failed');
    } catch (err) {
      assert.ok(true, 'OTP Invalid'); 
    }
  });


  it('generate two OTP code and validate first OTP code within valid time', async () => {
    try {
      const token = await service.generate('my-secret-for-otp-4');
      await service.generate('my-secret-for-otp-4');

      await service.validate({secret: 'my-secret-for-otp-4', token});

      assert.fail('was supposed to failed'); 
    } catch (error) {
      assert.ok(true, 'OTP Invalid');
    }
  });


  it('validate expired OTP code', async () => {
    const token = await service.generate('my-secret-for-otp-5'); 
    setTimeout(async () => {
      console.log(token);
      await service.validate({secret: 'my-secret-for-otp-5', token }).then(()=>{
        assert.fail('was supposed to failed');
      }).catch(err => {
        assert.ok(err, 'OTP Expired');
      });
    }, 35000);
  });
});
