{"name": "webservice-2", "description": "", "version": "********", "homepage": "", "private": true, "main": "src", "keywords": ["feathers"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [], "bugs": {}, "directories": {"lib": "src", "test": "test/", "config": "config/"}, "engines": {"node": ">= 12.0.0", "yarn": ">= 0.18.0"}, "scripts": {"test": "yarn lint", "lint": "yarn run standard src/. --fix", "dev": "nodemon src/", "start": "node src/", "mocha": "NODE_ENV=test mocha test/ --recursive --exit"}, "standard": {"env": ["mocha"], "ignore": []}, "dependencies": {"@aws-sdk/client-s3": "3.535.0", "@feathersjs/authentication": "^4.5.18", "@feathersjs/authentication-local": "^4.5.18", "@feathersjs/authentication-oauth": "^4.5.18", "@feathersjs/configuration": "^4.5.17", "@feathersjs/errors": "^4.5.17", "@feathersjs/express": "^4.5.18", "@feathersjs/feathers": "^4.5.17", "@feathersjs/socketio": "^4.5.18", "@feathersjs/transport-commons": "^4.5.18", "@multigunagemilang/multer-sharp-addon": "^1.0.4", "adm-zip": "^0.5.9", "agenda": "^5.0.0", "axios": "^1.8.2", "bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^2.1.0", "date-fns": "^2.29.3", "ejs": "^3.1.8", "exceljs": "^4.4.0", "feathers-hooks-common": "^6.1.5", "feathers-mailer": "^3.0.1", "feathers-mongodb-fuzzy-search": "^2.0.1", "feathers-mongoose": "^8.5.1", "feathers-sync": "3.0.3", "feathers-validate-joi": "^4.0.1", "file-type": "^16.3.0", "firebase-admin": "^11.11.1", "helmet": "^6.2.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "log4js": "6.9.1", "module-alias": "^2.2.2", "moment": "^2.29.4", "moment-timezone": "^0.5.39", "mongoose": "^6.13.8", "multer": "^2.0.1", "nodemailer": "^6.10.0", "nodemailer-smtp-transport": "^2.7.4", "p-limit": "^3.1.0", "qs": "^6.11.0", "redis": "4.7.1", "serve-favicon": "^2.5.0", "speakeasy": "^2.0.0", "stream": "^0.0.2"}, "devDependencies": {"eslint": "^8.57.1", "mocha": "^10.8.2", "standard": "^17.1.2"}, "overrides": {"@aws-sdk/types": "3.535.0", "@aws-sdk/util-locate-window": "3.535.0"}, "resolutions": {"@aws-sdk/types": "3.535.0", "@aws-sdk/util-locate-window": "3.535.0"}, "_moduleAliases": {"@hooks": "src/hooks", "@utils": "src/utils", "@schema": "src/schema"}}