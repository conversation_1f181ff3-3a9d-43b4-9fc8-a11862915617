# Configurable variables
PROJECT = webservice
VERSION ?=
APP = $(PROJECT)
FILE_ROOT = ctr-artifacts/$(PROJECT)
DEST = .

ifeq ($(filter deploy,$(MAKECMDGOALS)),deploy)
  ENV := prod
else
  ENV := uat
endif

ifeq ($(ENV),uat)
  BUCKET = cdas-uat-deployment
  ZIP_SUFFIX = -uat.zip
  PM2_ENV = uat
else ifeq ($(ENV),prod)
  BUCKET = cdas-prod-deployment
  ZIP_SUFFIX = .zip
  PM2_ENV = prod
else
  $(error ENV is missing or invalid for target "$(MAKECMDGOALS)")
endif

FILE_NAME = $(PROJECT)-$(VERSION)$(ZIP_SUFFIX)
FILE_PATH = $(FILE_ROOT)/$(VERSION)/$(FILE_NAME)
LOCAL_ZIP = ~/deployment/$(FILE_NAME)

# Styling
ECHO_INFO = echo -e "\033[1;34m[INFO]\033[0m"
ECHO_WARN = echo -e "\033[1;33m[WARN]\033[0m"
ECHO_ERROR = echo -e "\033[1;31m[ERROR]\033[0m"

deploy: require-version check-tools clear fetch unzip install restart
deploy-uat: require-version check-tools clear fetch unzip install restart

# Steps
require-version:
	@if [ -z "$(VERSION)" ]; then \
		$(ECHO_ERROR) "VERSION is not set. Usage: make [deploy-uat|deploy] VERSION=1.2.3"; \
		exit 1; \
	fi

check-tools:
	@command -v aws >/dev/null 2>&1 || { $(ECHO_ERROR) "aws CLI not installed"; exit 1; }
	@command -v unzip >/dev/null 2>&1 || { $(ECHO_ERROR) "unzip not installed"; exit 1; }
	@command -v yarn >/dev/null 2>&1 || { $(ECHO_ERROR) "yarn not installed"; exit 1; }
	@command -v pm2 >/dev/null 2>&1 || { $(ECHO_ERROR) "pm2 not installed"; exit 1; }

clear:
	@$(ECHO_INFO) "Clearing deployment and destination folders"
	@rm -rf ~/deployment/*
	@if [ -n "$(DEST)" ] && [ "$(DEST)" != "/" ]; then \
		$(ECHO_INFO) "Cleaning contents of $(DEST)"; \
		rm -rf $(DEST)/* $(DEST)/.[!.]* $(DEST)/..?*; \
	else \
		$(ECHO_WARN) "Skipping DEST clean to avoid deleting current/root dir."; \
	fi

fetch:
	@$(ECHO_INFO) "Fetching $(FILE_NAME) from s3://$(BUCKET)..."
	@aws s3 cp s3://$(BUCKET)/$(FILE_PATH) $(LOCAL_ZIP) || { $(ECHO_ERROR) "Failed to fetch $(FILE_NAME) from S3. File may not exist."; exit 1; }

unzip:
	@$(ECHO_INFO) "Unzipping $(LOCAL_ZIP) to $(DEST)..."
	@unzip -o $(LOCAL_ZIP) -d $(DEST)

install:
	@$(ECHO_INFO) "Installing dependencies in $(DEST)"
	@yarn install

restart:
	@$(ECHO_INFO) "Restarting PM2 process: $(APP) (env: $(PM2_ENV))"
	@pm2 stop ecosystem.json
	@pm2 delete ecosystem.json
	@pm2 start ecosystem.json --env $(PM2_ENV)
	@$(ECHO_INFO) "Deployment complete"

clean:
	@$(ECHO_INFO) "Cleaning all local files"
	@rm -rf ~/deployment/*

# Targets
.PHONY: deploy deploy-uat fetch unzip clear install clean check-tools require-version restart
